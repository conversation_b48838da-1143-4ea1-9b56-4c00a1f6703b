09:43:15.704 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
09:43:15.846 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:43:16.964 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:43:16.964 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:43:18.496 [main] INFO  c.r.s.StorageApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
09:43:23.091 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9207"]
09:43:23.093 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:43:23.093 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.102]
09:43:23.208 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:43:24.251 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1,master} inited
09:43:24.253 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,158] - dynamic-datasource - add a datasource named [master] success
09:43:24.253 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,241] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:43:24.694 [main] INFO  c.r.s.c.RestTemplateConfig - [restTemplate,24] - 创建外部工具RestTemplate，连接超时: 300000ms, 读取超时: 600000ms
09:43:26.643 [main] INFO  c.r.s.c.MongoConfig - [mongoClient,67] - MongoDB使用无认证模式
09:43:26.816 [main] INFO  o.m.driver.client - [info,71] - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "4.6.1"}, "os": {"type": "Windows", "name": "Windows 10", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Oracle Corporation/17.0.14+8-LTS-191"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@31d11998]}, clusterSettings={hosts=[mongo.server:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=50000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
09:43:26.833 [cluster-rtt-ClusterId{value='688aca3e3ac81e18b77903a4', description='null'}-mongo.server:27017] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:2, serverValue:2}] to mongo.server:27017
09:43:26.833 [cluster-ClusterId{value='688aca3e3ac81e18b77903a4', description='null'}-mongo.server:27017] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:1, serverValue:1}] to mongo.server:27017
09:43:26.833 [cluster-ClusterId{value='688aca3e3ac81e18b77903a4', description='null'}-mongo.server:27017] INFO  o.m.driver.cluster - [info,71] - Monitor thread successfully connected to server with description ServerDescription{address=mongo.server:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=25, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=48171500}
09:43:26.837 [main] INFO  c.a.m.t.UrlJoint - [jointMongoUrl,165] - get connected：mongodb://mongo.server:27017/?ssl=false&connectTimeoutMS=50000
09:43:26.844 [main] INFO  o.m.driver.client - [info,71] - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "4.6.1"}, "os": {"type": "Windows", "name": "Windows 10", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Oracle Corporation/17.0.14+8-LTS-191"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, streamFactoryFactory=null, commandListeners=[com.anwen.mongo.listener.BaseListener@1e8fd198], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@31d11998]}, clusterSettings={hosts=[mongo.server:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=50000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=50000, readTimeoutMS=50000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
09:43:26.847 [cluster-rtt-ClusterId{value='688aca3e3ac81e18b77903a5', description='null'}-mongo.server:27017] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:3, serverValue:3}] to mongo.server:27017
09:43:26.847 [cluster-ClusterId{value='688aca3e3ac81e18b77903a5', description='null'}-mongo.server:27017] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:4, serverValue:4}] to mongo.server:27017
09:43:26.847 [cluster-ClusterId{value='688aca3e3ac81e18b77903a5', description='null'}-mongo.server:27017] INFO  o.m.driver.cluster - [info,71] - Monitor thread successfully connected to server with description ServerDescription{address=mongo.server:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=25, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=2245000}
09:43:35.214 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:43:40.573 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-1
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.746 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.747 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.747 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220743
09:43:40.750 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-1, groupId=storage] Subscribed to topic(s): rsync-es
09:43:40.768 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-2
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.778 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.778 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.779 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220778
09:43:40.779 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-2, groupId=storage] Subscribed to topic(s): rsync-es
09:43:40.781 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-3
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.791 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.792 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.792 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220791
09:43:40.793 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-3, groupId=storage] Subscribed to topic(s): rsync-es
09:43:40.795 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-4
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.801 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.802 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.802 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220801
09:43:40.803 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-4, groupId=storage] Subscribed to topic(s): parse-mapping1
09:43:40.805 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-5
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.812 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.812 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.813 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220812
09:43:40.813 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-5, groupId=storage] Subscribed to topic(s): parse-mapping1
09:43:40.815 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-6
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.821 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.822 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.822 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220821
09:43:40.823 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-6, groupId=storage] Subscribed to topic(s): parse-mapping1
09:43:40.825 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-7
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.831 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.831 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.832 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220831
09:43:40.832 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-7, groupId=storage] Subscribed to topic(s): single-journal-dedupcate-mapping
09:43:40.835 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-8
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.842 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.842 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.842 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220842
09:43:40.843 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-8, groupId=storage] Subscribed to topic(s): single-journal-dedupcate-mapping
09:43:40.845 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-9
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.851 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.851 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.852 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220851
09:43:40.852 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-9, groupId=storage] Subscribed to topic(s): single-journal-dedupcate-mapping
09:43:40.855 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-10
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.860 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.860 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.861 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220860
09:43:40.861 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-10, groupId=storage] Subscribed to topic(s): fusion-article-dedupcate-mapping
09:43:40.863 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-11
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.870 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.870 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.870 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220869
09:43:40.871 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-11, groupId=storage] Subscribed to topic(s): fusion-article-dedupcate-mapping
09:43:40.874 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-12
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.880 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.880 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.881 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220880
09:43:40.881 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-12, groupId=storage] Subscribed to topic(s): fusion-article-dedupcate-mapping
09:43:40.883 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-13
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.889 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.889 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.890 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220889
09:43:40.890 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-13, groupId=storage] Subscribed to topic(s): fusion-journal-dedupcate-mapping
09:43:40.892 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-14
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.897 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.898 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.898 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220897
09:43:40.898 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-14, groupId=storage] Subscribed to topic(s): fusion-journal-dedupcate-mapping
09:43:40.900 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-15
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.907 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.907 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.907 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220907
09:43:40.908 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-15, groupId=storage] Subscribed to topic(s): fusion-journal-dedupcate-mapping
09:43:40.910 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-16
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.916 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.916 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.917 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220916
09:43:40.917 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-16, groupId=storage] Subscribed to topic(s): rsync-es
09:43:40.919 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-17
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.925 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.925 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.926 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220925
09:43:40.926 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-17, groupId=storage] Subscribed to topic(s): rsync-es
09:43:40.928 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-18
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.933 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.933 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.934 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220933
09:43:40.934 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-18, groupId=storage] Subscribed to topic(s): rsync-es
09:43:40.936 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-19
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.941 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.942 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.942 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220941
09:43:40.942 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-19, groupId=storage] Subscribed to topic(s): fusion-mapping
09:43:40.944 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-20
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.949 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.949 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.949 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220949
09:43:40.949 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-20, groupId=storage] Subscribed to topic(s): fusion-mapping
09:43:40.952 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-21
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.958 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.958 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.958 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220958
09:43:40.958 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-21, groupId=storage] Subscribed to topic(s): fusion-mapping
09:43:40.960 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-22
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.966 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.966 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.966 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220966
09:43:40.967 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-22, groupId=storage] Subscribed to topic(s): single-article-dedupcate-mapping
09:43:40.969 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-23
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.975 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.975 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.975 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220975
09:43:40.976 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-23, groupId=storage] Subscribed to topic(s): single-article-dedupcate-mapping
09:43:40.978 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-24
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:43:40.984 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:43:40.985 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:43:40.985 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926220984
09:43:40.985 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-24, groupId=storage] Subscribed to topic(s): single-article-dedupcate-mapping
09:43:40.987 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9207"]
09:43:41.054 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:43:41.055 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:43:41.222 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP storage 10.4.111.182:9207 register finished
09:43:41.377 [main] INFO  c.r.s.StorageApplication - [logStarted,61] - Started StorageApplication in 26.824 seconds (JVM running for 28.289)
09:43:41.408 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,141] - [Nacos Config] Listening config: dataId=storage, group=DEFAULT_GROUP
09:43:41.409 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,141] - [Nacos Config] Listening config: dataId=storage.yml, group=DEFAULT_GROUP
09:43:41.816 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-14, groupId=storage] Resetting the last seen epoch of partition fusion-journal-dedupcate-mapping-0 to 0 since the associated topicId changed from null to WxO4uUfdR9e0VPm2jTL_Yg
09:43:41.816 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-22, groupId=storage] Resetting the last seen epoch of partition single-article-dedupcate-mapping-0 to 0 since the associated topicId changed from null to ibf_uSJURzOuhcEsfrQwpg
09:43:41.816 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-19, groupId=storage] Resetting the last seen epoch of partition fusion-mapping-0 to 0 since the associated topicId changed from null to WMGluhexQZ-Wr5klceoA1w
09:43:41.816 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-15, groupId=storage] Resetting the last seen epoch of partition fusion-journal-dedupcate-mapping-0 to 0 since the associated topicId changed from null to WxO4uUfdR9e0VPm2jTL_Yg
09:43:41.816 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-7, groupId=storage] Resetting the last seen epoch of partition single-journal-dedupcate-mapping-0 to 0 since the associated topicId changed from null to yyKozdmRQMKTbVA5UdfOgQ
09:43:41.816 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-13, groupId=storage] Resetting the last seen epoch of partition fusion-journal-dedupcate-mapping-0 to 0 since the associated topicId changed from null to WxO4uUfdR9e0VPm2jTL_Yg
09:43:41.816 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-9, groupId=storage] Resetting the last seen epoch of partition single-journal-dedupcate-mapping-0 to 0 since the associated topicId changed from null to yyKozdmRQMKTbVA5UdfOgQ
09:43:41.817 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-10, groupId=storage] Resetting the last seen epoch of partition fusion-article-dedupcate-mapping-0 to 0 since the associated topicId changed from null to K1JJzi6xTa--4ovI853QnA
09:43:41.816 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-6, groupId=storage] Resetting the last seen epoch of partition parse-mapping1-0 to 0 since the associated topicId changed from null to f1B-od9jRUaVbOT32QzoOA
09:43:41.817 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-11, groupId=storage] Resetting the last seen epoch of partition fusion-article-dedupcate-mapping-0 to 0 since the associated topicId changed from null to K1JJzi6xTa--4ovI853QnA
09:43:41.816 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-16, groupId=storage] Resetting the last seen epoch of partition rsync-es-0 to 16 since the associated topicId changed from null to cPNHXbgUQnScGipupRYp9A
09:43:41.816 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-18, groupId=storage] Resetting the last seen epoch of partition rsync-es-0 to 16 since the associated topicId changed from null to cPNHXbgUQnScGipupRYp9A
09:43:41.816 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-8, groupId=storage] Resetting the last seen epoch of partition single-journal-dedupcate-mapping-0 to 0 since the associated topicId changed from null to yyKozdmRQMKTbVA5UdfOgQ
09:43:41.816 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-1, groupId=storage] Resetting the last seen epoch of partition rsync-es-0 to 16 since the associated topicId changed from null to cPNHXbgUQnScGipupRYp9A
09:43:41.817 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-12, groupId=storage] Resetting the last seen epoch of partition fusion-article-dedupcate-mapping-0 to 0 since the associated topicId changed from null to K1JJzi6xTa--4ovI853QnA
09:43:41.817 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-20, groupId=storage] Resetting the last seen epoch of partition fusion-mapping-0 to 0 since the associated topicId changed from null to WMGluhexQZ-Wr5klceoA1w
09:43:41.817 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-23, groupId=storage] Resetting the last seen epoch of partition single-article-dedupcate-mapping-0 to 0 since the associated topicId changed from null to ibf_uSJURzOuhcEsfrQwpg
09:43:41.817 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-4, groupId=storage] Resetting the last seen epoch of partition parse-mapping1-0 to 0 since the associated topicId changed from null to f1B-od9jRUaVbOT32QzoOA
09:43:41.817 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-21, groupId=storage] Resetting the last seen epoch of partition fusion-mapping-0 to 0 since the associated topicId changed from null to WMGluhexQZ-Wr5klceoA1w
09:43:41.817 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-2, groupId=storage] Resetting the last seen epoch of partition rsync-es-0 to 16 since the associated topicId changed from null to cPNHXbgUQnScGipupRYp9A
09:43:41.817 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-5, groupId=storage] Resetting the last seen epoch of partition parse-mapping1-0 to 0 since the associated topicId changed from null to f1B-od9jRUaVbOT32QzoOA
09:43:41.817 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-3, groupId=storage] Resetting the last seen epoch of partition rsync-es-0 to 16 since the associated topicId changed from null to cPNHXbgUQnScGipupRYp9A
09:43:41.817 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-17, groupId=storage] Resetting the last seen epoch of partition rsync-es-0 to 16 since the associated topicId changed from null to cPNHXbgUQnScGipupRYp9A
09:43:41.817 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-24, groupId=storage] Resetting the last seen epoch of partition single-article-dedupcate-mapping-0 to 0 since the associated topicId changed from null to ibf_uSJURzOuhcEsfrQwpg
09:43:41.833 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-10, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.833 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-22, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-15, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-19, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-14, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.833 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-4, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.833 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-24, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-11, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-12, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-3, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-7, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-16, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-8, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-1, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-9, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-13, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-21, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-17, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-6, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-20, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-2, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-23, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-18, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.834 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-5, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-3, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-24, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-13, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-19, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-14, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-6, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-17, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-4, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-20, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-9, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-11, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-21, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-22, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-2, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-1, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-23, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-18, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-16, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-5, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-15, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-8, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-12, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-7, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.838 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-10, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:43:41.845 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-14, groupId=storage] (Re-)joining group
09:43:41.845 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-7, groupId=storage] (Re-)joining group
09:43:41.845 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-24, groupId=storage] (Re-)joining group
09:43:41.845 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-20, groupId=storage] (Re-)joining group
09:43:41.845 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-10, groupId=storage] (Re-)joining group
09:43:41.845 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-9, groupId=storage] (Re-)joining group
09:43:41.845 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-19, groupId=storage] (Re-)joining group
09:43:41.845 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-18, groupId=storage] (Re-)joining group
09:43:41.845 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-6, groupId=storage] (Re-)joining group
09:43:41.846 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-17, groupId=storage] (Re-)joining group
09:43:41.846 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-22, groupId=storage] (Re-)joining group
09:43:41.846 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-23, groupId=storage] (Re-)joining group
09:43:41.846 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-1, groupId=storage] (Re-)joining group
09:43:41.847 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-13, groupId=storage] (Re-)joining group
09:43:41.847 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-2, groupId=storage] (Re-)joining group
09:43:41.847 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-16, groupId=storage] (Re-)joining group
09:43:41.847 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-11, groupId=storage] (Re-)joining group
09:43:41.847 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-3, groupId=storage] (Re-)joining group
09:43:41.847 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-8, groupId=storage] (Re-)joining group
09:43:41.847 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-15, groupId=storage] (Re-)joining group
09:43:41.847 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-12, groupId=storage] (Re-)joining group
09:43:41.848 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-4, groupId=storage] (Re-)joining group
09:43:41.848 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-5, groupId=storage] (Re-)joining group
09:43:41.848 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-21, groupId=storage] (Re-)joining group
09:43:41.891 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-23, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.892 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-23, groupId=storage] (Re-)joining group
09:43:41.894 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-5, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.894 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-15, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.895 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-5, groupId=storage] (Re-)joining group
09:43:41.895 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-15, groupId=storage] (Re-)joining group
09:43:41.895 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-1, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.895 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-10, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.895 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-7, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.896 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-7, groupId=storage] (Re-)joining group
09:43:41.895 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-19, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.896 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-10, groupId=storage] (Re-)joining group
09:43:41.896 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-4, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.896 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-21, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.896 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-16, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.896 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-19, groupId=storage] (Re-)joining group
09:43:41.897 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-4, groupId=storage] (Re-)joining group
09:43:41.896 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-1, groupId=storage] (Re-)joining group
09:43:41.896 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-9, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.896 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-6, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.896 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-22, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.897 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-9, groupId=storage] (Re-)joining group
09:43:41.897 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-17, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.897 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-6, groupId=storage] (Re-)joining group
09:43:41.897 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-22, groupId=storage] (Re-)joining group
09:43:41.896 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-13, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.896 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-2, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.897 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-21, groupId=storage] (Re-)joining group
09:43:41.897 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-16, groupId=storage] (Re-)joining group
09:43:41.897 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-14, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.898 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-2, groupId=storage] (Re-)joining group
09:43:41.898 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-13, groupId=storage] (Re-)joining group
09:43:41.898 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-24, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.898 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-17, groupId=storage] (Re-)joining group
09:43:41.898 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-11, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.898 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-14, groupId=storage] (Re-)joining group
09:43:41.899 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-24, groupId=storage] (Re-)joining group
09:43:41.899 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-11, groupId=storage] (Re-)joining group
09:43:41.899 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-18, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.899 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-20, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.900 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-3, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.900 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-18, groupId=storage] (Re-)joining group
09:43:41.900 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-20, groupId=storage] (Re-)joining group
09:43:41.900 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-8, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.900 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-12, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:43:41.900 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-3, groupId=storage] (Re-)joining group
09:43:41.900 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-12, groupId=storage] (Re-)joining group
09:43:41.900 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-8, groupId=storage] (Re-)joining group
09:43:47.901 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-15, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-15-5444fe74-a6bd-4fd4-939f-fa78e3ada649', protocol='range'}
09:43:47.901 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-18, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-18-c9a63431-c948-4602-bed5-e8c8f6b08851', protocol='range'}
09:43:47.901 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-10, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-10-246cac3e-bd38-41db-bfa9-35ea43c3fce7', protocol='range'}
09:43:47.901 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-3, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-3-892d7937-8db6-496c-b5f7-6b9cbe245d41', protocol='range'}
09:43:47.901 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-6, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-6-4b4a1544-9469-456d-ba76-c195fd467e57', protocol='range'}
09:43:47.901 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-13, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-13-9495814d-dcb3-41e8-b8b7-59c48beed6e0', protocol='range'}
09:43:47.901 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-16, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-16-2abb4541-4dcf-4502-8e20-65afdb2b126b', protocol='range'}
09:43:47.902 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-8, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-8-e73c8de9-ee8a-4f41-8c57-7766e04cc658', protocol='range'}
09:43:47.902 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-23, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-23-89354af3-2cba-4256-b075-0d7440fabc73', protocol='range'}
09:43:47.902 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-24, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-24-c86b73d1-0004-4598-bb6b-eefd08774321', protocol='range'}
09:43:47.902 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-21, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-21-44e9344d-e620-4b90-a9f2-40c76234872e', protocol='range'}
09:43:47.902 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-5, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-5-2e8602ae-14ec-4a19-8236-29eb435b50e6', protocol='range'}
09:43:47.902 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-4, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-4-893e62c9-2aa1-4c3f-bb2d-ba51d794a7c0', protocol='range'}
09:43:47.902 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-1, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-1-56b4f9ad-530a-4e51-833e-12c12a945a36', protocol='range'}
09:43:47.902 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-19, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-19-1a88bebe-ffb8-4029-a801-ddd106687435', protocol='range'}
09:43:47.902 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-12, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-12-a6de1e20-ede4-4153-be4c-0915bda08649', protocol='range'}
09:43:47.902 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-17, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-17-6e61521b-3139-4152-ac23-12ec214443fc', protocol='range'}
09:43:47.902 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-2, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-2-877f2707-82f1-4645-b977-0730202991fe', protocol='range'}
09:43:47.902 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-14, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-14-439ae171-dc78-436f-91ee-3e360907d783', protocol='range'}
09:43:47.902 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-7, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-7-83c3bef9-aadb-4ad1-a387-872476cb44af', protocol='range'}
09:43:47.902 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-20, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-20-3429a6d5-3372-4ea4-87c0-e9a9f5606d2a', protocol='range'}
09:43:47.902 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-9, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-9-b1e5558b-3f8b-4bf1-b6a2-ef571b9d75fa', protocol='range'}
09:43:47.903 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-22, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-22-445298a4-3503-4f10-9ea9-770d34dad09c', protocol='range'}
09:43:47.904 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-11, groupId=storage] Successfully joined group with generation Generation{generationId=77, memberId='consumer-storage-11-4c66a985-5e55-4d75-9268-75c3c55dbc45', protocol='range'}
09:43:47.927 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-23, groupId=storage] Resetting the last seen epoch of partition fusion-journal-dedupcate-mapping-0 to 0 since the associated topicId changed from null to WxO4uUfdR9e0VPm2jTL_Yg
09:43:47.927 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-23, groupId=storage] Resetting the last seen epoch of partition fusion-mapping-0 to 0 since the associated topicId changed from null to WMGluhexQZ-Wr5klceoA1w
09:43:47.927 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-23, groupId=storage] Resetting the last seen epoch of partition fusion-article-dedupcate-mapping-0 to 0 since the associated topicId changed from null to K1JJzi6xTa--4ovI853QnA
09:43:47.927 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-23, groupId=storage] Resetting the last seen epoch of partition rsync-es-0 to 16 since the associated topicId changed from null to cPNHXbgUQnScGipupRYp9A
09:43:47.927 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-23, groupId=storage] Resetting the last seen epoch of partition single-journal-dedupcate-mapping-0 to 0 since the associated topicId changed from null to yyKozdmRQMKTbVA5UdfOgQ
09:43:47.927 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-23, groupId=storage] Resetting the last seen epoch of partition parse-mapping1-0 to 0 since the associated topicId changed from null to f1B-od9jRUaVbOT32QzoOA
09:43:47.930 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [performAssignment,659] - [Consumer clientId=consumer-storage-23, groupId=storage] Finished assignment for group at generation 77: {consumer-storage-13-9495814d-dcb3-41e8-b8b7-59c48beed6e0=Assignment(partitions=[fusion-journal-dedupcate-mapping-0]), consumer-storage-8-e73c8de9-ee8a-4f41-8c57-7766e04cc658=Assignment(partitions=[]), consumer-storage-12-a6de1e20-ede4-4153-be4c-0915bda08649=Assignment(partitions=[]), consumer-storage-7-83c3bef9-aadb-4ad1-a387-872476cb44af=Assignment(partitions=[single-journal-dedupcate-mapping-0]), consumer-storage-16-2abb4541-4dcf-4502-8e20-65afdb2b126b=Assignment(partitions=[]), consumer-storage-4-893e62c9-2aa1-4c3f-bb2d-ba51d794a7c0=Assignment(partitions=[parse-mapping1-0]), consumer-storage-5-2e8602ae-14ec-4a19-8236-29eb435b50e6=Assignment(partitions=[]), consumer-storage-10-246cac3e-bd38-41db-bfa9-35ea43c3fce7=Assignment(partitions=[fusion-article-dedupcate-mapping-0]), consumer-storage-17-6e61521b-3139-4152-ac23-12ec214443fc=Assignment(partitions=[]), consumer-storage-24-c86b73d1-0004-4598-bb6b-eefd08774321=Assignment(partitions=[]), consumer-storage-22-445298a4-3503-4f10-9ea9-770d34dad09c=Assignment(partitions=[single-article-dedupcate-mapping-0]), consumer-storage-11-4c66a985-5e55-4d75-9268-75c3c55dbc45=Assignment(partitions=[]), consumer-storage-15-5444fe74-a6bd-4fd4-939f-fa78e3ada649=Assignment(partitions=[]), consumer-storage-1-56b4f9ad-530a-4e51-833e-12c12a945a36=Assignment(partitions=[rsync-es-0]), consumer-storage-9-b1e5558b-3f8b-4bf1-b6a2-ef571b9d75fa=Assignment(partitions=[]), consumer-storage-14-439ae171-dc78-436f-91ee-3e360907d783=Assignment(partitions=[]), consumer-storage-20-3429a6d5-3372-4ea4-87c0-e9a9f5606d2a=Assignment(partitions=[]), consumer-storage-21-44e9344d-e620-4b90-a9f2-40c76234872e=Assignment(partitions=[]), consumer-storage-18-c9a63431-c948-4602-bed5-e8c8f6b08851=Assignment(partitions=[]), consumer-storage-19-1a88bebe-ffb8-4029-a801-ddd106687435=Assignment(partitions=[fusion-mapping-0]), consumer-storage-3-892d7937-8db6-496c-b5f7-6b9cbe245d41=Assignment(partitions=[]), consumer-storage-23-89354af3-2cba-4256-b075-0d7440fabc73=Assignment(partitions=[]), consumer-storage-6-4b4a1544-9469-456d-ba76-c195fd467e57=Assignment(partitions=[]), consumer-storage-2-877f2707-82f1-4645-b977-0730202991fe=Assignment(partitions=[])}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-10, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-10-246cac3e-bd38-41db-bfa9-35ea43c3fce7', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-13, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-13-9495814d-dcb3-41e8-b8b7-59c48beed6e0', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-16, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-16-2abb4541-4dcf-4502-8e20-65afdb2b126b', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-12, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-12-a6de1e20-ede4-4153-be4c-0915bda08649', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-3, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-3-892d7937-8db6-496c-b5f7-6b9cbe245d41', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-23, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-23-89354af3-2cba-4256-b075-0d7440fabc73', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-6, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-6-4b4a1544-9469-456d-ba76-c195fd467e57', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-19, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-19-1a88bebe-ffb8-4029-a801-ddd106687435', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-18, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-18-c9a63431-c948-4602-bed5-e8c8f6b08851', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-2, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-2-877f2707-82f1-4645-b977-0730202991fe', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-15, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-15-5444fe74-a6bd-4fd4-939f-fa78e3ada649', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-7, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-7-83c3bef9-aadb-4ad1-a387-872476cb44af', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-16, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-12, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-8, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-8-e73c8de9-ee8a-4f41-8c57-7766e04cc658', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-13, groupId=storage] Notifying assignor about the new Assignment(partitions=[fusion-journal-dedupcate-mapping-0])
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-10, groupId=storage] Notifying assignor about the new Assignment(partitions=[fusion-article-dedupcate-mapping-0])
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-6, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-1, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-1-56b4f9ad-530a-4e51-833e-12c12a945a36', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-3, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-14, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-14-439ae171-dc78-436f-91ee-3e360907d783', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-23, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-11, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-11-4c66a985-5e55-4d75-9268-75c3c55dbc45', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-19, groupId=storage] Notifying assignor about the new Assignment(partitions=[fusion-mapping-0])
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-18, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-1, groupId=storage] Notifying assignor about the new Assignment(partitions=[rsync-es-0])
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-3, groupId=storage] Adding newly assigned partitions: 
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-9, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-9-b1e5558b-3f8b-4bf1-b6a2-ef571b9d75fa', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-4, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-4-893e62c9-2aa1-4c3f-bb2d-ba51d794a7c0', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-2, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-9, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-22, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-22-445298a4-3503-4f10-9ea9-770d34dad09c', protocol='range'}
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-4, groupId=storage] Notifying assignor about the new Assignment(partitions=[parse-mapping1-0])
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-5, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-5-2e8602ae-14ec-4a19-8236-29eb435b50e6', protocol='range'}
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-15, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-7, groupId=storage] Notifying assignor about the new Assignment(partitions=[single-journal-dedupcate-mapping-0])
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-12, groupId=storage] Adding newly assigned partitions: 
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-22, groupId=storage] Notifying assignor about the new Assignment(partitions=[single-article-dedupcate-mapping-0])
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-17, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-17-6e61521b-3139-4152-ac23-12ec214443fc', protocol='range'}
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-15, groupId=storage] Adding newly assigned partitions: 
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-16, groupId=storage] Adding newly assigned partitions: 
09:43:47.946 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-8, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.948 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-17, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-6, groupId=storage] Adding newly assigned partitions: 
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-20, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-20-3429a6d5-3372-4ea4-87c0-e9a9f5606d2a', protocol='range'}
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-14, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-24, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-24-c86b73d1-0004-4598-bb6b-eefd08774321', protocol='range'}
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-23, groupId=storage] Adding newly assigned partitions: 
09:43:47.948 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-17, groupId=storage] Adding newly assigned partitions: 
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-11, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-18, groupId=storage] Adding newly assigned partitions: 
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-21, groupId=storage] Successfully synced group in generation Generation{generationId=77, memberId='consumer-storage-21-44e9344d-e620-4b90-a9f2-40c76234872e', protocol='range'}
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-2, groupId=storage] Adding newly assigned partitions: 
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-9, groupId=storage] Adding newly assigned partitions: 
09:43:47.947 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-5, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.948 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-8, groupId=storage] Adding newly assigned partitions: 
09:43:47.948 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-20, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.948 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-14, groupId=storage] Adding newly assigned partitions: 
09:43:47.948 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-24, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.948 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-11, groupId=storage] Adding newly assigned partitions: 
09:43:47.949 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-21, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:43:47.949 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-5, groupId=storage] Adding newly assigned partitions: 
09:43:47.949 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-20, groupId=storage] Adding newly assigned partitions: 
09:43:47.949 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-24, groupId=storage] Adding newly assigned partitions: 
09:43:47.949 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-21, groupId=storage] Adding newly assigned partitions: 
09:43:47.959 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-22, groupId=storage] Adding newly assigned partitions: single-article-dedupcate-mapping-0
09:43:47.959 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-10, groupId=storage] Adding newly assigned partitions: fusion-article-dedupcate-mapping-0
09:43:47.959 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-13, groupId=storage] Adding newly assigned partitions: fusion-journal-dedupcate-mapping-0
09:43:47.959 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-4, groupId=storage] Adding newly assigned partitions: parse-mapping1-0
09:43:47.959 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-7, groupId=storage] Adding newly assigned partitions: single-journal-dedupcate-mapping-0
09:43:47.959 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-19, groupId=storage] Adding newly assigned partitions: fusion-mapping-0
09:43:47.959 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-1, groupId=storage] Adding newly assigned partitions: rsync-es-0
09:43:47.976 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,1405] - [Consumer clientId=consumer-storage-1, groupId=storage] Found no committed offset for partition rsync-es-0
09:43:47.976 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,1405] - [Consumer clientId=consumer-storage-22, groupId=storage] Found no committed offset for partition single-article-dedupcate-mapping-0
09:43:47.976 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,1405] - [Consumer clientId=consumer-storage-10, groupId=storage] Found no committed offset for partition fusion-article-dedupcate-mapping-0
09:43:47.976 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,1405] - [Consumer clientId=consumer-storage-7, groupId=storage] Found no committed offset for partition single-journal-dedupcate-mapping-0
09:43:47.976 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,1405] - [Consumer clientId=consumer-storage-13, groupId=storage] Found no committed offset for partition fusion-journal-dedupcate-mapping-0
09:43:47.976 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,1405] - [Consumer clientId=consumer-storage-19, groupId=storage] Found no committed offset for partition fusion-mapping-0
09:43:47.981 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [refreshCommittedOffsetsIfNeeded,851] - [Consumer clientId=consumer-storage-4, groupId=storage] Setting offset for partition parse-mapping1-0 to the committed offset FetchPosition{offset=9, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=0}}
09:43:48.013 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.SubscriptionState - [maybeSeekUnvalidated,398] - [Consumer clientId=consumer-storage-10, groupId=storage] Resetting offset for partition fusion-article-dedupcate-mapping-0 to position FetchPosition{offset=0, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=0}}.
09:43:48.013 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.SubscriptionState - [maybeSeekUnvalidated,398] - [Consumer clientId=consumer-storage-19, groupId=storage] Resetting offset for partition fusion-mapping-0 to position FetchPosition{offset=0, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=0}}.
09:43:48.013 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.SubscriptionState - [maybeSeekUnvalidated,398] - [Consumer clientId=consumer-storage-7, groupId=storage] Resetting offset for partition single-journal-dedupcate-mapping-0 to position FetchPosition{offset=0, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=0}}.
09:43:48.014 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.SubscriptionState - [maybeSeekUnvalidated,398] - [Consumer clientId=consumer-storage-1, groupId=storage] Resetting offset for partition rsync-es-0 to position FetchPosition{offset=0, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=16}}.
09:43:48.014 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.SubscriptionState - [maybeSeekUnvalidated,398] - [Consumer clientId=consumer-storage-13, groupId=storage] Resetting offset for partition fusion-journal-dedupcate-mapping-0 to position FetchPosition{offset=0, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=0}}.
09:43:48.014 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.SubscriptionState - [maybeSeekUnvalidated,398] - [Consumer clientId=consumer-storage-22, groupId=storage] Resetting offset for partition single-article-dedupcate-mapping-0 to position FetchPosition{offset=0, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=0}}.
09:46:31.195 [http-nio-9207-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:48:12.830 [http-nio-9207-exec-7] INFO  c.r.s.p.s.i.RuleBaseServiceImpl - [addRuleBase,89] - 新增解析规则数据:AddRuleBaseReqVO(name=3453242424, sourceId=06, docType=J, ruleType=ANALYSIS, useStatus=N, ruleStatus=EXECUTING, toolStatus=DATA_SENT, description=333, sampleFile=/test/storage/rule/parsingmapping/pubmed测试数据.zip, phase=null, fileList=[ExFileVO(name=pubmed测试数据.zip, url=/test/storage/rule/parsingmapping/pubmed测试数据.zip, md5=4bff50944455bcf92daa2f8a285b3f96, size=2965459)])
09:48:12.871 [http-nio-9207-exec-7] INFO  c.r.s.p.s.i.RuleBaseServiceImpl - [addRuleBase,113] - 根据规则文件进行工具接口调用，解析映射规则：3453242424 
09:48:12.875 [pool-7-thread-1] INFO  c.r.s.a.s.AnalysisService - [toolInterfaceCall,62] - 解析工具开始调用 ==> 线程名称：pool-7-thread-1
09:48:18.779 [pool-7-thread-1] INFO  c.r.s.u.RestUtils - [analysis,86] - 请求参数: url=http://***********:20215/tool_1_get_rules_and_result, body={files=[file [d:\home\library2020\tempDir\f9d1179a-2e83-4d55-a91f-22e3adf59180\pubmed测试数据.zip]], rules=[[]], checks=[[FileChecks(fileName=pubmed测试数据.zip, md5=4bff50944455bcf92daa2f8a285b3f96)]], info=[RuleInfo(ruleId=128, sourceId=J, dataType=null, docType=J, sourceType=PubmedArticle)]}
09:48:40.234 [pool-7-thread-1] INFO  c.r.s.a.s.AnalysisService - [updateAfterSuccess,195] - 工具调用成功：null
09:48:40.344 [pool-7-thread-1] INFO  c.r.s.a.s.AnalysisService - [updateAfterSuccess,216] - 工具返回数据转换异常：结果字符串不能为空
09:48:40.344 [pool-7-thread-1] INFO  c.r.s.a.s.AnalysisService - [updateAfterFailure,175] - 工具调用失败：结果字符串不能为空
09:48:41.362 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-13, groupId=storage] Node -1 disconnected.
09:48:41.582 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-21, groupId=storage] Node -1 disconnected.
09:48:41.774 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-10, groupId=storage] Node -1 disconnected.
09:48:41.795 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-1, groupId=storage] Node -1 disconnected.
09:48:41.958 [kafka-coordinator-heartbeat-thread | storage] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-18, groupId=storage] Node -1 disconnected.
09:48:42.028 [kafka-coordinator-heartbeat-thread | storage] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-2, groupId=storage] Node -1 disconnected.
09:48:42.028 [kafka-coordinator-heartbeat-thread | storage] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-14, groupId=storage] Node -1 disconnected.
09:48:42.028 [kafka-coordinator-heartbeat-thread | storage] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-8, groupId=storage] Node -1 disconnected.
09:48:42.028 [kafka-coordinator-heartbeat-thread | storage] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-7, groupId=storage] Node -1 disconnected.
09:48:42.210 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-4, groupId=storage] Node -1 disconnected.
09:48:42.334 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-22, groupId=storage] Node -1 disconnected.
09:48:42.571 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-19, groupId=storage] Node -1 disconnected.
09:48:43.112 [kafka-coordinator-heartbeat-thread | storage] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-23, groupId=storage] Node -1 disconnected.
09:48:43.470 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-3, groupId=storage] Node -1 disconnected.
09:48:43.766 [kafka-coordinator-heartbeat-thread | storage] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-11, groupId=storage] Node -1 disconnected.
09:48:44.050 [kafka-coordinator-heartbeat-thread | storage] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-12, groupId=storage] Node -1 disconnected.
09:48:44.501 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-16, groupId=storage] Node -1 disconnected.
09:48:44.554 [kafka-coordinator-heartbeat-thread | storage] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-15, groupId=storage] Node -1 disconnected.
09:48:45.241 [kafka-coordinator-heartbeat-thread | storage] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-20, groupId=storage] Node -1 disconnected.
09:48:45.301 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-5, groupId=storage] Node -1 disconnected.
09:48:45.623 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-9, groupId=storage] Node -1 disconnected.
09:48:45.782 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-24, groupId=storage] Node -1 disconnected.
09:48:46.368 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-17, groupId=storage] Node -1 disconnected.
09:48:47.195 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.NetworkClient - [handleDisconnections,935] - [Consumer clientId=consumer-storage-6, groupId=storage] Node -1 disconnected.
09:51:55.780 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsRevoked,311] - [Consumer clientId=consumer-storage-1, groupId=storage] Revoke previously assigned partitions rsync-es-0
09:51:55.780 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-2, groupId=storage] Member consumer-storage-2-877f2707-82f1-4645-b977-0730202991fe sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.780 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-3, groupId=storage] Member consumer-storage-3-892d7937-8db6-496c-b5f7-6b9cbe245d41 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.781 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsRevoked,311] - [Consumer clientId=consumer-storage-4, groupId=storage] Revoke previously assigned partitions parse-mapping1-0
09:51:55.781 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-6, groupId=storage] Member consumer-storage-6-4b4a1544-9469-456d-ba76-c195fd467e57 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.791 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-5, groupId=storage] Member consumer-storage-5-2e8602ae-14ec-4a19-8236-29eb435b50e6 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.791 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-3, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.791 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-2, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.791 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-6, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.792 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-5, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.793 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-3, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.793 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-2, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.793 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-6, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.793 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsRevoked,311] - [Consumer clientId=consumer-storage-7, groupId=storage] Revoke previously assigned partitions single-journal-dedupcate-mapping-0
09:51:55.802 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-5, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.802 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-3, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.802 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-2, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.803 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-8, groupId=storage] Member consumer-storage-8-e73c8de9-ee8a-4f41-8c57-7766e04cc658 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.803 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-6, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.803 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-1, groupId=storage] Member consumer-storage-1-56b4f9ad-530a-4e51-833e-12c12a945a36 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.803 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-7, groupId=storage] Member consumer-storage-7-83c3bef9-aadb-4ad1-a387-872476cb44af sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.803 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-9, groupId=storage] Member consumer-storage-9-b1e5558b-3f8b-4bf1-b6a2-ef571b9d75fa sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.804 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-5, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.803 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-16, groupId=storage] Member consumer-storage-16-2abb4541-4dcf-4502-8e20-65afdb2b126b sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.805 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-8, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.806 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-2, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.806 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-1, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.806 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-6, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.806 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-3, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.806 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-7, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.806 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-11, groupId=storage] Member consumer-storage-11-4c66a985-5e55-4d75-9268-75c3c55dbc45 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.806 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-4, groupId=storage] Member consumer-storage-4-893e62c9-2aa1-4c3f-bb2d-ba51d794a7c0 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.806 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsRevoked,311] - [Consumer clientId=consumer-storage-10, groupId=storage] Revoke previously assigned partitions fusion-article-dedupcate-mapping-0
09:51:55.808 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-12, groupId=storage] Member consumer-storage-12-a6de1e20-ede4-4153-be4c-0915bda08649 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.808 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsRevoked,311] - [Consumer clientId=consumer-storage-13, groupId=storage] Revoke previously assigned partitions fusion-journal-dedupcate-mapping-0
09:51:55.808 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-9, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.811 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-5, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.811 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-15, groupId=storage] Member consumer-storage-15-5444fe74-a6bd-4fd4-939f-fa78e3ada649 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.811 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-14, groupId=storage] Member consumer-storage-14-439ae171-dc78-436f-91ee-3e360907d783 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.811 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-16, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.812 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-2, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.812 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-8, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.812 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-3, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.812 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-17, groupId=storage] Member consumer-storage-17-6e61521b-3139-4152-ac23-12ec214443fc sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.812 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsRevoked,311] - [Consumer clientId=consumer-storage-19, groupId=storage] Revoke previously assigned partitions fusion-mapping-0
09:51:55.813 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-7, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.812 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-1, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.812 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-6, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.813 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-18, groupId=storage] Member consumer-storage-18-c9a63431-c948-4602-bed5-e8c8f6b08851 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.813 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-9, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.813 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-10, groupId=storage] Member consumer-storage-10-246cac3e-bd38-41db-bfa9-35ea43c3fce7 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.813 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-24, groupId=storage] Member consumer-storage-24-c86b73d1-0004-4598-bb6b-eefd08774321 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.813 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-4, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.813 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-11, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.813 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-12, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.813 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-13, groupId=storage] Member consumer-storage-13-9495814d-dcb3-41e8-b8b7-59c48beed6e0 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.814 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-15, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.813 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-23, groupId=storage] Member consumer-storage-23-89354af3-2cba-4256-b075-0d7440fabc73 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.813 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-20, groupId=storage] Member consumer-storage-20-3429a6d5-3372-4ea4-87c0-e9a9f5606d2a sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.814 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-5, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.814 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-14, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.814 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsRevoked,311] - [Consumer clientId=consumer-storage-22, groupId=storage] Revoke previously assigned partitions single-article-dedupcate-mapping-0
09:51:55.814 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-21, groupId=storage] Member consumer-storage-21-44e9344d-e620-4b90-a9f2-40c76234872e sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.815 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-8, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.815 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-17, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.814 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-16, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.815 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-7, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.815 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-19, groupId=storage] Member consumer-storage-19-1a88bebe-ffb8-4029-a801-ddd106687435 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.815 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-1, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.815 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-4, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.815 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-9, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.815 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-18, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.815 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-10, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.815 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-11, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.816 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-12, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.815 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-24, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.816 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-13, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.816 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-15, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.816 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-14, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.816 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-23, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.816 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-20, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.816 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-22, groupId=storage] Member consumer-storage-22-445298a4-3503-4f10-9ea9-770d34dad09c sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:51:55.817 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-17, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.817 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-19, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.817 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-4, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.817 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-7, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.817 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-18, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.818 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-24, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.817 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-16, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.817 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-21, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.817 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-11, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.818 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-20, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.817 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-10, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.817 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-8, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.818 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-12, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.818 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-15, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.824 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-22, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.824 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-17, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.818 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-1, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.818 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-13, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.818 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-9, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.818 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-14, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.818 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-23, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.825 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-19, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.825 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-7, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.825 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-18, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.825 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-24, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.825 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-21, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.825 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-4, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.826 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-10, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.826 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-20, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.826 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-8, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.826 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-13, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.826 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-16, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.826 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-11, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.826 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-12, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.826 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-22, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.826 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-15, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.826 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-17, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.827 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-23, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.826 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-9, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.826 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-1, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.827 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-14, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.828 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-19, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.828 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.828 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.833 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.833 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.833 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-24, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.834 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-21, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.834 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-4, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.834 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-13, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.835 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-12, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.835 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-11, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.834 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-20, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.835 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-22, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:51:55.835 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-17, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.835 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-15, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.835 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-14, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.835 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-16, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.835 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-23, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.836 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-19, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.836 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.836 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-18, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.836 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.836 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-24, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.836 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.836 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.841 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-10, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.841 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-13, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.841 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-21, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.841 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-20, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.842 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-22, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:51:55.842 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-23, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.842 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.843 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-19, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.854 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.843 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.853 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-18, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.855 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.856 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.857 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-10, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.857 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.863 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-21, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.863 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.864 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.880 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.880 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.881 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.880 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.879 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.903 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.888 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.885 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.881 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-22, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:51:55.908 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.882 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.910 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.911 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.923 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.925 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.926 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.926 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.933 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.933 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.933 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.933 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.945 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.945 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.945 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.945 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.933 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.945 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.944 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.945 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.946 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.946 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.947 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.948 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.948 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.948 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.948 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.948 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.949 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.949 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.949 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.949 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.949 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.949 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.950 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.949 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.950 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.949 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.950 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.950 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.959 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.958 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.959 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.960 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:51:55.968 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.968 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:55.969 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:51:55.980 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:51:56.229 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-1 unregistered
09:51:56.329 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-5 unregistered
09:51:56.508 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-2 unregistered
09:51:56.528 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-7 unregistered
09:51:56.531 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-20 unregistered
09:51:56.535 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-14 unregistered
09:51:56.538 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-8 unregistered
09:51:56.550 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-22 unregistered
09:51:56.551 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-3 unregistered
09:51:56.551 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-17 unregistered
09:51:56.551 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-21 unregistered
09:51:56.552 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-13 unregistered
09:51:56.552 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-9 unregistered
09:51:56.554 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-11 unregistered
09:51:56.555 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-24 unregistered
09:51:56.555 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-12 unregistered
09:51:56.556 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-18 unregistered
09:51:56.557 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-23 unregistered
09:51:56.557 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-19 unregistered
09:51:56.558 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-15 unregistered
09:51:56.559 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-6 unregistered
09:51:56.560 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-4 unregistered
09:51:56.560 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-10 unregistered
09:51:56.561 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-16 unregistered
09:51:56.597 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
09:51:56.739 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
09:51:57.051 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource start closing ....
09:51:57.062 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
09:51:57.077 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
09:51:57.077 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - [destroy,98] - dynamic-datasource close the datasource named [master] success,
09:51:57.078 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,219] - dynamic-datasource all closed success,bye
09:52:14.088 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
09:52:14.266 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:52:15.678 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:52:15.679 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:52:17.344 [main] INFO  c.r.s.StorageApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
09:52:22.207 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9207"]
09:52:22.209 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:52:22.210 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.102]
09:52:22.392 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:52:23.605 [main] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1,master} inited
09:52:23.606 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,158] - dynamic-datasource - add a datasource named [master] success
09:52:23.607 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,241] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:52:23.986 [main] INFO  c.r.s.c.RestTemplateConfig - [restTemplate,24] - 创建外部工具RestTemplate，连接超时: 300000ms, 读取超时: 600000ms
09:52:27.135 [main] INFO  c.r.s.c.MongoConfig - [mongoClient,67] - MongoDB使用无认证模式
09:52:27.378 [main] INFO  o.m.driver.client - [info,71] - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "4.6.1"}, "os": {"type": "Windows", "name": "Windows 10", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Oracle Corporation/17.0.14+8-LTS-191"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, streamFactoryFactory=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@21dd405a]}, clusterSettings={hosts=[mongo.server:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=50000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
09:52:27.384 [cluster-rtt-ClusterId{value='688acc5b1a168b782c018cc2', description='null'}-mongo.server:27017] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:2, serverValue:6}] to mongo.server:27017
09:52:27.385 [cluster-ClusterId{value='688acc5b1a168b782c018cc2', description='null'}-mongo.server:27017] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:1, serverValue:5}] to mongo.server:27017
09:52:27.385 [cluster-ClusterId{value='688acc5b1a168b782c018cc2', description='null'}-mongo.server:27017] INFO  o.m.driver.cluster - [info,71] - Monitor thread successfully connected to server with description ServerDescription{address=mongo.server:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=25, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=55037700}
09:52:27.405 [main] INFO  c.a.m.t.UrlJoint - [jointMongoUrl,165] - get connected：mongodb://mongo.server:27017/?ssl=false&connectTimeoutMS=50000
09:52:27.412 [main] INFO  o.m.driver.client - [info,71] - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "4.6.1"}, "os": {"type": "Windows", "name": "Windows 10", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Oracle Corporation/17.0.14+8-LTS-191"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, streamFactoryFactory=null, commandListeners=[com.anwen.mongo.listener.BaseListener@41fbe8c0], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.Jep395RecordCodecProvider@21dd405a]}, clusterSettings={hosts=[mongo.server:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='30000 ms'}, socketSettings=SocketSettings{connectTimeoutMS=50000, readTimeoutMS=0, receiveBufferSize=0, sendBufferSize=0}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=50000, readTimeoutMS=50000, receiveBufferSize=0, sendBufferSize=0}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, contextProvider=null}
09:52:27.415 [cluster-ClusterId{value='688acc5b1a168b782c018cc3', description='null'}-mongo.server:27017] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:3, serverValue:7}] to mongo.server:27017
09:52:27.415 [cluster-rtt-ClusterId{value='688acc5b1a168b782c018cc3', description='null'}-mongo.server:27017] INFO  o.m.d.connection - [info,71] - Opened connection [connectionId{localValue:4, serverValue:8}] to mongo.server:27017
09:52:27.415 [cluster-ClusterId{value='688acc5b1a168b782c018cc3', description='null'}-mongo.server:27017] INFO  o.m.driver.cluster - [info,71] - Monitor thread successfully connected to server with description ServerDescription{address=mongo.server:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=25, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=2227200}
09:52:40.128 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:52:48.782 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-1
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.038 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.038 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.039 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769032
09:52:49.048 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-1, groupId=storage] Subscribed to topic(s): parse-mapping1
09:52:49.088 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-2
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.103 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.103 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.103 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769102
09:52:49.105 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-2, groupId=storage] Subscribed to topic(s): parse-mapping1
09:52:49.112 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-3
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.127 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.128 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.128 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769127
09:52:49.129 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-3, groupId=storage] Subscribed to topic(s): parse-mapping1
09:52:49.133 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-4
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.144 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.144 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.145 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769144
09:52:49.146 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-4, groupId=storage] Subscribed to topic(s): rsync-es
09:52:49.149 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-5
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.161 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.161 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.162 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769160
09:52:49.163 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-5, groupId=storage] Subscribed to topic(s): rsync-es
09:52:49.167 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-6
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.175 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.176 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.176 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769175
09:52:49.177 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-6, groupId=storage] Subscribed to topic(s): rsync-es
09:52:49.180 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-7
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.188 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.188 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.188 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769188
09:52:49.189 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-7, groupId=storage] Subscribed to topic(s): fusion-journal-dedupcate-mapping
09:52:49.193 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-8
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.203 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.204 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.204 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769203
09:52:49.205 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-8, groupId=storage] Subscribed to topic(s): fusion-journal-dedupcate-mapping
09:52:49.208 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-9
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.218 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.218 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.221 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769218
09:52:49.222 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-9, groupId=storage] Subscribed to topic(s): fusion-journal-dedupcate-mapping
09:52:49.226 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-10
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.233 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.234 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.234 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769233
09:52:49.235 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-10, groupId=storage] Subscribed to topic(s): single-article-dedupcate-mapping
09:52:49.238 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-11
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.247 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.247 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.248 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769247
09:52:49.248 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-11, groupId=storage] Subscribed to topic(s): single-article-dedupcate-mapping
09:52:49.251 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-12
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.260 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.261 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.261 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769260
09:52:49.261 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-12, groupId=storage] Subscribed to topic(s): single-article-dedupcate-mapping
09:52:49.265 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-13
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.274 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.275 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.275 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769274
09:52:49.276 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-13, groupId=storage] Subscribed to topic(s): fusion-article-dedupcate-mapping
09:52:49.283 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-14
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.292 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.292 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.293 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769292
09:52:49.293 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-14, groupId=storage] Subscribed to topic(s): fusion-article-dedupcate-mapping
09:52:49.296 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-15
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.304 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.304 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.304 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769303
09:52:49.305 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-15, groupId=storage] Subscribed to topic(s): fusion-article-dedupcate-mapping
09:52:49.309 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-16
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.321 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.322 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.322 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769321
09:52:49.323 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-16, groupId=storage] Subscribed to topic(s): logs
09:52:49.330 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-17
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.365 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.368 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.369 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769364
09:52:49.373 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-17, groupId=storage] Subscribed to topic(s): logs
09:52:49.383 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-18
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.398 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.398 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.399 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769398
09:52:49.400 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-18, groupId=storage] Subscribed to topic(s): logs
09:52:49.404 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-19
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.414 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.415 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.415 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769414
09:52:49.416 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-19, groupId=storage] Subscribed to topic(s): fusion-mapping
09:52:49.419 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-20
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.428 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.428 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.428 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769428
09:52:49.429 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-20, groupId=storage] Subscribed to topic(s): fusion-mapping
09:52:49.434 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-21
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.444 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.444 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.444 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769443
09:52:49.445 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-21, groupId=storage] Subscribed to topic(s): fusion-mapping
09:52:49.450 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-22
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.463 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.463 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.464 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769463
09:52:49.464 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-22, groupId=storage] Subscribed to topic(s): single-journal-dedupcate-mapping
09:52:49.468 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-23
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.476 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.477 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.477 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769476
09:52:49.477 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-23, groupId=storage] Subscribed to topic(s): single-journal-dedupcate-mapping
09:52:49.480 [main] INFO  o.a.k.c.c.ConsumerConfig - [logAll,376] - ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.offset.reset = earliest
	bootstrap.servers = [kafka.server:9092]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-storage-24
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = storage
	group.instance.id = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_uncommitted
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 500
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.springframework.kafka.support.serializer.JsonDeserializer

09:52:49.488 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,119] - Kafka version: 3.1.2
09:52:49.488 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,120] - Kafka commitId: f8c67dc3ae0a3265
09:52:49.488 [main] INFO  o.a.k.c.u.AppInfoParser - [<init>,121] - Kafka startTimeMs: 1753926769488
09:52:49.489 [main] INFO  o.a.k.c.c.KafkaConsumer - [subscribe,966] - [Consumer clientId=consumer-storage-24, groupId=storage] Subscribed to topic(s): single-journal-dedupcate-mapping
09:52:49.492 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9207"]
09:52:49.569 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:52:49.569 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:52:49.756 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP storage 10.4.111.182:9207 register finished
09:52:50.051 [main] INFO  c.r.s.StorageApplication - [logStarted,61] - Started StorageApplication in 37.516 seconds (JVM running for 40.554)
09:52:50.105 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,141] - [Nacos Config] Listening config: dataId=storage, group=DEFAULT_GROUP
09:52:50.107 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,141] - [Nacos Config] Listening config: dataId=storage.yml, group=DEFAULT_GROUP
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-9, groupId=storage] Resetting the last seen epoch of partition fusion-journal-dedupcate-mapping-0 to 0 since the associated topicId changed from null to WxO4uUfdR9e0VPm2jTL_Yg
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-19, groupId=storage] Resetting the last seen epoch of partition fusion-mapping-0 to 0 since the associated topicId changed from null to WMGluhexQZ-Wr5klceoA1w
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-15, groupId=storage] Resetting the last seen epoch of partition fusion-article-dedupcate-mapping-0 to 0 since the associated topicId changed from null to K1JJzi6xTa--4ovI853QnA
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-10, groupId=storage] Resetting the last seen epoch of partition single-article-dedupcate-mapping-0 to 0 since the associated topicId changed from null to ibf_uSJURzOuhcEsfrQwpg
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-6, groupId=storage] Resetting the last seen epoch of partition rsync-es-0 to 16 since the associated topicId changed from null to cPNHXbgUQnScGipupRYp9A
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-2, groupId=storage] Resetting the last seen epoch of partition parse-mapping1-0 to 0 since the associated topicId changed from null to f1B-od9jRUaVbOT32QzoOA
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-14, groupId=storage] Resetting the last seen epoch of partition fusion-article-dedupcate-mapping-0 to 0 since the associated topicId changed from null to K1JJzi6xTa--4ovI853QnA
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-20, groupId=storage] Resetting the last seen epoch of partition fusion-mapping-0 to 0 since the associated topicId changed from null to WMGluhexQZ-Wr5klceoA1w
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-24, groupId=storage] Resetting the last seen epoch of partition single-journal-dedupcate-mapping-0 to 0 since the associated topicId changed from null to yyKozdmRQMKTbVA5UdfOgQ
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-7, groupId=storage] Resetting the last seen epoch of partition fusion-journal-dedupcate-mapping-0 to 0 since the associated topicId changed from null to WxO4uUfdR9e0VPm2jTL_Yg
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-13, groupId=storage] Resetting the last seen epoch of partition fusion-article-dedupcate-mapping-0 to 0 since the associated topicId changed from null to K1JJzi6xTa--4ovI853QnA
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-11, groupId=storage] Resetting the last seen epoch of partition single-article-dedupcate-mapping-0 to 0 since the associated topicId changed from null to ibf_uSJURzOuhcEsfrQwpg
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-23, groupId=storage] Resetting the last seen epoch of partition single-journal-dedupcate-mapping-0 to 0 since the associated topicId changed from null to yyKozdmRQMKTbVA5UdfOgQ
09:52:50.535 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-3, groupId=storage] Resetting the last seen epoch of partition parse-mapping1-0 to 0 since the associated topicId changed from null to f1B-od9jRUaVbOT32QzoOA
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-1, groupId=storage] Resetting the last seen epoch of partition parse-mapping1-0 to 0 since the associated topicId changed from null to f1B-od9jRUaVbOT32QzoOA
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-21, groupId=storage] Resetting the last seen epoch of partition fusion-mapping-0 to 0 since the associated topicId changed from null to WMGluhexQZ-Wr5klceoA1w
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-8, groupId=storage] Resetting the last seen epoch of partition fusion-journal-dedupcate-mapping-0 to 0 since the associated topicId changed from null to WxO4uUfdR9e0VPm2jTL_Yg
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-22, groupId=storage] Resetting the last seen epoch of partition single-journal-dedupcate-mapping-0 to 0 since the associated topicId changed from null to yyKozdmRQMKTbVA5UdfOgQ
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-5, groupId=storage] Resetting the last seen epoch of partition rsync-es-0 to 16 since the associated topicId changed from null to cPNHXbgUQnScGipupRYp9A
09:52:50.535 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-4, groupId=storage] Resetting the last seen epoch of partition rsync-es-0 to 16 since the associated topicId changed from null to cPNHXbgUQnScGipupRYp9A
09:52:50.534 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-12, groupId=storage] Resetting the last seen epoch of partition single-article-dedupcate-mapping-0 to 0 since the associated topicId changed from null to ibf_uSJURzOuhcEsfrQwpg
09:52:50.540 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-16, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.540 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-18, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.540 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-17, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.546 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-15, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.546 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-16, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.546 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-13, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.546 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-17, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-24, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-7, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-9, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-5, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-8, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-4, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-20, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-23, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-2, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-11, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-14, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.548 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-13, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.548 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-15, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-1, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-6, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-10, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-22, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-19, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.546 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-18, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-21, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-12, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.549 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-9, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.547 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.Metadata - [update,287] - [Consumer clientId=consumer-storage-3, groupId=storage] Cluster ID: eUPWhlw-S5Spt3JilLWiEQ
09:52:50.548 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-24, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.549 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-8, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.548 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-7, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.549 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-5, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.549 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-2, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.549 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-4, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.549 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-14, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.549 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-11, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.549 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-6, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.550 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-22, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.549 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-20, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.549 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-23, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.549 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-1, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.550 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-10, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.550 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-19, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.550 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-21, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.550 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-3, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.550 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [onSuccess,853] - [Consumer clientId=consumer-storage-12, groupId=storage] Discovered group coordinator *************:9092 (id: 2147483646 rack: null)
09:52:50.556 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-22, groupId=storage] (Re-)joining group
09:52:50.556 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-24, groupId=storage] (Re-)joining group
09:52:50.556 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-11, groupId=storage] (Re-)joining group
09:52:50.556 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-6, groupId=storage] (Re-)joining group
09:52:50.556 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-1, groupId=storage] (Re-)joining group
09:52:50.557 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-14, groupId=storage] (Re-)joining group
09:52:50.557 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-5, groupId=storage] (Re-)joining group
09:52:50.557 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-7, groupId=storage] (Re-)joining group
09:52:50.557 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-20, groupId=storage] (Re-)joining group
09:52:50.557 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-8, groupId=storage] (Re-)joining group
09:52:50.558 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-9, groupId=storage] (Re-)joining group
09:52:50.558 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-12, groupId=storage] (Re-)joining group
09:52:50.558 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-15, groupId=storage] (Re-)joining group
09:52:50.558 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-23, groupId=storage] (Re-)joining group
09:52:50.558 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-21, groupId=storage] (Re-)joining group
09:52:50.558 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-10, groupId=storage] (Re-)joining group
09:52:50.559 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-3, groupId=storage] (Re-)joining group
09:52:50.559 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-19, groupId=storage] (Re-)joining group
09:52:50.559 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-13, groupId=storage] (Re-)joining group
09:52:50.559 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-4, groupId=storage] (Re-)joining group
09:52:50.559 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-17, groupId=storage] (Re-)joining group
09:52:50.559 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-2, groupId=storage] (Re-)joining group
09:52:50.560 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-16, groupId=storage] (Re-)joining group
09:52:50.560 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-18, groupId=storage] (Re-)joining group
09:52:50.620 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-11, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.620 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-16, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.620 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-20, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.620 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-11, groupId=storage] (Re-)joining group
09:52:50.620 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-16, groupId=storage] (Re-)joining group
09:52:50.620 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-20, groupId=storage] (Re-)joining group
09:52:50.622 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-2, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.622 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-4, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.622 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-2, groupId=storage] (Re-)joining group
09:52:50.622 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-3, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.622 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-4, groupId=storage] (Re-)joining group
09:52:50.623 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-1, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.623 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-3, groupId=storage] (Re-)joining group
09:52:50.623 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-1, groupId=storage] (Re-)joining group
09:52:50.624 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-6, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.624 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-23, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.624 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-18, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.624 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-19, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.624 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-22, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.624 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-17, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.624 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-14, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.624 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-10, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.624 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-24, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.624 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-5, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.625 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-6, groupId=storage] (Re-)joining group
09:52:50.625 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-23, groupId=storage] (Re-)joining group
09:52:50.625 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-13, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.625 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-22, groupId=storage] (Re-)joining group
09:52:50.625 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-19, groupId=storage] (Re-)joining group
09:52:50.625 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-17, groupId=storage] (Re-)joining group
09:52:50.625 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-21, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.625 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-18, groupId=storage] (Re-)joining group
09:52:50.625 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-14, groupId=storage] (Re-)joining group
09:52:50.625 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-5, groupId=storage] (Re-)joining group
09:52:50.625 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-10, groupId=storage] (Re-)joining group
09:52:50.625 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-24, groupId=storage] (Re-)joining group
09:52:50.625 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-7, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.626 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-13, groupId=storage] (Re-)joining group
09:52:50.627 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-15, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.627 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-9, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.627 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-12, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.627 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-21, groupId=storage] (Re-)joining group
09:52:50.627 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-8, groupId=storage] Request joining group due to: need to re-join with the given member-id
09:52:50.628 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-7, groupId=storage] (Re-)joining group
09:52:50.628 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-9, groupId=storage] (Re-)joining group
09:52:50.629 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-8, groupId=storage] (Re-)joining group
09:52:50.628 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-15, groupId=storage] (Re-)joining group
09:52:50.628 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [sendJoinGroupRequest,535] - [Consumer clientId=consumer-storage-12, groupId=storage] (Re-)joining group
09:52:50.643 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-17, groupId=storage] Resetting the last seen epoch of partition logs-0 to 0 since the associated topicId changed from null to CkFKnhmqRtWSoLRNjemjsg
09:52:50.650 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-18, groupId=storage] Resetting the last seen epoch of partition logs-0 to 0 since the associated topicId changed from null to CkFKnhmqRtWSoLRNjemjsg
09:52:50.653 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-16, groupId=storage] Resetting the last seen epoch of partition logs-0 to 0 since the associated topicId changed from null to CkFKnhmqRtWSoLRNjemjsg
09:52:56.630 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-22, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-22-f65a0959-4c12-4535-98e5-8955ea98e539', protocol='range'}
09:52:56.630 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-19, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-19-ad00007e-7dd4-4044-987f-ee04590476e0', protocol='range'}
09:52:56.630 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-16, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-16-c8a89aee-5307-464f-aa2e-a12726fbcbef', protocol='range'}
09:52:56.630 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-20, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-20-685d9fdc-6005-4b72-8cd8-46fd707fd475', protocol='range'}
09:52:56.630 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-4, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-4-5e2cc75b-436d-4701-bfbc-4435cfc1b4f5', protocol='range'}
09:52:56.631 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-7, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-7-971bfb89-3fbc-415b-95c1-a6a6f56cf395', protocol='range'}
09:52:56.631 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-21, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-21-406cda2b-412c-4172-9bff-579aceb42334', protocol='range'}
09:52:56.631 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-11, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-11-684210ff-8a82-471b-a1b6-322df27a1e14', protocol='range'}
09:52:56.631 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-8, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-8-564d940a-0907-45f2-ba1c-86038bdabbc4', protocol='range'}
09:52:56.631 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-2, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-2-917c18e1-b596-451b-aaf2-1f88e60d36c4', protocol='range'}
09:52:56.631 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-1, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-1-e683a63b-b05b-4320-aa5a-97bdd570cf42', protocol='range'}
09:52:56.631 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-3, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-3-625b9828-d455-4237-be00-86750312b2ab', protocol='range'}
09:52:56.631 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-13, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-13-65a5497e-7210-4cb3-ac25-f8f669f5b59d', protocol='range'}
09:52:56.632 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-18, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-18-eda38f3b-eb53-4e3c-9934-886c5e11ffd3', protocol='range'}
09:52:56.631 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-17, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-17-683b55ab-0b29-4202-8b86-3fca272bdc79', protocol='range'}
09:52:56.632 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-6, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-6-da5b72a7-c0ea-47e0-bd23-baa7e14282b1', protocol='range'}
09:52:56.632 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-5, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-5-c902ee75-f88f-411a-926b-8fab60e1289c', protocol='range'}
09:52:56.632 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-23, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-23-00d071d8-6004-437f-ac4f-27d0d7953a49', protocol='range'}
09:52:56.632 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-9, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-9-3a404dc2-ef52-4571-9818-a70e53792626', protocol='range'}
09:52:56.633 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-14, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-14-4b770869-b202-44ae-a384-08f94f16efa5', protocol='range'}
09:52:56.633 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-15, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-15-fbafb52d-d0d2-4614-b48e-72391c3a3fa5', protocol='range'}
09:52:56.633 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-24, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-24-dc10e19f-dc9a-456d-9416-463047b14d1f', protocol='range'}
09:52:56.633 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-10, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-10-fe0c11b1-70c8-4fb4-8ef4-f7c917e6921f', protocol='range'}
09:52:56.633 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,595] - [Consumer clientId=consumer-storage-12, groupId=storage] Successfully joined group with generation Generation{generationId=79, memberId='consumer-storage-12-f625c08a-c6f4-4302-b05b-ef558d629223', protocol='range'}
09:52:56.656 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-11, groupId=storage] Resetting the last seen epoch of partition fusion-journal-dedupcate-mapping-0 to 0 since the associated topicId changed from null to WxO4uUfdR9e0VPm2jTL_Yg
09:52:56.656 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-11, groupId=storage] Resetting the last seen epoch of partition fusion-mapping-0 to 0 since the associated topicId changed from null to WMGluhexQZ-Wr5klceoA1w
09:52:56.656 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-11, groupId=storage] Resetting the last seen epoch of partition fusion-article-dedupcate-mapping-0 to 0 since the associated topicId changed from null to K1JJzi6xTa--4ovI853QnA
09:52:56.656 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-11, groupId=storage] Resetting the last seen epoch of partition rsync-es-0 to 16 since the associated topicId changed from null to cPNHXbgUQnScGipupRYp9A
09:52:56.656 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-11, groupId=storage] Resetting the last seen epoch of partition logs-0 to 0 since the associated topicId changed from null to CkFKnhmqRtWSoLRNjemjsg
09:52:56.657 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-11, groupId=storage] Resetting the last seen epoch of partition single-journal-dedupcate-mapping-0 to 0 since the associated topicId changed from null to yyKozdmRQMKTbVA5UdfOgQ
09:52:56.657 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.Metadata - [updateLatestMetadata,402] - [Consumer clientId=consumer-storage-11, groupId=storage] Resetting the last seen epoch of partition parse-mapping1-0 to 0 since the associated topicId changed from null to f1B-od9jRUaVbOT32QzoOA
09:52:56.662 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [performAssignment,659] - [Consumer clientId=consumer-storage-11, groupId=storage] Finished assignment for group at generation 79: {consumer-storage-21-406cda2b-412c-4172-9bff-579aceb42334=Assignment(partitions=[]), consumer-storage-18-eda38f3b-eb53-4e3c-9934-886c5e11ffd3=Assignment(partitions=[]), consumer-storage-9-3a404dc2-ef52-4571-9818-a70e53792626=Assignment(partitions=[]), consumer-storage-22-f65a0959-4c12-4535-98e5-8955ea98e539=Assignment(partitions=[single-journal-dedupcate-mapping-0]), consumer-storage-2-917c18e1-b596-451b-aaf2-1f88e60d36c4=Assignment(partitions=[]), consumer-storage-1-e683a63b-b05b-4320-aa5a-97bdd570cf42=Assignment(partitions=[parse-mapping1-0]), consumer-storage-13-65a5497e-7210-4cb3-ac25-f8f669f5b59d=Assignment(partitions=[fusion-article-dedupcate-mapping-0]), consumer-storage-16-c8a89aee-5307-464f-aa2e-a12726fbcbef=Assignment(partitions=[logs-0]), consumer-storage-4-5e2cc75b-436d-4701-bfbc-4435cfc1b4f5=Assignment(partitions=[rsync-es-0]), consumer-storage-3-625b9828-d455-4237-be00-86750312b2ab=Assignment(partitions=[]), consumer-storage-11-684210ff-8a82-471b-a1b6-322df27a1e14=Assignment(partitions=[]), consumer-storage-24-dc10e19f-dc9a-456d-9416-463047b14d1f=Assignment(partitions=[]), consumer-storage-8-564d940a-0907-45f2-ba1c-86038bdabbc4=Assignment(partitions=[]), consumer-storage-17-683b55ab-0b29-4202-8b86-3fca272bdc79=Assignment(partitions=[]), consumer-storage-19-ad00007e-7dd4-4044-987f-ee04590476e0=Assignment(partitions=[fusion-mapping-0]), consumer-storage-15-fbafb52d-d0d2-4614-b48e-72391c3a3fa5=Assignment(partitions=[]), consumer-storage-12-f625c08a-c6f4-4302-b05b-ef558d629223=Assignment(partitions=[]), consumer-storage-5-c902ee75-f88f-411a-926b-8fab60e1289c=Assignment(partitions=[]), consumer-storage-14-4b770869-b202-44ae-a384-08f94f16efa5=Assignment(partitions=[]), consumer-storage-10-fe0c11b1-70c8-4fb4-8ef4-f7c917e6921f=Assignment(partitions=[single-article-dedupcate-mapping-0]), consumer-storage-6-da5b72a7-c0ea-47e0-bd23-baa7e14282b1=Assignment(partitions=[]), consumer-storage-23-00d071d8-6004-437f-ac4f-27d0d7953a49=Assignment(partitions=[]), consumer-storage-7-971bfb89-3fbc-415b-95c1-a6a6f56cf395=Assignment(partitions=[fusion-journal-dedupcate-mapping-0]), consumer-storage-20-685d9fdc-6005-4b72-8cd8-46fd707fd475=Assignment(partitions=[])}
09:52:56.679 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-22, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-22-f65a0959-4c12-4535-98e5-8955ea98e539', protocol='range'}
09:52:56.679 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-16, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-16-c8a89aee-5307-464f-aa2e-a12726fbcbef', protocol='range'}
09:52:56.680 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-4, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-4-5e2cc75b-436d-4701-bfbc-4435cfc1b4f5', protocol='range'}
09:52:56.680 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-20, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-20-685d9fdc-6005-4b72-8cd8-46fd707fd475', protocol='range'}
09:52:56.680 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-7, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-7-971bfb89-3fbc-415b-95c1-a6a6f56cf395', protocol='range'}
09:52:56.680 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-22, groupId=storage] Notifying assignor about the new Assignment(partitions=[single-journal-dedupcate-mapping-0])
09:52:56.680 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-4, groupId=storage] Notifying assignor about the new Assignment(partitions=[rsync-es-0])
09:52:56.680 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-20, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:52:56.680 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-16, groupId=storage] Notifying assignor about the new Assignment(partitions=[logs-0])
09:52:56.680 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-14, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-14-4b770869-b202-44ae-a384-08f94f16efa5', protocol='range'}
09:52:56.680 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-7, groupId=storage] Notifying assignor about the new Assignment(partitions=[fusion-journal-dedupcate-mapping-0])
09:52:56.680 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-2, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-2-917c18e1-b596-451b-aaf2-1f88e60d36c4', protocol='range'}
09:52:56.681 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-8, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-8-564d940a-0907-45f2-ba1c-86038bdabbc4', protocol='range'}
09:52:56.680 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-15, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-15-fbafb52d-d0d2-4614-b48e-72391c3a3fa5', protocol='range'}
09:52:56.680 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-21, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-21-406cda2b-412c-4172-9bff-579aceb42334', protocol='range'}
09:52:56.680 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-9, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-9-3a404dc2-ef52-4571-9818-a70e53792626', protocol='range'}
09:52:56.680 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-11, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-11-684210ff-8a82-471b-a1b6-322df27a1e14', protocol='range'}
09:52:56.681 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-3, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-3-625b9828-d455-4237-be00-86750312b2ab', protocol='range'}
09:52:56.681 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-20, groupId=storage] Adding newly assigned partitions: 
09:52:56.681 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-1, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-1-e683a63b-b05b-4320-aa5a-97bdd570cf42', protocol='range'}
09:52:56.681 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-13, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-13-65a5497e-7210-4cb3-ac25-f8f669f5b59d', protocol='range'}
09:52:56.681 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-14, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:52:56.681 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-24, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-24-dc10e19f-dc9a-456d-9416-463047b14d1f', protocol='range'}
09:52:56.682 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-15, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:52:56.682 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-21, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:52:56.682 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-9, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:52:56.681 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-2, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:52:56.682 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-3, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:52:56.682 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-8, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:52:56.682 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-11, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:52:56.682 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-1, groupId=storage] Notifying assignor about the new Assignment(partitions=[parse-mapping1-0])
09:52:56.682 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-14, groupId=storage] Adding newly assigned partitions: 
09:52:56.682 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-13, groupId=storage] Notifying assignor about the new Assignment(partitions=[fusion-article-dedupcate-mapping-0])
09:52:56.682 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-15, groupId=storage] Adding newly assigned partitions: 
09:52:56.682 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-24, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:52:56.682 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-21, groupId=storage] Adding newly assigned partitions: 
09:52:56.682 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-9, groupId=storage] Adding newly assigned partitions: 
09:52:56.682 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-12, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-12-f625c08a-c6f4-4302-b05b-ef558d629223', protocol='range'}
09:52:56.682 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-2, groupId=storage] Adding newly assigned partitions: 
09:52:56.683 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-3, groupId=storage] Adding newly assigned partitions: 
09:52:56.682 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-10, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-10-fe0c11b1-70c8-4fb4-8ef4-f7c917e6921f', protocol='range'}
09:52:56.683 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-8, groupId=storage] Adding newly assigned partitions: 
09:52:56.683 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-11, groupId=storage] Adding newly assigned partitions: 
09:52:56.683 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-17, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-17-683b55ab-0b29-4202-8b86-3fca272bdc79', protocol='range'}
09:52:56.683 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-24, groupId=storage] Adding newly assigned partitions: 
09:52:56.683 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-6, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-6-da5b72a7-c0ea-47e0-bd23-baa7e14282b1', protocol='range'}
09:52:56.683 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-19, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-19-ad00007e-7dd4-4044-987f-ee04590476e0', protocol='range'}
09:52:56.683 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-23, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-23-00d071d8-6004-437f-ac4f-27d0d7953a49', protocol='range'}
09:52:56.683 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-12, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:52:56.684 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-10, groupId=storage] Notifying assignor about the new Assignment(partitions=[single-article-dedupcate-mapping-0])
09:52:56.683 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-18, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-18-eda38f3b-eb53-4e3c-9934-886c5e11ffd3', protocol='range'}
09:52:56.684 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,761] - [Consumer clientId=consumer-storage-5, groupId=storage] Successfully synced group in generation Generation{generationId=79, memberId='consumer-storage-5-c902ee75-f88f-411a-926b-8fab60e1289c', protocol='range'}
09:52:56.684 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-17, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:52:56.684 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-6, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:52:56.684 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-12, groupId=storage] Adding newly assigned partitions: 
09:52:56.684 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-19, groupId=storage] Notifying assignor about the new Assignment(partitions=[fusion-mapping-0])
09:52:56.684 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-23, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:52:56.684 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-18, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:52:56.684 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokeOnAssignment,280] - [Consumer clientId=consumer-storage-5, groupId=storage] Notifying assignor about the new Assignment(partitions=[])
09:52:56.685 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-17, groupId=storage] Adding newly assigned partitions: 
09:52:56.685 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-6, groupId=storage] Adding newly assigned partitions: 
09:52:56.685 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-23, groupId=storage] Adding newly assigned partitions: 
09:52:56.685 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-18, groupId=storage] Adding newly assigned partitions: 
09:52:56.685 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-5, groupId=storage] Adding newly assigned partitions: 
09:52:56.694 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-1, groupId=storage] Adding newly assigned partitions: parse-mapping1-0
09:52:56.694 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-7, groupId=storage] Adding newly assigned partitions: fusion-journal-dedupcate-mapping-0
09:52:56.694 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-13, groupId=storage] Adding newly assigned partitions: fusion-article-dedupcate-mapping-0
09:52:56.694 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-22, groupId=storage] Adding newly assigned partitions: single-journal-dedupcate-mapping-0
09:52:56.694 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-4, groupId=storage] Adding newly assigned partitions: rsync-es-0
09:52:56.694 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-10, groupId=storage] Adding newly assigned partitions: single-article-dedupcate-mapping-0
09:52:56.694 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-19, groupId=storage] Adding newly assigned partitions: fusion-mapping-0
09:52:56.694 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsAssigned,292] - [Consumer clientId=consumer-storage-16, groupId=storage] Adding newly assigned partitions: logs-0
09:52:56.719 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,1405] - [Consumer clientId=consumer-storage-13, groupId=storage] Found no committed offset for partition fusion-article-dedupcate-mapping-0
09:52:56.719 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,1405] - [Consumer clientId=consumer-storage-16, groupId=storage] Found no committed offset for partition logs-0
09:52:56.719 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,1405] - [Consumer clientId=consumer-storage-19, groupId=storage] Found no committed offset for partition fusion-mapping-0
09:52:56.719 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,1405] - [Consumer clientId=consumer-storage-4, groupId=storage] Found no committed offset for partition rsync-es-0
09:52:56.720 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,1405] - [Consumer clientId=consumer-storage-22, groupId=storage] Found no committed offset for partition single-journal-dedupcate-mapping-0
09:52:56.720 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,1405] - [Consumer clientId=consumer-storage-10, groupId=storage] Found no committed offset for partition single-article-dedupcate-mapping-0
09:52:56.721 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [handle,1405] - [Consumer clientId=consumer-storage-7, groupId=storage] Found no committed offset for partition fusion-journal-dedupcate-mapping-0
09:52:56.724 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [refreshCommittedOffsetsIfNeeded,851] - [Consumer clientId=consumer-storage-1, groupId=storage] Setting offset for partition parse-mapping1-0 to the committed offset FetchPosition{offset=9, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=0}}
09:52:56.741 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.SubscriptionState - [maybeSeekUnvalidated,398] - [Consumer clientId=consumer-storage-16, groupId=storage] Resetting offset for partition logs-0 to position FetchPosition{offset=0, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=0}}.
09:52:56.749 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.SubscriptionState - [maybeSeekUnvalidated,398] - [Consumer clientId=consumer-storage-22, groupId=storage] Resetting offset for partition single-journal-dedupcate-mapping-0 to position FetchPosition{offset=0, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=0}}.
09:52:56.752 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.SubscriptionState - [maybeSeekUnvalidated,398] - [Consumer clientId=consumer-storage-7, groupId=storage] Resetting offset for partition fusion-journal-dedupcate-mapping-0 to position FetchPosition{offset=0, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=0}}.
09:52:56.754 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.SubscriptionState - [maybeSeekUnvalidated,398] - [Consumer clientId=consumer-storage-19, groupId=storage] Resetting offset for partition fusion-mapping-0 to position FetchPosition{offset=0, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=0}}.
09:52:56.755 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.SubscriptionState - [maybeSeekUnvalidated,398] - [Consumer clientId=consumer-storage-4, groupId=storage] Resetting offset for partition rsync-es-0 to position FetchPosition{offset=0, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=16}}.
09:52:56.755 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.SubscriptionState - [maybeSeekUnvalidated,398] - [Consumer clientId=consumer-storage-10, groupId=storage] Resetting offset for partition single-article-dedupcate-mapping-0 to position FetchPosition{offset=0, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=0}}.
09:52:56.756 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.SubscriptionState - [maybeSeekUnvalidated,398] - [Consumer clientId=consumer-storage-13, groupId=storage] Resetting offset for partition fusion-article-dedupcate-mapping-0 to position FetchPosition{offset=0, offsetEpoch=Optional.empty, currentLeader=LeaderAndEpoch{leader=Optional[*************:9092 (id: 1 rack: null)], epoch=0}}.
09:53:36.817 [http-nio-9207-exec-3] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:53:59.284 [http-nio-9207-exec-6] INFO  c.r.s.p.s.i.RuleBaseServiceImpl - [addRuleBase,89] - 新增解析规则数据:AddRuleBaseReqVO(name=8888888888888888888, sourceId=06, docType=J, ruleType=ANALYSIS, useStatus=N, ruleStatus=EXECUTING, toolStatus=DATA_SENT, description=888, sampleFile=/test/storage/rule/parsingmapping/example_20250610191704A001.zip, phase=null, fileList=[ExFileVO(name=example_20250610191704A001.zip, url=/test/storage/rule/parsingmapping/example_20250610191704A001.zip, md5=af601228f47196fbf9d3f7b178dfd429, size=399066)])
09:53:59.334 [http-nio-9207-exec-6] INFO  c.r.s.p.s.i.RuleBaseServiceImpl - [addRuleBase,113] - 根据规则文件进行工具接口调用，解析映射规则：8888888888888888888 
09:53:59.339 [pool-7-thread-1] INFO  c.r.s.a.s.AnalysisService - [toolInterfaceCall,64] - 解析工具开始调用 ==> 线程名称：pool-7-thread-1
09:54:04.500 [pool-7-thread-1] INFO  c.r.s.u.RestUtils - [analysis,86] - 请求参数: url=http://***********:20215/tool_1_get_rules_and_result, body={files=[file [d:\home\library2020\tempDir\b284009f-f5b6-4f0c-9561-ef68906598a3\example_20250610191704A001.zip]], rules=[[]], checks=[[FileChecks(fileName=example_20250610191704A001.zip, md5=af601228f47196fbf9d3f7b178dfd429)]], info=[RuleInfo(ruleId=129, sourceId=J, dataType=null, docType=J, sourceType=PubmedArticle)]}
09:54:25.589 [pool-7-thread-1] INFO  c.r.s.a.s.AnalysisService - [updateAfterSuccess,197] - 工具调用成功：null
09:54:25.609 [pool-7-thread-1] INFO  c.r.s.a.s.AnalysisService - [updateAfterSuccess,218] - 工具返回数据转换异常：结果字符串不能为空
09:54:25.609 [pool-7-thread-1] INFO  c.r.s.a.s.AnalysisService - [updateAfterFailure,177] - 工具调用失败：结果字符串不能为空
09:56:18.477 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-3, groupId=storage] Member consumer-storage-3-625b9828-d455-4237-be00-86750312b2ab sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.477 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-2, groupId=storage] Member consumer-storage-2-917c18e1-b596-451b-aaf2-1f88e60d36c4 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.477 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-5, groupId=storage] Member consumer-storage-5-c902ee75-f88f-411a-926b-8fab60e1289c sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.478 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-5, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.478 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-2, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.478 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-3, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.478 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-8, groupId=storage] Member consumer-storage-8-564d940a-0907-45f2-ba1c-86038bdabbc4 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.478 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-9, groupId=storage] Member consumer-storage-9-3a404dc2-ef52-4571-9818-a70e53792626 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.478 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-5, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.478 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-2, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.479 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-3, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.479 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-8, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.479 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-5, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.479 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-9, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.479 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsRevoked,311] - [Consumer clientId=consumer-storage-1, groupId=storage] Revoke previously assigned partitions parse-mapping1-0
09:56:18.479 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-2, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.479 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsRevoked,311] - [Consumer clientId=consumer-storage-4, groupId=storage] Revoke previously assigned partitions rsync-es-0
09:56:18.479 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-6, groupId=storage] Member consumer-storage-6-da5b72a7-c0ea-47e0-bd23-baa7e14282b1 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.479 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-3, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.479 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-8, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.480 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-9, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.480 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsRevoked,311] - [Consumer clientId=consumer-storage-7, groupId=storage] Revoke previously assigned partitions fusion-journal-dedupcate-mapping-0
09:56:18.480 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-11, groupId=storage] Member consumer-storage-11-684210ff-8a82-471b-a1b6-322df27a1e14 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.481 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-6, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.481 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-8, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.481 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsRevoked,311] - [Consumer clientId=consumer-storage-10, groupId=storage] Revoke previously assigned partitions single-article-dedupcate-mapping-0
09:56:18.481 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-9, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.482 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-11, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.482 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-5, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.482 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-12, groupId=storage] Member consumer-storage-12-f625c08a-c6f4-4302-b05b-ef558d629223 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.482 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-2, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.482 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-6, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.482 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsRevoked,311] - [Consumer clientId=consumer-storage-13, groupId=storage] Revoke previously assigned partitions fusion-article-dedupcate-mapping-0
09:56:18.482 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-14, groupId=storage] Member consumer-storage-14-4b770869-b202-44ae-a384-08f94f16efa5 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.483 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-9, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.483 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-11, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.484 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-5, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.484 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-12, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.484 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-2, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.484 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-6, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.485 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-15, groupId=storage] Member consumer-storage-15-fbafb52d-d0d2-4614-b48e-72391c3a3fa5 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.485 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-14, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.485 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-18, groupId=storage] Member consumer-storage-18-eda38f3b-eb53-4e3c-9934-886c5e11ffd3 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.485 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-23, groupId=storage] Member consumer-storage-23-00d071d8-6004-437f-ac4f-27d0d7953a49 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.485 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-21, groupId=storage] Member consumer-storage-21-406cda2b-412c-4172-9bff-579aceb42334 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.485 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-9, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.485 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-20, groupId=storage] Member consumer-storage-20-685d9fdc-6005-4b72-8cd8-46fd707fd475 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.485 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-24, groupId=storage] Member consumer-storage-24-dc10e19f-dc9a-456d-9416-463047b14d1f sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.485 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-3, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.486 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsRevoked,311] - [Consumer clientId=consumer-storage-16, groupId=storage] Revoke previously assigned partitions logs-0
09:56:18.486 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsRevoked,311] - [Consumer clientId=consumer-storage-19, groupId=storage] Revoke previously assigned partitions fusion-mapping-0
09:56:18.486 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-8, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.487 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [invokePartitionsRevoked,311] - [Consumer clientId=consumer-storage-22, groupId=storage] Revoke previously assigned partitions single-journal-dedupcate-mapping-0
09:56:18.487 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-11, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.491 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-12, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.492 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-6, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.493 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-14, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.493 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-18, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.493 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-23, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.493 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-20, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.493 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-15, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.493 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-21, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.493 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-3, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.493 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-24, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.494 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-8, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.494 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-11, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.495 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-12, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.495 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-17, groupId=storage] Member consumer-storage-17-683b55ab-0b29-4202-8b86-3fca272bdc79 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.496 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-6, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.496 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-14, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.496 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-20, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.496 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-18, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.496 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-4, groupId=storage] Member consumer-storage-4-5e2cc75b-436d-4701-bfbc-4435cfc1b4f5 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.496 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-23, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.496 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-15, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.496 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-7, groupId=storage] Member consumer-storage-7-971bfb89-3fbc-415b-95c1-a6a6f56cf395 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.496 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-21, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.497 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-24, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.497 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-1, groupId=storage] Member consumer-storage-1-e683a63b-b05b-4320-aa5a-97bdd570cf42 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.497 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-11, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.497 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-13, groupId=storage] Member consumer-storage-13-65a5497e-7210-4cb3-ac25-f8f669f5b59d sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.498 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-16, groupId=storage] Member consumer-storage-16-c8a89aee-5307-464f-aa2e-a12726fbcbef sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.498 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-19, groupId=storage] Member consumer-storage-19-ad00007e-7dd4-4044-987f-ee04590476e0 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.498 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-17, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.498 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-12, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.498 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-22, groupId=storage] Member consumer-storage-22-f65a0959-4c12-4535-98e5-8955ea98e539 sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.499 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [maybeLeaveGroup,1060] - [Consumer clientId=consumer-storage-10, groupId=storage] Member consumer-storage-10-fe0c11b1-70c8-4fb4-8ef4-f7c917e6921f sending LeaveGroup request to coordinator *************:9092 (id: 2147483646 rack: null) due to the consumer unsubscribed from all topics
09:56:18.500 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-20, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.500 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-4, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.500 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-18, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.500 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-23, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.500 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-15, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.500 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-21, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.500 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-7, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.500 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-24, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.500 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-14, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.501 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-1, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.501 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-13, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.502 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-16, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.502 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-19, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.502 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-17, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.503 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-10, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.502 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-12, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.504 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.502 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-22, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.504 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-4, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.504 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-20, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.505 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-7, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.505 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-23, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.505 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-18, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.506 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.505 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-21, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.505 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-24, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.506 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.506 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-15, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.507 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-14, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.507 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-1, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.507 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-13, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.507 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-16, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.508 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.508 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-19, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.508 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-17, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.508 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-10, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.508 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-22, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.510 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-4, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.510 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-20, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.510 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-7, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.510 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-23, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.510 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-18, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.510 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.510 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-21, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.510 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.511 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-15, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.510 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-24, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.512 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-1, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.512 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.512 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.513 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.508 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.513 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-13, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.513 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-16, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.513 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.513 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-19, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.513 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-10, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.514 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.KafkaConsumer - [unsubscribe,1075] - [Consumer clientId=consumer-storage-22, groupId=storage] Unsubscribed all topics or patterns and assigned partitions
09:56:18.515 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-4, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.516 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.516 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-7, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.516 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.517 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.517 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.517 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.517 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-1, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.518 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-13, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.518 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-16, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.518 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.519 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.520 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.519 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-10, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.520 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.520 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-19, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.520 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-17, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.521 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [resetStateAndGeneration,972] - [Consumer clientId=consumer-storage-22, groupId=storage] Resetting generation due to: consumer pro-actively leaving the group
09:56:18.521 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-4, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.522 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-7, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.523 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.523 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.523 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.523 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-1, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.524 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-13, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.524 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-16, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.524 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.524 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-10, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.525 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-17, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.525 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-22, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.524 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.524 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [requestRejoin,1000] - [Consumer clientId=consumer-storage-19, groupId=storage] Request joining group due to: consumer pro-actively leaving the group
09:56:18.526 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.528 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.528 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.527 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.528 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.528 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.530 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.530 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.531 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.532 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.532 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.532 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.532 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.532 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.533 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.534 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.534 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.534 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.534 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.534 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.534 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.535 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.535 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.536 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.537 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.537 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.537 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.538 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.538 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.539 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.539 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.539 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.541 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.541 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.542 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.542 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.542 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.542 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.542 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.543 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.m.Metrics - [close,659] - Metrics scheduler closed
09:56:18.544 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.544 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.545 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.545 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.545 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.546 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.m.Metrics - [close,663] - Closing reporter org.apache.kafka.common.metrics.JmxReporter
09:56:18.547 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.m.Metrics - [close,669] - Metrics reporters closed
09:56:18.560 [org.springframework.kafka.KafkaListenerEndpointContainer#1-2-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-24 unregistered
09:56:18.574 [org.springframework.kafka.KafkaListenerEndpointContainer#4-2-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-15 unregistered
09:56:18.580 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-19 unregistered
09:56:18.590 [org.springframework.kafka.KafkaListenerEndpointContainer#4-0-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-13 unregistered
09:56:18.603 [org.springframework.kafka.KafkaListenerEndpointContainer#7-0-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-4 unregistered
09:56:18.607 [org.springframework.kafka.KafkaListenerEndpointContainer#6-1-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-2 unregistered
09:56:18.612 [org.springframework.kafka.KafkaListenerEndpointContainer#1-0-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-22 unregistered
09:56:18.616 [org.springframework.kafka.KafkaListenerEndpointContainer#5-0-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-16 unregistered
09:56:18.619 [org.springframework.kafka.KafkaListenerEndpointContainer#7-2-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-6 unregistered
09:56:18.622 [org.springframework.kafka.KafkaListenerEndpointContainer#3-0-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-10 unregistered
09:56:18.626 [org.springframework.kafka.KafkaListenerEndpointContainer#4-1-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-14 unregistered
09:56:18.634 [org.springframework.kafka.KafkaListenerEndpointContainer#7-1-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-5 unregistered
09:56:18.652 [org.springframework.kafka.KafkaListenerEndpointContainer#1-1-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-23 unregistered
09:56:18.655 [org.springframework.kafka.KafkaListenerEndpointContainer#0-2-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-21 unregistered
09:56:18.658 [org.springframework.kafka.KafkaListenerEndpointContainer#3-1-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-11 unregistered
09:56:18.670 [org.springframework.kafka.KafkaListenerEndpointContainer#6-2-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-3 unregistered
09:56:18.673 [org.springframework.kafka.KafkaListenerEndpointContainer#5-1-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-17 unregistered
09:56:18.674 [org.springframework.kafka.KafkaListenerEndpointContainer#5-2-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-18 unregistered
09:56:18.675 [org.springframework.kafka.KafkaListenerEndpointContainer#6-0-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-1 unregistered
09:56:18.675 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-20 unregistered
09:56:18.676 [org.springframework.kafka.KafkaListenerEndpointContainer#2-1-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-8 unregistered
09:56:18.689 [org.springframework.kafka.KafkaListenerEndpointContainer#2-2-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-9 unregistered
09:56:18.689 [org.springframework.kafka.KafkaListenerEndpointContainer#2-0-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-7 unregistered
09:56:18.696 [org.springframework.kafka.KafkaListenerEndpointContainer#3-2-C-1] INFO  o.a.k.c.u.AppInfoParser - [unregisterAppInfo,83] - App info kafka.consumer for consumer-storage-12 unregistered
09:56:18.881 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
09:56:18.919 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
09:56:19.548 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource start closing ....
09:56:19.555 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
09:56:19.569 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
09:56:19.569 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - [destroy,98] - dynamic-datasource close the datasource named [master] success,
09:56:19.569 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,219] - dynamic-datasource all closed success,bye
