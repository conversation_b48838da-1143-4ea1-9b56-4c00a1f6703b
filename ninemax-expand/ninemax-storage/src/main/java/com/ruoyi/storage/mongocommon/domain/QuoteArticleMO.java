package com.ruoyi.storage.mongocommon.domain;

import com.anwen.mongo.annotation.ID;
import com.anwen.mongo.annotation.collection.CollectionField;
import com.anwen.mongo.annotation.collection.CollectionName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/7/23 9:09
 */
@Data
@CollectionName("quote_article")
public class QuoteArticleMO {
    /**
     * 主键ID
     */
    @ID
    @CollectionField("_id")
    private String id;

    /**
     * 篇级ID
     */
    /*@CollectionField("article_id")
    private String articleId;*/

    /**
     * 篇级文献标题
     */
    /*@CollectionField("article_title")
    private String articleTitle;*/

    /**
     * 篇级期刊标题
     */
   /* @CollectionField("journal_title")
    private String journalTitle;*/

    /**
     * 引用文献ID
     */
    @CollectionField("ref_id")
    private String refId;

    /**
     * 引用文献标题
     */
    @CollectionField("ref_title")
    private String refTitle;

    /**
     * 篇级期刊标题
     */
    @CollectionField("ref_journal_title")
    private String refJournalTitle;

    /**
     * 被引用文献ID
     */
    @CollectionField("cited_id")
    private String citedId;

    /**
     * 被引用文献标题
     */
    @CollectionField("cited_title")
    private String citedTitle;

    /**
     * 被引用文献期刊标题
     */
    @CollectionField("cited_journal_title")
    private String citedJournalTitle;

    /**
     * 更新时间
     */
    @CollectionField("update_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 引用类型
     * 1:施引 2:被引
     */
    @CollectionField("cite_type")
    private String citeType;

    /**
     * 创建时间
     */
    @CollectionField("create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 年份
     */
    @CollectionField("year")
    private String year;

    /**
     * 卷期
     */
    @CollectionField("volume")
    private String volume;

    /**
     * 期刊期数
     */
    @CollectionField("issue")
    private String issue;

    /**
     * ISSN
     */
    @CollectionField("issn")
    private String issn;

    /**
     * 起始页码
     */
    @CollectionField("fpage")
    private String firstPage;

    /**
     * 结束页码
     */
    @CollectionField("lpage")
    private String lastPage;

    /**
     * 页码范围
     */
    @CollectionField("page_range")
    private String pageRange;

    /**
     * DOI
     */
    @CollectionField("doi")
    private String doi;

    /**
     * 追加文章
     * 1: 新增 2: 追加
     */
    @CollectionField("append_article")
    private String appendArticle;

    /**
     * 次数
     * citedType: 1: 参考文献数
     * citedType: 2: 被引次数
     */
    @CollectionField("cited_count")
    private String citedCount;
}
