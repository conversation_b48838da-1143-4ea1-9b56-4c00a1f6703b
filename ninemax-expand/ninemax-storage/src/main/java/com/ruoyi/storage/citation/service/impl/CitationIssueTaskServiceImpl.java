package com.ruoyi.storage.citation.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.anwen.mongo.conditions.update.UpdateChainWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.bean.BeanUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.storage.base.service.impl.BaseServiceImpl;
import com.ruoyi.storage.citation.domain.CitationFile;
import com.ruoyi.storage.citation.domain.CitationIssueTask;
import com.ruoyi.storage.citation.mapper.CitationIssueTaskMapper;
import com.ruoyi.storage.citation.service.ICitationFileService;
import com.ruoyi.storage.citation.service.ICitationIssueTaskService;
import com.ruoyi.storage.citation.utils.ReadFileUtils;
import com.ruoyi.storage.citation.vo.CitationIssueTaskDataVo;
import com.ruoyi.storage.citation.vo.CitationIssueTaskImportData;
import com.ruoyi.storage.fullArchive.domain.ArchiveUploadTask;
import com.ruoyi.storage.mongocommon.domain.FusionArticleProductMO;
import com.ruoyi.storage.mongocommon.domain.QuoteArticleMO;
import com.ruoyi.storage.mongocommon.param.QuoteArticleParamVo;
import com.ruoyi.storage.mongocommon.service.IFusionArticleProductMOService;
import com.ruoyi.storage.mongocommon.service.IQuoteArticleMOService;
import com.ruoyi.storage.utils.FileUtils;
import com.ruoyi.storage.utils.MinioPathUtils;
import com.ruoyi.storage.utils.MinioUtil;
import com.ruoyi.system.api.model.LoginUser;
import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 卷期任务表 服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
@Slf4j
public class CitationIssueTaskServiceImpl extends BaseServiceImpl<CitationIssueTaskMapper, CitationIssueTask> implements ICitationIssueTaskService {

    @Resource
    private RedisService redisService;

    @Resource
    private IQuoteArticleMOService quoteArticleMoService;

    @Resource
    private IFusionArticleProductMOService fusionArticleProductMoService;

    @Resource
    private ICitationFileService citationFileService;

    @Autowired
    private MinioClient minioClient;

    @Override
    public IPage<CitationIssueTask> queryCitationIssueTaskList(Page<CitationIssueTask> page, CitationIssueTaskDataVo citationIssueTaskDataVo) {
        /*int firstYear = redisService.getCacheObject("firstCitationLevel");
        int secondYear = redisService.getCacheObject("secondCitationLevel");
        int thirdYear = redisService.getCacheObject("thirdCitationLevel");
        LambdaQueryWrapper<CitationIssueTask> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CitationIssueTask::getYear, firstYear, secondYear, thirdYear);*/
        this.baseMapper.selectPage(page, this.buildQueryWrapper(citationIssueTaskDataVo));
        return this.baseMapper.selectPage(page, this.buildQueryWrapper(citationIssueTaskDataVo));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<?> importCitationIssueTaskList(MultipartFile file, String handleType,  String source, String rule) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isBlank(fileName)){
            return R.fail("请上传符合格式的文件");
        }

        String[] nameParts = fileName.split("\\.");
        if (nameParts.length < 2) {
            return R.fail("文件格式不正确");
        }
        String fileSuffix = nameParts[nameParts.length - 1];
        // 有处理类型参数
        boolean isHaveType = true;
        if(StringUtils.isBlank(handleType)){
            isHaveType = false;
        }
        if (StringUtils.equalsIgnoreCase("xml", fileSuffix)){
            log.info("xml文件解析开始");
            JSONObject jsonObject = parseXmlFile(file);
            CitationIssueTask citationIssueTask = new CitationIssueTask();
            citationIssueTask.setJournalTitle(jsonObject.getString("journalTitle"));
            citationIssueTask.setJournalId(jsonObject.getString("journalId"));
            citationIssueTask.setIssn(jsonObject.getString("issn"));
            citationIssueTask.setYear(jsonObject.getInteger("year"));
            citationIssueTask.setVolume(jsonObject.getString("volume"));
            citationIssueTask.setIssue(jsonObject.getString("issue"));
            citationIssueTask.setArticleNum(jsonObject.getInteger("articleNum"));
            if (StringUtils.isNotBlank(handleType)){
                if (StringUtils.equals("0", handleType)){
                    citationIssueTask.setStatus(0);
                }else{
                    // 没有引文 直接标记为已完成
                    citationIssueTask.setStatus(1);
                }
            }else{
                citationIssueTask.setStatus(0);
            }
            List<JSONObject> articleList = (List<JSONObject>) jsonObject.get("articleList");
            // TODO 判断数据库中是否存在
            LambdaQueryWrapper<CitationIssueTask> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CitationIssueTask::getJournalId, citationIssueTask.getJournalId());
            CitationIssueTask databaseCitationIssue = this.baseMapper.selectOne(lambdaQueryWrapper);
            // 保存到卷期任务表中
            if (Objects.nonNull(databaseCitationIssue)){
                citationIssueTask.setId(databaseCitationIssue.getId());
                LoginUser loginUser = SecurityUtils.getLoginUser();
                if (Objects.nonNull(loginUser)) {
                    citationIssueTask.setUpdateBy(loginUser.getUsername());
                }else{
                    citationIssueTask.setUpdateBy("admin");
                }
                citationIssueTask.setUpdateTime(new Date());
                this.baseMapper.updateById(citationIssueTask);
            }
            // 保存施引被引数据
            for (JSONObject object : articleList) {
                List<JSONObject> refsList = (List<JSONObject>)object.get("refs");
                // 保存施引与被引数据
                saveQuoteArticle(refsList, object.getString("articleId"),
                        object.getString("articleTitle"), jsonObject.getString("journalTitle"),
                        object.getString("doi"), object.getString("firstPage"), object.getString("lastPage"), isHaveType);
            }
            log.info("xml文件解析结束");
        }else if(StringUtils.equalsIgnoreCase("zip", fileSuffix)){
            log.info("zip文件解析开始");
            String fileMd5 = FileUtils.getFileMD5(file);
            // Map<String, Object> resultMap = parseZipFile(file);
            Map<String, Object> resultMap = parseZipFile(file);
            List<JSONObject> jsonObjectList = (List<JSONObject>)resultMap.get("list");
            if (jsonObjectList != null && !jsonObjectList.isEmpty()){
                for (JSONObject jsonObject : jsonObjectList) {
                    CitationIssueTask citationIssueTask = JSONObject.parseObject(jsonObject.toString(), CitationIssueTask.class);
                    if (StringUtils.isNotBlank(handleType)){
                        if (StringUtils.equals("0", handleType)){
                            citationIssueTask.setStatus(0);
                        }else{
                            // 没有引文 直接标记为已完成
                            citationIssueTask.setStatus(1);
                        }
                    }else{
                        citationIssueTask.setStatus(0);
                    }
                    citationIssueTask.setMd5(fileMd5);
                    List<JSONObject> articleList = (List<JSONObject>) jsonObject.get("articleList");
                    // TODO 判断数据库中是否存在
                    LambdaQueryWrapper<CitationIssueTask> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                    lambdaQueryWrapper.eq(CitationIssueTask::getJournalId, citationIssueTask.getJournalId());
                    CitationIssueTask databaseCitationIssue = this.baseMapper.selectOne(lambdaQueryWrapper);
                    if (Objects.nonNull(databaseCitationIssue)){
                        citationIssueTask.setId(databaseCitationIssue.getId());
                        this.baseMapper.updateById(citationIssueTask);
                    }
                    // 保存施引被引数据
                    for (JSONObject object : articleList) {
                        List<JSONObject> refsList = (List<JSONObject>)object.get("refs");
                        // 保存施引与被引数据
                        saveQuoteArticle(refsList, object.getString("articleId"),
                                object.getString("articleTitle"), jsonObject.getString("journalTitle"),
                                object.getString("doi"), object.getString("firstPage"), object.getString("lastPage"), isHaveType);

                    }
                };
            }
            LocalDate localDate = LocalDate.now();
            String date = localDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            String path = "citationfile/" + date + "/" + file.getOriginalFilename();
            try {
                if(!minioClient.bucketExists(BucketExistsArgs.builder().bucket("citationfile").build())){
                    minioClient.makeBucket(MakeBucketArgs.builder().bucket("citationfile").build());
                }
                minioClient.putObject(PutObjectArgs.builder()
                        .bucket("citationfile")
                        .object(path)
                        .stream(file.getInputStream(), file.getSize(), -1)
                        .build());
            } catch (Exception e) {
               log.error("上传minio失败", e);
            }
            JSONObject numberObject = (JSONObject)resultMap.get("number");
            int number = citationFileService.getCitationFileCountBySource(source, fileName);
            if (number == 0){
                CitationFile citationFile = CitationFile.builder()
                        .source(source).citationNoNum(numberObject.getInteger("relatedNotNumber")).citationYesNum(numberObject.getInteger("citationNumber"))
                        .noneNum(numberObject.getInteger("notRelated"))
                        .rule(rule).fileName(fileName).fileSize(file.getSize() + "").md5(fileMd5)
                        .status(0).localFileUri(path).build();
                citationFileService.save(citationFile);
            }
            log.info("zip文件解析结束");
        }
        return R.ok();
    }

    @Override
    public CitationIssueTask getCitationIssueTaskByJournalId(String journalId) {
        LambdaQueryWrapper<CitationIssueTask> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CitationIssueTask::getJournalId, journalId);

        return this.baseMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    public R<?> getJournalCitedList(String citedId) {
        return R.ok(quoteArticleMoService.getQuoteArticlesByArticleId(citedId));
    }

    @Override
    public R<?> getArticlesByJournalId(String journalId) {
        return R.ok(fusionArticleProductMoService.queryArticlesByJournalId(journalId));
    }

    @Override
    public R<?> saveCitationTask(List<CitationIssueTask> citationIssueTasks) {
        for (CitationIssueTask citationIssueTask : citationIssueTasks) {
            this.baseMapper.insert(citationIssueTask);
        }
        return R.ok();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<?> importCitationFile(MultipartFile file, String articleId, String articleTitle, String journalId, String journalTitle) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isBlank(fileName)){
            return R.fail();
        }
        String fileSuffix = fileName.split("\\.")[1];
        if (StringUtils.equals("xml", fileSuffix)){
            log.info("****xml文件解析开始****");
            List<JSONObject> jsonObjectList = parseXmlFileGetRefList(file, articleId);
            JSONObject jsonObject = parseXmlFile(file);
            List<JSONObject> articleList = (List<JSONObject>)jsonObject.get("articleList");
            String doi = "";
            String firstPage = "";
            String lastPage = "";
            for (JSONObject object : articleList) {
                String xmlArticleId = object.getString("articleId");
                if (StringUtils.equals(xmlArticleId, articleId)){
                    doi = object.getString("doi");
                }
                firstPage = object.getString("firstPage");
                lastPage = object.getString("lastPage");
            }
            saveSingleQuoteArticle(jsonObjectList, articleId, articleTitle, journalTitle, doi, firstPage, lastPage);
        }
        if (StringUtils.equals("zip", fileSuffix)){
            log.info("----zip文件解析开始----");
            // TODO 解析zip文件
        }if (StringUtils.equals("txt", fileSuffix)){
            log.info("txt文件解析开始");
            // TODO 解析txt文件
        }
        return null;
    }

    @Override
    public List<CitationIssueTask> getCitationTasListByMd5(String md5) {
        LambdaQueryWrapper<CitationIssueTask> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CitationIssueTask::getMd5, md5);
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }

    private LambdaQueryWrapper<CitationIssueTask> buildQueryWrapper(CitationIssueTaskDataVo citationIssueTaskDataVo) {
        LambdaQueryWrapper<CitationIssueTask> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(citationIssueTaskDataVo.getJournalTitle())){
            lambdaQueryWrapper.eq(CitationIssueTask::getJournalTitle, citationIssueTaskDataVo.getJournalTitle());
        }
        if (Objects.nonNull(citationIssueTaskDataVo.getYear())){
            lambdaQueryWrapper.eq(CitationIssueTask::getYear, citationIssueTaskDataVo.getYear());
        }
        if (Objects.nonNull(citationIssueTaskDataVo.getStatus())){
            lambdaQueryWrapper.eq(CitationIssueTask::getStatus, citationIssueTaskDataVo.getStatus());
        }
        if (StringUtils.isNotBlank(citationIssueTaskDataVo.getVolume())){
            lambdaQueryWrapper.eq(CitationIssueTask::getVolume, citationIssueTaskDataVo.getVolume());
        }
        if (StringUtils.isNotBlank(citationIssueTaskDataVo.getIssue())){
            lambdaQueryWrapper.eq(CitationIssueTask::getIssue, citationIssueTaskDataVo.getIssue());
        }
        if (StringUtils.isNotBlank(citationIssueTaskDataVo.getStartCreateTime())){
            lambdaQueryWrapper.ge(CitationIssueTask::getCreateTime, citationIssueTaskDataVo.getStartCreateTime());
        }
        if (StringUtils.isNotBlank(citationIssueTaskDataVo.getEndCreateTime())){
            lambdaQueryWrapper.le(CitationIssueTask::getCreateTime, citationIssueTaskDataVo.getEndCreateTime());
        }
        return lambdaQueryWrapper;
    }

    /**
     * 解析xml文件
     * @param file
     * @return
     */
    private static JSONObject parseXmlFile(MultipartFile file){
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject = ReadFileUtils.parseXmlDocument(file.getInputStream());
            log.info("解析xml文件数据结束");
        } catch (Exception e) {
            log.error("解析xml文件失败", e);
        }
        return jsonObject;
    }

    /**
     * 解析zip文件
     * @param file
     * @return
     */
    private static Map<String, Object> parseZipFile(MultipartFile file){
        Map<String, Object> result = new HashMap<>();
        try (ZipInputStream zipInputStream = new ZipInputStream(file.getInputStream())) {
            result = ReadFileUtils.readZipFile(zipInputStream);
        } catch (IOException e) {
            log.error("创建或关闭ZIP输入流时出错: {}", e.getMessage(), e);
            throw new RuntimeException("处理ZIP文件失败", e);
        } catch (Exception e) {
            log.error("解析ZIP文件时发生异常: {}", e.getMessage(), e);
            throw new RuntimeException("处理ZIP文件失败", e);
        }
        return result;
    }

    /**
     * 解析xml文件获取引文信息
     * @param file  上传文件
     * @param articleId   文章id
     * @return
     */
    private static List<JSONObject> parseXmlFileGetRefList(MultipartFile file, String articleId){
        List<JSONObject> jsonObjectList = new ArrayList<>();
        try {
            jsonObjectList = ReadFileUtils.parseXmlFileGetCitationData(file.getInputStream(), articleId);
        } catch (Exception e) {
            log.error("解析xml文件失败", e);
        }
        return jsonObjectList;
    }

    private static List<JSONObject> parseZipFileGetRefList(MultipartFile file, String articleId){
        List<JSONObject> jsonObjectList = new ArrayList<>();
        try {
            // jsonObjectList = ReadFileUtils.parseZipFileGetCitationData(new ZipInputStream(file.getInputStream()), articleId);
        } catch (Exception e) {
            log.error("解析zip文件失败", e);
        }
        return jsonObjectList;
    }

    private static List<JSONObject> parseTxtFileGetRefList(MultipartFile file, String articleId){
        List<JSONObject> jsonObjectList = new ArrayList<>();
        try {
            //  = ReadFileUtils.parseTxtFileGetCitationData(file.getInputStream(), articleId);
        } catch (Exception e) {
            log.error("解析txt文件失败", e);
        }
        return jsonObjectList;
    }

    /**
     * 保存施引与被引信息
     * @param jsonObjectList
     */
    private void saveQuoteArticle(List<JSONObject> jsonObjectList, String articleId, String articleTitle, String journalTitle,
                                  String doi, String firstPage, String lastPage, boolean isHaveType){
        saveQuoteArticleCommon(jsonObjectList, articleId, articleTitle, journalTitle, isHaveType);
        saveCitingArticle(articleId, articleTitle, journalTitle, doi, firstPage, lastPage, jsonObjectList.size());
    }

    private void saveSingleQuoteArticle(List<JSONObject> jsonObjectList, String articleId, String articleTitle, String journalTitle, String doi, String firstPage, String lastPage){
        saveQuoteArticleCommon(jsonObjectList, articleId, articleTitle, journalTitle, false);
        saveCitingArticle(articleId, articleTitle, journalTitle, doi, firstPage, lastPage, jsonObjectList.size());
    }

    private  void saveQuoteArticleCommon(List<JSONObject> jsonObjectList, String articleId, String articleTitle, String journalTitle, boolean checkFusion) {
        for (JSONObject jsonObject : jsonObjectList) {
            QuoteArticleParamVo quoteArticleParamVo = new QuoteArticleParamVo();
            quoteArticleParamVo.setRefId(jsonObject.getString("articleId"));
            // TODO  保存被引信息
            QuoteArticleMO quoteArticleMo = new QuoteArticleMO();
            // TODO 获取被引信息存在则跳过
            List<QuoteArticleMO> list = this.quoteArticleMoService.getQuoteArticleListByRefIds(quoteArticleParamVo);
            // 如果需要检查融合库数据
            if (checkFusion) {
                List<String> articleIdList = Collections.singletonList(jsonObject.getString("articleId"));
                List<FusionArticleProductMO> fusionArticleProductMoList = fusionArticleProductMoService.queryFusionArticleProductByArticleIds(articleIdList);
                if ((Objects.nonNull(fusionArticleProductMoList) && !fusionArticleProductMoList.isEmpty())
                        || (Objects.nonNull(list) && !list.isEmpty())) {
                    return;
                }
            } else {
                // 只检查已有引文数据
                if (Objects.nonNull(list) && !list.isEmpty()) {
                    QuoteArticleMO mongoQuoteArticleMo = list.get(0);
                    String citedId = mongoQuoteArticleMo.getCitedId();
                    if (!StringUtils.equalsIgnoreCase(articleId, citedId) || StringUtils.contains(mongoQuoteArticleMo.getCitedTitle(), articleId)){
                        UpdateChainWrapper updateChainWrapper = this.quoteArticleMoService.lambdaUpdate()
                                .eq(QuoteArticleMO::getId, mongoQuoteArticleMo.getId());
                        updateChainWrapper.set("cited_id", citedId + "," + articleId);
                        updateChainWrapper.set("cited_count", String.valueOf(Integer.parseInt(mongoQuoteArticleMo.getCitedCount()) + 1));
                        this.quoteArticleMoService.update(updateChainWrapper);
                    }
                    return;
                }
            }
            // 根据是否检查融合库数据决定是添加还是更新引文
            quoteArticleMo.setAppendArticle(checkFusion ? "1" : "2");
            quoteArticleMo.setRefId(jsonObject.getString("articleId"));
            quoteArticleMo.setRefTitle(jsonObject.getString("articleTitle"));
            quoteArticleMo.setRefJournalTitle(jsonObject.getString("sourceArticleTitle"));
            quoteArticleMo.setCitedId(articleId);
            quoteArticleMo.setCitedTitle(articleTitle);
            quoteArticleMo.setCitedJournalTitle(journalTitle);
            quoteArticleMo.setYear(jsonObject.getString("year"));
            quoteArticleMo.setVolume(jsonObject.getString("volume"));
            quoteArticleMo.setIssue(jsonObject.getString("issue"));
            quoteArticleMo.setIssn(jsonObject.getString("issn"));
            quoteArticleMo.setFirstPage(jsonObject.getString("firstPage"));
            quoteArticleMo.setLastPage(jsonObject.getString("lastPage"));
            quoteArticleMo.setPageRange(jsonObject.getString("pageRange"));
            // 被引
            quoteArticleMo.setCiteType("2");
            quoteArticleMo.setUpdateTime(new Date());
            quoteArticleMo.setCreateTime(new Date());
            this.quoteArticleMoService.save(quoteArticleMo);
        };
    }

    private void saveCitingArticle(String articleId, String articleTitle, String journalTitle, String doi, String firstPage, String lastPage, int citedCount) {
        QuoteArticleParamVo quoteArticleParamVo = new QuoteArticleParamVo();
        quoteArticleParamVo.setRefId(articleId);
        List<QuoteArticleMO> list = this.quoteArticleMoService.getQuoteArticleListByRefIds(quoteArticleParamVo);
        if (Objects.nonNull(list) && !list.isEmpty()) {
            return;
        }
        // TODO  保存施引信息
        QuoteArticleMO quoteArticleMo = new QuoteArticleMO();
        quoteArticleMo.setRefId(articleId);
        quoteArticleMo.setRefTitle(articleTitle);
        quoteArticleMo.setRefJournalTitle(journalTitle);
        // 施引
        quoteArticleMo.setCiteType("1");
        quoteArticleMo.setUpdateTime(new Date());
        quoteArticleMo.setCreateTime(new Date());
        quoteArticleMo.setFirstPage(firstPage);
        quoteArticleMo.setLastPage(lastPage);
        quoteArticleMo.setDoi(doi);
        quoteArticleMo.setCitedCount(String.valueOf(citedCount));
        this.quoteArticleMoService.save(quoteArticleMo);
    }
}
