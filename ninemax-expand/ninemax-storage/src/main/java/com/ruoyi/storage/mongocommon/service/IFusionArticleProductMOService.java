package com.ruoyi.storage.mongocommon.service;

import com.alibaba.fastjson2.JSONObject;
import com.anwen.mongo.model.PageParam;
import com.anwen.mongo.model.PageResult;
import com.anwen.mongo.service.IService;
import com.ruoyi.storage.mongocommon.domain.FusionArticleProductMO;
import com.ruoyi.storage.mongocommon.param.QueryFusionArticleProductMOReqVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IFusionArticleProductMOService extends IService<FusionArticleProductMO> {

    PageResult<FusionArticleProductMO> queryFusionArticleProduct(QueryFusionArticleProductMOReqVO vo, PageParam pageParam);

    List<JSONObject> queryArticlesByJournalId(String fusionJournalId);

    void deleteArticleByArticleId(String articleId);

    FusionArticleProductMO queryFusionArticleProductByArticleId(String articleId);
    /**
     * 根据文章id批量查询
     * @param articleIds
     * @return
     */
    List<FusionArticleProductMO> queryFusionArticleProductByArticleIds(List<String> articleIds);

    List<FusionArticleProductMO> queryFusionArticleProduct(String source, String journalId);

    List<FusionArticleProductMO> queryFusionArticleProductByDoi(String doi);

    boolean queryFusionArticlePubIdsByArticle(String articleId);

    Map<String, Object> queryFusionArticleProductByPage(QueryFusionArticleProductMOReqVO queryFusionArticleProductMoReqVo, PageParam pageParam);


    List<JSONObject> queryFusionArticleProduct(QueryFusionArticleProductMOReqVO queryFusionArticleProductMoReqVo);

}
