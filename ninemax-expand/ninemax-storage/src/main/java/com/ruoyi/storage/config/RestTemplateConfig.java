package com.ruoyi.storage.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class RestTemplateConfig {

    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(3000000);
        factory.setReadTimeout(6000000);

        RestTemplate restTemplate = new RestTemplate(factory);

        log.info("创建外部工具RestTemplate，连接超时: {}ms, 读取超时: {}ms", 3000000, 6000000);

        return restTemplate;
    }

}
