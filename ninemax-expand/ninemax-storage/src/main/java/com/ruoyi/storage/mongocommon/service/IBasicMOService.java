package com.ruoyi.storage.mongocommon.service;

import com.anwen.mongo.model.PageParam;
import com.anwen.mongo.model.PageResult;
import com.anwen.mongo.service.IService;
import com.ruoyi.storage.mongocommon.domain.BasicMO;
import com.ruoyi.storage.mongocommon.param.QueryBasicMOReqVO;
import com.ruoyi.storage.mongocommon.vo.ArticleDetailReqVO;
import com.ruoyi.storage.mongocommon.vo.ArticleDetailRespVO;

import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IBasicMOService extends IService<BasicMO> {

    PageResult<Map<String,Object>> queryBasicList(QueryBasicMOReqVO queryBasicReqVO, PageParam pageParam);

    ArticleDetailRespVO articleDetail(@Valid ArticleDetailReqVO articleDetailReqVO);

    void saveBasicData(BasicMO basicMO);
}
