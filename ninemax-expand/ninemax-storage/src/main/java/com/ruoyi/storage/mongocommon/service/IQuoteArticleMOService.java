package com.ruoyi.storage.mongocommon.service;

import com.anwen.mongo.model.PageParam;
import com.anwen.mongo.model.PageResult;
import com.anwen.mongo.service.IService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.mongocommon.domain.QuoteArticleMO;
import com.ruoyi.storage.mongocommon.param.QuoteArticleParamVo;

import java.util.List;
import java.util.Map;

public interface IQuoteArticleMOService extends IService<QuoteArticleMO> {

    /**
     * 获取文章ID引用文章列表
     * @param articleId 文章ID
     * @return
     */
    List<QuoteArticleMO> getQuoteArticlesByArticleId(String articleId);

    PageResult<QuoteArticleMO> getQuoteArticlesByPage(QuoteArticleParamVo quoteArticleParamVo, PageParam pageParam);

    /**
     * 批量删除
     * @param removeCitedId 删除的引用id
     */
    void deleteQuoteArticleByArticleId(String removeCitedId);

    /**
     * 更新引用文章
     * @param quoteArticleParamVo 参数
     */
    void updateQuoteArticle(QuoteArticleParamVo quoteArticleParamVo);

    /**
     * 获取引用文章列表
     * @param quoteArticleParamVo 参数(引用文章id数组refids)
     * @return 引用文章列表
     */
    List<QuoteArticleMO> getQuoteArticleListByRefIds(QuoteArticleParamVo quoteArticleParamVo);

    /**
     * 获取引用文章列表
     * @param quoteArticleParamVo 被引用文章id数组refids
     * @return 引用文章列表
     */
    PageResult<QuoteArticleMO> getQuoteArticleListByCitedIds(QuoteArticleParamVo quoteArticleParamVo, PageParam pageParam);

    List<QuoteArticleMO> getQuoteArticleListByDoi(String doi);

    void deleteByDoi(String doi);

    QuoteArticleMO getQuoteArticleDetail(String id);
}
