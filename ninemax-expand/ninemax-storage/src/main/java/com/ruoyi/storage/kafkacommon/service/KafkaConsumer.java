package com.ruoyi.storage.kafkacommon.service;

import com.ruoyi.storage.api.service.*;
import com.ruoyi.storage.escommon.service.IPublishDataService;
import com.ruoyi.storage.kafkacommon.domain.FlowLogVO;
import com.ruoyi.storage.kafkacommon.domain.KafkaRsyncVO;
import com.ruoyi.storage.mongocommon.domain.SingleJournalProductMO;
import com.ruoyi.storage.utils.Constants;
import com.ruoyi.storage.utils.EsBulkProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Kafka消息消费者
 */
@Slf4j
@Component
public class KafkaConsumer {

    @Autowired
    private AnalysisRuleService analysisRuleService;
    @Autowired
    private SingleArticleDeduplicateService singleArticleDeduplicateService;

    @Autowired
    private SingleJournalDeduplicateService singleJournalDeduplicateService;

    @Autowired
    private FusionArticleDeduplicateService fusionArticleDeduplicateService;

    @Autowired
    private FusionJournalDeduplicateService fusionJournalDeduplicateService;
    @Autowired
    private IPublishDataService publishBatchData;

    // 2. 创建批量处理器实例
    EsBulkProcessor bulkProcessor = new EsBulkProcessor(
            (object) -> {
                FlowLogVO log = (FlowLogVO) object;
                Map<String, Object> doc = new HashMap<>();
                doc.put("id", log.getId());
                doc.put("sourceId", log.getSourceId());
                doc.put("dataType", log.getDataType());
                doc.put("pId", log.getPId());
                doc.put("createTime", log.getCreateTime());
                doc.put("status",log.getStatus());
                // 其他字段...
                return doc;
            }
    );

    /**
     * 数据解析映射消费kafka
     */
    @KafkaListener(topics = Constants.KAFKA_PARSE_MAPPING, groupId = "${kafka.groupId}")
    public void consumeMessage(String batchId, Acknowledgment acknowledgment) {
        try {
            // 进行收割数据、上传数据的解析映射处理逻辑
            // todo 暂时先只传递batchId信息
            log.info("开始处理消息:类型：{}，参数：{}", Constants.KAFKA_PARSE_MAPPING, batchId);
            analysisRuleService.handleParsingMapping(batchId);
            // 确认消息已处理
            acknowledgment.acknowledge();
            log.info("消息处理完成并确认:类型：{}，参数：{}", Constants.KAFKA_PARSE_MAPPING, batchId);
        } catch (Exception e) {
            log.error("消息处理失败:类型：{}，参数：{}", Constants.KAFKA_PARSE_MAPPING, batchId, e);
            // 这里可以添加重试逻辑或错误处理
        }
    }

    /**
     * 单源篇级归一消费kafka
     */
    @KafkaListener(topics = Constants.KAFKA_SINGLE_ARTICLE_DEDUPCATE_MAPPING, groupId = "${kafka.groupId}")
    public void consumeSingleArticleDeduplicateMessage(String batchId, Acknowledgment acknowledgment) {
        try {
            // 进行收割数据、上传数据的解析映射处理逻辑
            // todo 暂时先只传递batchId信息
            log.info("开始处理消息:类型：{}，参数：{}", Constants.KAFKA_SINGLE_ARTICLE_DEDUPCATE_MAPPING, batchId);
            singleArticleDeduplicateService.deduplicate(batchId);
            // 确认消息已处理
            acknowledgment.acknowledge();
            log.info("消息处理完成并确认:类型：{}，参数：{}", Constants.KAFKA_SINGLE_ARTICLE_DEDUPCATE_MAPPING, batchId);
        } catch (Exception e) {
            log.error("消息处理失败:类型：{}，参数：{}", Constants.KAFKA_SINGLE_ARTICLE_DEDUPCATE_MAPPING, batchId, e);
            // 这里可以添加重试逻辑或错误处理
        }
    }

    /**
     * 单条流程日志消费kafka
     * 从kafka中读取单条数据并发布到ES
     */
    @KafkaListener(topics = Constants.KAFKA_LOGS, groupId = "${kafka.groupId}")
    public void consumeFlowLog(FlowLogVO flowLogVO, Acknowledgment acknowledgment) {
        try {
            //日志发送至ES
            bulkProcessor.add(flowLogVO);
            // 确认消息已处理
            acknowledgment.acknowledge();
        } catch (Exception e) {
            log.error("流程日志处理失败: {}", flowLogVO, e);
            // 这里可以添加重试逻辑或错误处理
        }
    }

    /**
     * 单源品种归一消费kafka
     */
    @KafkaListener(topics = Constants.KAFKA_SINGLE_JOURNAL_DEDUPCATE_MAPPING, groupId = "${kafka.groupId}")
    public void consumeSingleJournalDeduplicateMessage(SingleJournalProductMO singleJournalProductMO, Acknowledgment acknowledgment) {
        try {
            // 进行收割数据、上传数据的解析映射处理逻辑
            // todo 暂时先只传递batchId信息
            log.info("开始处理消息:类型：{}，参数：{}", Constants.KAFKA_SINGLE_JOURNAL_DEDUPCATE_MAPPING, singleJournalProductMO.getSingleJournalId());
            singleJournalDeduplicateService.deduplicate(singleJournalProductMO);
            // 确认消息已处理
            acknowledgment.acknowledge();
            log.info("消息处理完成并确认:类型：{}，参数：{}", Constants.KAFKA_SINGLE_JOURNAL_DEDUPCATE_MAPPING, singleJournalProductMO.getSingleJournalId());
        } catch (Exception e) {
            log.error("消息处理失败:类型：{}，参数：{}", Constants.KAFKA_SINGLE_JOURNAL_DEDUPCATE_MAPPING, singleJournalProductMO.getSingleJournalId(), e);
            // 这里可以添加重试逻辑或错误处理
        }
    }

    /**
     * 多源篇级归一消费kafka
     */
    @KafkaListener(topics = Constants.KAFKA_FUSION_ARTICLE_DEDUPCATE_MAPPING, groupId = "${kafka.groupId}")
    public void consumeFusionArticleDeduplicateMessage(String batchId, Acknowledgment acknowledgment) {
        try {
            // 进行收割数据、上传数据的解析映射处理逻辑
            // todo 暂时先只传递batchId信息
            log.info("开始处理消息:类型：{}，参数：{}", Constants.KAFKA_FUSION_ARTICLE_DEDUPCATE_MAPPING, batchId);
            fusionArticleDeduplicateService.deduplicate(batchId);
            // 确认消息已处理
            acknowledgment.acknowledge();
            log.info("消息处理完成并确认:类型：{}，参数：{}", Constants.KAFKA_FUSION_ARTICLE_DEDUPCATE_MAPPING, batchId);
        } catch (Exception e) {
            log.error("消息处理失败:类型：{}，参数：{}", Constants.KAFKA_FUSION_ARTICLE_DEDUPCATE_MAPPING, batchId, e);
            // 这里可以添加重试逻辑或错误处理
        }
    }

    /**
     * 多源品种归一消费kafka
     */
    @KafkaListener(topics = Constants.KAFKA_FUSION_JOURNAL_DEDUPCATE_MAPPING, groupId = "${kafka.groupId}")
    public void consumeFusionJournalDeduplicateMessage(String batchId, Acknowledgment acknowledgment) {
        try {
            // 进行收割数据、上传数据的解析映射处理逻辑
            // todo 暂时先只传递batchId信息
            log.info("开始处理消息:类型：{}，参数：{}", Constants.KAFKA_FUSION_JOURNAL_DEDUPCATE_MAPPING, batchId);
            fusionJournalDeduplicateService.deduplicate(batchId);
            // 确认消息已处理
            acknowledgment.acknowledge();
            log.info("消息处理完成并确认:类型：{}，参数：{}", Constants.KAFKA_FUSION_JOURNAL_DEDUPCATE_MAPPING, batchId);
        } catch (Exception e) {
            log.error("消息处理失败:类型：{}，参数：{}", Constants.KAFKA_FUSION_JOURNAL_DEDUPCATE_MAPPING, batchId, e);
            // 这里可以添加重试逻辑或错误处理
        }
    }

    /**
     * 多源品种归一消费kafka
     */
    @KafkaListener(topics = Constants.KAFKA_FUSION_MAPPING, groupId = "${kafka.groupId}")
    public void consumeFusionMessage(String batchId, Acknowledgment acknowledgment) {
        try {
            // 进行收割数据、上传数据的解析映射处理逻辑
            // todo 暂时先只传递batchId信息
            log.info("开始处理消息:类型：{}，参数：{}", Constants.KAFKA_FUSION_MAPPING, batchId);
            fusionJournalDeduplicateService.deduplicate(batchId);
            // 确认消息已处理
            acknowledgment.acknowledge();
            log.info("消息处理完成并确认:类型：{}，参数：{}", Constants.KAFKA_FUSION_MAPPING, batchId);
        } catch (Exception e) {
            log.error("消息处理失败:类型：{}，参数：{}", Constants.KAFKA_FUSION_MAPPING, batchId, e);
            // 这里可以添加重试逻辑或错误处理
        }
    }

    /**
     * MongoDB保存后，执行同步数据到es
     */
    @KafkaListener(topics = Constants.KAFKA_RSYNC_ES, groupId = "${kafka.groupId}")
    public void kafkaRsync(KafkaRsyncVO vo, Acknowledgment acknowledgment) {
        try {
            log.info("开始处理消息: {}", vo);
            // 同步数据到es
            publishBatchData.publishData(vo);
            // 确认消息已处理
            acknowledgment.acknowledge();
            log.info("消息处理完成并确认: {}", vo);
        } catch (Exception e) {
            log.error("消息处理失败: {}", vo, e);
        }
    }

}
