package com.ruoyi.storage.processingtool.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.storage.api.vo.ListSelect;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.processingtool.domain.RuleBase;
import com.ruoyi.storage.processingtool.mapper.RuleBaseMapper;
import com.ruoyi.storage.processingtool.service.IRuleBaseService;
import com.ruoyi.storage.processingtool.service.impl.RuleBaseServiceImpl;
import com.ruoyi.storage.processingtool.vo.AddRuleBaseReqVO;
import com.ruoyi.storage.processingtool.vo.ExportRuleBaseVO;
import com.ruoyi.storage.processingtool.vo.QueryRuleBaseSelectReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ruleBase")
@Validated
public class RuleBaseController extends BaseController<RuleBaseMapper, RuleBase> {
    RuleBaseController(RuleBaseServiceImpl service) {
        super(service, RuleBase.class);
    }

    @Autowired
    private IRuleBaseService ruleBaseService;

    /**
     * 新增数据
     *
     * @param addRuleBaseReqVO 添加数据请求参数
     * @return 操作结果
     */
    @PostMapping("/add")
    public R<?> addRuleBase(@Valid @RequestBody AddRuleBaseReqVO addRuleBaseReqVO) {
        ruleBaseService.addRuleBase(addRuleBaseReqVO);
        return R.ok();
    }

    /**
     * 修改数据
     *
     * @param ruleBase 实体对象
     * @return 操作结果
     */
    @Override
    @Log(title = "解析规则-修改", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<?> edit(@Valid @RequestBody RuleBase ruleBase) {
        ruleBaseService.updateRuleBase(ruleBase);
        return R.ok();
    }

    /**
     * 重新发送规则解析请求
     *
     * @param ruleId 规则ID
     * @return 操作结果
     */
    @PostMapping("/resendRuleBase/{ruleId}")
    public R<?> resendRuleBase(@PathVariable("ruleId") String ruleId) {
        ruleBaseService.resendRuleBase(ruleId);
        return R.ok();
    }

    @GetMapping("/querySelectList")
    public R<List<ListSelect>> querySelectList(QueryRuleBaseSelectReqVO queryRuleBaseSelectReqVO) {
        return R.ok(ruleBaseService.querySelectList(queryRuleBaseSelectReqVO));
    }

    /**
     * 导出数据
     *
     * @param response HTTP响应对象
     * @param ruleBase 查询条件实体
     */
    @Log(title = "通用-导出", businessType = BusinessType.EXPORT)
    @Override
    @PostMapping("/export")
    public void export(HttpServletResponse response, RuleBase ruleBase) {
        try {
            List<RuleBase> list = service.queryList(ruleBase);
            // 进行数据转换处理
            list.stream().map(item -> {
                return ExportRuleBaseVO.builder()
                        .name(item.getName())
                        .sourceId(item.getSourceId())
                        .docType(item.getDocType())
                        .ruleType(item.getRuleType())
                        .useStatus(item.getUseStatus())
                        .ruleStatus(item.getRuleStatus())
                        .toolStatus(item.getToolStatus())
                        .description(item.getDescription())
                        .phase(item.getPhase())
            }).toList();
            ExcelUtil<ExportRuleBaseVO> util = new ExcelUtil<>(ExportRuleBaseVO.class);
            util.exportExcel(response, list, "sheet1");
        } catch (Exception e) {
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

}
