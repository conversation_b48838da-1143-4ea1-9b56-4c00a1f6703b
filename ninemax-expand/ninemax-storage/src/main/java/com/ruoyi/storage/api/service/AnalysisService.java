package com.ruoyi.storage.api.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.ruoyi.storage.api.RemoteExFileService;
import com.ruoyi.storage.api.domain.*;
import com.ruoyi.storage.processingtool.domain.RuleAnalysis;
import com.ruoyi.storage.processingtool.domain.RuleAnalysisValidation;
import com.ruoyi.storage.processingtool.domain.RuleBase;
import com.ruoyi.storage.processingtool.dto.*;
import com.ruoyi.storage.processingtool.service.IRuleAnalysisService;
import com.ruoyi.storage.processingtool.service.IRuleAnalysisValidationService;
import com.ruoyi.storage.processingtool.service.IRuleBaseService;
import com.ruoyi.storage.utils.RestUtils;
import com.ruoyi.storage.utils.ResultConversionUtil;
import feign.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 解析映射数据处理服务
 *
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class AnalysisService {

    private final RestUtils restUtils;
    private final IRuleBaseService ruleBaseService;
    private final ResultConversionUtil resultConversionUtil;
    private final IRuleAnalysisValidationService ruleAnalysisValidationService;
    private final IRuleAnalysisService ruleAnalysisService;
    private final RemoteExFileService remoteExFileService;

    @Value("${storage.processingTool.tempDir}")
    private String tempDir;
    @Value("${storage.tool.analysis_url}")
    private String analysisUrl;

    /**
     * 工具接口调用 ==> 当前接口处理使用异步方式调用请求数据
     *
     * @param handleAnalysisToolDTO 解析映射数据封装类
     */
    public void toolInterfaceCall(HandleRuleAnalysisToolDTO handleAnalysisToolDTO) {
        List<File> files = Collections.emptyList();
        try {
            log.info("解析工具开始调用 ==> 线程名称：{}", Thread.currentThread().getName());
            ParsingMappingReq req = new ParsingMappingReq();

            // 效验文件信息
            RuleAnalysisValidation ruleAnalysisValidation = handleAnalysisToolDTO.getRuleAnalysisValidation();
            String filePath = ruleAnalysisValidation.getFilePath();
            Assert.isFalse(StringUtils.isBlank(filePath), "规则效验文件处理失败==>文件路径为空!");
            String fileName = ruleAnalysisValidation.getFileName();
            files = handleToolRuleFile(filePath, fileName);
            Assert.isFalse(files.isEmpty(), "规则效验文件处理失败==>文件不存在!");
            //添加文件
            req.setFiles(files);
            //添加info
            req.setInfo(RuleInfo.builder()
                    .ruleId(handleAnalysisToolDTO.getRuleBase().getId())
                    .sourceId(handleAnalysisToolDTO.getRuleBase().getSourceId())
                    .sourceId("J")
                    .docType(handleAnalysisToolDTO.getRuleBase().getDocType())
                    .build());
            //添加checks
            List<FileChecks> checksList = new ArrayList<>();
            checksList.add(FileChecks.builder()
                    .fileName(ruleAnalysisValidation.getFileName())
                    .md5(ruleAnalysisValidation.getMd5())
                    .build());
            req.setChecks(checksList);

            // 效验规则信息
            if (!handleAnalysisToolDTO.isInitFlag()) {
                // 获取当前解析映射规则规则关联文件信息
                LambdaQueryWrapper<RuleAnalysisValidation> validationLambdaQueryWrapper = new LambdaQueryWrapper<RuleAnalysisValidation>().eq(RuleAnalysisValidation::getRuleId, handleAnalysisToolDTO.getRuleBase().getId())
                        .eq(RuleAnalysisValidation::getType, 0).last("LIMIT 1");
                RuleAnalysisValidation analysisValidation = ruleAnalysisValidationService.getOne(validationLambdaQueryWrapper);

                LambdaQueryWrapper<RuleAnalysis> analysisLambdaQueryWrapper = new LambdaQueryWrapper<>();
                analysisLambdaQueryWrapper.eq(RuleAnalysis::getRuleId, analysisValidation.getRuleId()).eq(RuleAnalysis::getRuleValidId, analysisValidation.getId());
                List<RuleAnalysis> ruleAnalysisList = ruleAnalysisService.list(analysisLambdaQueryWrapper);
                List<AnalysisMappingRule> rules = ruleAnalysisList.stream()
                        .filter(item -> !StringUtils.endsWithIgnoreCase("H", item.getStatus()))
                        .map(item -> AnalysisMappingRule.builder()
                                .imiPath(item.getImiPath())
                                .status(item.getStatus())
                                .pathId(item.getPathId())
                                .iskey(item.getIskey())
                                .sort(item.getSort())
                                .analysisStatus(item.getAnalysisStatus())
                                .sampleValue(StringUtils.isNotBlank(item.getSampleValue()) ? Arrays.asList(item.getSampleValue().split(",")) : Collections.emptyList())
                                .samplePath(StringUtils.isNotBlank(item.getSamplePath()) ? Arrays.asList(item.getSamplePath().split(",")) : Collections.emptyList())
                                .frequency(item.getFrequency())
                                .srcPath(item.getSrcPath())
                                .build()).toList();
                req.setRules(rules);
            }
            // 2、使用工具类调用外部接口
            String result = restUtils.analysis(analysisUrl, req);

            handleAnalysisToolDTO.setResult(result);
            // 3、接口调用成功后更新状态和保存数据
            updateAfterSuccess(handleAnalysisToolDTO);
        } catch (Exception e) {
            // 处理异常情况，更新为失败状态
            log.info("工具调用异常：{}", e.getMessage());
            handleAnalysisToolDTO.setMessage("工具调用异常" + e.getMessage());
            updateAfterFailure(handleAnalysisToolDTO);
        } finally {
            // 删除临时文件信息
            files.forEach(FileUtils::deleteQuietly);
        }
    }

    /**
     * 处理规则文件
     *
     * @param filePath 文件路径
     * @param fileName 文件名称
     * @return 文件列表
     */
    private List<File> handleToolRuleFile(String filePath, String fileName) {
        //将文件拷贝到临时文件夹中
        File targetFile = null;
        try (Response response = remoteExFileService.resourceDownload(filePath);
             InputStream is = response.body().asInputStream()) {

            //获取文件对象
            //这里使用uuid进行相同文件名的区分，防止进行文件的覆盖(目标文件)
            String tempFilePath = tempDir + File.separator + StrUtil.uuid() + File.separator + fileName;

            targetFile = new File(tempFilePath);
            //将输入流复制到临时文件夹的目标文件路径下
            FileUtils.copyInputStreamToFile(is, targetFile);
            //获取文件大小
            long length = targetFile.length();
            if (length == 0) {
                //如果没有该文件
                log.info("【文件下载失败】 ==> 文件不存在，文件路径：{}", filePath);
            }
            return List.of(targetFile);
        } catch (Exception e) {
            // 失败时尝试清理临时文件
            if (targetFile != null && targetFile.exists()) {
                FileUtils.deleteQuietly(targetFile);
            }
            log.error("【文件下载失败】 ==> 未知错误 | 文件路径：{} | 错误信息==> {}", filePath, e.getMessage());
            throw new RuntimeException("【文件下载失败】 ==> 未知错误 " + e.getMessage());
        }
    }

    /**
     * 工具调用失败更新状态和保存数据
     *
     * @param handleAnalysisToolDTO 解析映射规则对象
     */
    private void updateAfterFailure(HandleRuleAnalysisToolDTO handleAnalysisToolDTO) {
        log.info("工具调用失败：{}", handleAnalysisToolDTO.getMessage());
        // 更新相关对象状态信息
        RuleBase ruleBase = handleAnalysisToolDTO.getRuleBase();
        RuleAnalysisValidation ruleAnalysisValidation = handleAnalysisToolDTO.getRuleAnalysisValidation();
        if (handleAnalysisToolDTO.isInitFlag()) {
            ruleBase.setRuleStatus("COMPLETED");
            ruleBase.setToolStatus("INIT_FAIL");
            ruleBaseService.updateById(ruleBase);
        }
        ruleAnalysisValidation.setStatus("FAILURE");
        ruleAnalysisValidation.setDescription(handleAnalysisToolDTO.getMessage());
        ruleAnalysisValidationService.updateById(ruleAnalysisValidation);
    }

    /**
     * 工具调用成功后更新状态和保存数据
     *
     * @param handleAnalysisToolDTO 工具调用结果
     */
    private void updateAfterSuccess(HandleRuleAnalysisToolDTO handleAnalysisToolDTO) {
        log.info("工具调用成功：{}", handleAnalysisToolDTO.getMessage());
        // 1、更新相关对象状态信息

        RuleBase ruleBase = handleAnalysisToolDTO.getRuleBase();
        RuleAnalysisValidation ruleAnalysisValidation = handleAnalysisToolDTO.getRuleAnalysisValidation();
        if (handleAnalysisToolDTO.isInitFlag()) {
            ruleBase.setRuleStatus("PENDING");
            ruleBase.setToolStatus("INIT_SUCCESS");
            ruleBaseService.updateById(ruleBase);
        }
        ruleAnalysisValidation.setStatus("SUCCESS");
        ruleAnalysisValidation.setDescription(handleAnalysisToolDTO.getMessage());
        ruleAnalysisValidationService.updateById(ruleAnalysisValidation);

        // 2、处理工具接口返回的数据结果
        ParsingMappingResp resp;
        try {
            String result = handleAnalysisToolDTO.getResult();
            resp = resultConversionUtil.convertToRespDTO(result);
            ruleAnalysisService.handleResultData(resp, ruleBase.getId(), ruleBase.getName(), ruleAnalysisValidation.getId());
        } catch (Exception e) {
            log.info("工具返回数据转换异常：{}", e.getMessage());
            handleAnalysisToolDTO.setMessage(e.getMessage());
            updateAfterFailure(handleAnalysisToolDTO);
        }

    }

}
