package com.ruoyi.storage.mongocommon.service;

import com.anwen.mongo.service.IService;
import com.ruoyi.storage.mongocommon.domain.SingleArticlePrepareMO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ISingleArticlePrepareMOService extends IService<SingleArticlePrepareMO> {

    /**
     * 根据原始文章id查询单篇文章准备信息
     *
     * @param originalArticleId 原始文章id
     * @param batchId           批次id
     * @return
     */
    SingleArticlePrepareMO getSingleArticlePrepareByOriginalArticleId(String originalArticleId, String batchId);

    /**
     * 根据来源id查询单篇文章准备信息
     *
     * @param sourceId 来源id
     * @return
     */
    List<SingleArticlePrepareMO> getSingleArticlePrepareListBySourceId(String sourceId);

    List<SingleArticlePrepareMO> getByBatchId(String batchId);

    void updataSusLink(SingleArticlePrepareMO singleArticlePrepareMO);

    void saveData(SingleArticlePrepareMO singleArticlePrepareMO);

    void updateByOriginalArticleId(SingleArticlePrepareMO mo);
}
