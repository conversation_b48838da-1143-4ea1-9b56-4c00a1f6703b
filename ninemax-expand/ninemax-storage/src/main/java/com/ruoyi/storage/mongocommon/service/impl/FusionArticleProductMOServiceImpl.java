package com.ruoyi.storage.mongocommon.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.anwen.mongo.conditions.query.LambdaQueryChainWrapper;
import com.anwen.mongo.conditions.query.QueryWrapper;
import com.anwen.mongo.conditions.update.LambdaUpdateChainWrapper;
import com.anwen.mongo.model.PageParam;
import com.anwen.mongo.model.PageResult;
import com.anwen.mongo.service.impl.ServiceImpl;
import com.ruoyi.storage.mongocommon.domain.FusionArticleProductMO;
import com.ruoyi.storage.mongocommon.domain.QuoteArticleMO;
import com.ruoyi.storage.mongocommon.param.QueryFusionArticleProductMOReqVO;
import com.ruoyi.storage.mongocommon.service.IFusionArticleProductMOService;
import com.ruoyi.storage.mongocommon.service.IQuoteArticleMOService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FusionArticleProductMOServiceImpl extends ServiceImpl<FusionArticleProductMO> implements IFusionArticleProductMOService {

    @Resource
    private IQuoteArticleMOService quoteArticleMoService;

    @Override
    public PageResult<FusionArticleProductMO> queryFusionArticleProduct(QueryFusionArticleProductMOReqVO vo, PageParam pageParam) {
        LambdaQueryChainWrapper<FusionArticleProductMO> lambdaQueryChainWrapper = this.lambdaQuery()
                .in(FusionArticleProductMO::getSources, vo.getSoruces());
        if (StringUtils.isNotEmpty(vo.getFusionJournalId())) {
            lambdaQueryChainWrapper.eq(FusionArticleProductMO::getFusionJournalId, vo.getFusionJournalId());
        }
        return this.page(lambdaQueryChainWrapper, pageParam);
    }

    @Override
    public List<JSONObject> queryArticlesByJournalId(String fusionJournalId) {
        LambdaQueryChainWrapper<FusionArticleProductMO> lambdaQueryChainWrapper = this.lambdaQuery().eq(FusionArticleProductMO::getFusionJournalId, fusionJournalId);
        List<FusionArticleProductMO> articleProductMoList = lambdaQueryChainWrapper.list();
        return articleProductMoList.stream().map(articleProductMo -> {
            Map<String, Object> information = this.getArticleInformation(articleProductMo);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("articleId", articleProductMo.getFusionArticleId());
            jsonObject.put("articleTitle", information.get("articleTitle").toString());
            jsonObject.put("journalTitle", information.get("journalTitle").toString());
            jsonObject.put("journalId", articleProductMo.getFusionJournalId());
            jsonObject.put("issn", information.get("issn"));
            jsonObject.put("year", information.get("year"));
            jsonObject.put("volume", information.get("volume"));
            jsonObject.put("issue", information.get("issue"));
            if (information.get("pageRange") != null){
                jsonObject.put("pageRange", information.get("pageRange"));
            }else {
                jsonObject.put("pageRange", information.get("firstPage") + "-" + information.get("lastPage"));
            }
            jsonObject.put("citeCount", information.get("citeCount"));
            return jsonObject;
        }).toList();
    }

    @Override
    public void deleteArticleByArticleId(String articleId) {
        LambdaUpdateChainWrapper<FusionArticleProductMO> lambdaUpdateChainWrapper = this.lambdaUpdate()
                .eq(FusionArticleProductMO::getSingleArticleIds, articleId);
                lambdaUpdateChainWrapper.remove();
    }

    @Override
    public FusionArticleProductMO queryFusionArticleProductByArticleId(String articleId) {
        log.info("查询文章：{}", articleId);
        // LambdaQueryChainWrapper<FusionArticleProductMO> lambdaQueryChainWrapper = this.lambdaQuery().eq(FusionArticleProductMO::getSingleArticleIds, articleId);
        //return lambdaQueryChainWrapper.one();
        QueryWrapper<FusionArticleProductMO> queryWrapper = new QueryWrapper<>();
        queryWrapper.elemMatch("id_list",
                new QueryWrapper<FusionArticleProductMO>().eq("id", articleId));
        return this.one(queryWrapper);
    }

    @Override
    public List<FusionArticleProductMO> queryFusionArticleProductByArticleIds(List<String> articleIds) {
        LambdaQueryChainWrapper<FusionArticleProductMO> lambdaQueryChainWrapper = this.lambdaQuery().in(FusionArticleProductMO::getSingleArticleIds, articleIds);
        return lambdaQueryChainWrapper.list();
    }

    @Override
    public List<FusionArticleProductMO> queryFusionArticleProduct(String source, String journalId) {
        LambdaQueryChainWrapper<FusionArticleProductMO> lambdaQueryChainWrapper = this.lambdaQuery()
                .eq(FusionArticleProductMO::getFusionJournalId, journalId).in(FusionArticleProductMO::getSources, Collections.singletonList(source));
        return lambdaQueryChainWrapper.list();
    }

    @Override
    public List<FusionArticleProductMO> queryFusionArticleProductByDoi(String doi) {
        log.info("查询DOI：{}", doi);
        QueryWrapper<FusionArticleProductMO> queryWrapper = new QueryWrapper<>();
        queryWrapper.elemMatch("record",
                new QueryWrapper<FusionArticleProductMO>().elemMatch("article-meta",
                        new QueryWrapper<FusionArticleProductMO>().elemMatch("article-id",
                                new QueryWrapper<FusionArticleProductMO>().eq("_text", doi).eq("@pub-id-type", "doi"))));
        return this.list(queryWrapper);
    }

    @Override
    public boolean queryFusionArticlePubIdsByArticle(String articleId) {
        QueryWrapper<FusionArticleProductMO> queryWrapper = new QueryWrapper<>();
        queryWrapper.elemMatch("record",
                new QueryWrapper<FusionArticleProductMO>().elemMatch("article-meta",
                        new QueryWrapper<FusionArticleProductMO>().elemMatch("article-id",
                                new QueryWrapper<FusionArticleProductMO>().eq("_text", articleId).eq("@pub-id-type", "doi"))));
        FusionArticleProductMO fusionArticleProductMo = this.one(queryWrapper);
        return !Objects.isNull(fusionArticleProductMo);
    }

    @Override
    public Map<String, Object> queryFusionArticleProductByPage(QueryFusionArticleProductMOReqVO queryFusionArticleProductMoReqVo, PageParam pageParam) {
        Map<String, Object> resultMap = new HashMap<>();
        QueryWrapper<FusionArticleProductMO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(FusionArticleProductMO::getFusionJournalId, queryFusionArticleProductMoReqVo.getJournalIds());
        // TODO 关联状态为空的时候，默认查询全部状态
        if (StringUtils.isBlank(queryFusionArticleProductMoReqVo.getRelateStatus())){
            if (StringUtils.isNotBlank(queryFusionArticleProductMoReqVo.getDoi())){
                queryWrapper.elemMatch("record",
                        new QueryWrapper<FusionArticleProductMO>().elemMatch("article-meta",
                                new QueryWrapper<FusionArticleProductMO>().elemMatch("article-id",
                                        new QueryWrapper<FusionArticleProductMO>().eq("_text", queryFusionArticleProductMoReqVo.getDoi()).eq("@pub-id-type", "doi"))));
            }
            if (StringUtils.isNotBlank(queryFusionArticleProductMoReqVo.getArticleTitle())){
                queryWrapper.elemMatch("record",
                        new QueryWrapper<FusionArticleProductMO>().elemMatch("article-meta",
                                new QueryWrapper<FusionArticleProductMO>().elemMatch("article-id",
                                        new QueryWrapper<FusionArticleProductMO>().like("article-title", queryFusionArticleProductMoReqVo.getArticleTitle()))));
            }
            if (StringUtils.isNotBlank(queryFusionArticleProductMoReqVo.getJournalTitle())){
                queryWrapper.elemMatch("record",
                        new QueryWrapper<FusionArticleProductMO>().elemMatch("source-meta",
                                new QueryWrapper<FusionArticleProductMO>().elemMatch("source-title-group",
                                        new QueryWrapper<FusionArticleProductMO>().eq("source-title", queryFusionArticleProductMoReqVo.getJournalTitle()))));
            }
            PageResult<FusionArticleProductMO> pageResult = this.page(queryWrapper, pageParam);
            List<JSONObject> jsonObjectList = new ArrayList<>();
            for (FusionArticleProductMO fusionArticleProductMo : pageResult.getContentData()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("articleId", fusionArticleProductMo.getFusionArticleId());
                Map<String,  Object> articleInformation = getArticleInformation(fusionArticleProductMo);
                String doi = Optional.ofNullable(articleInformation.get("doi")).orElse("").toString();
                String articleTitle = Optional.ofNullable(articleInformation.get("articleTitle")).orElse("").toString();
                String sourceTile = Optional.ofNullable(articleInformation.get("sourceTile")).orElse("").toString();
                List<QuoteArticleMO> quoteArticleMoList = quoteArticleMoService.getQuoteArticleListByDoi(doi);
                if (quoteArticleMoList != null && !quoteArticleMoList.isEmpty()) {
                    boolean flag = existsPubIdInRecord(fusionArticleProductMo);
                    if (!flag) {
                        jsonObject.put("relateStatus", "关联不上");
                    }else{
                        jsonObject.put("relateStatus", "更新数据");
                    }
                }else{
                    jsonObject.put("relateStatus", "正常");
                }
                jsonObject.put("doi", doi);
                jsonObject.put("articleTitle", articleTitle);
                jsonObject.put("sourceTile", sourceTile);
                jsonObjectList.add(jsonObject);
            }
            resultMap.put("totalSize", pageResult.getTotalSize());
            resultMap.put("totalPages", pageResult.getTotalPages());
            resultMap.put("contentData", jsonObjectList);
        }else{
            List<JSONObject> jsonObjectList = new ArrayList<>();
            List<FusionArticleProductMO> lists = this.list(queryWrapper);
            for (FusionArticleProductMO fusionArticleProductMo : lists) {
                Map<String,  Object> articleInformation = getArticleInformation(fusionArticleProductMo);
                articleInformation.put("articleId", fusionArticleProductMo.getFusionArticleId());
                String doi = Optional.ofNullable(articleInformation.get("doi")).orElse("").toString();
                List<QuoteArticleMO> quoteArticleMoList = quoteArticleMoService.getQuoteArticleListByDoi(doi);
                boolean flag = existsPubIdInRecord(fusionArticleProductMo);
                if (StringUtils.equals("正常", queryFusionArticleProductMoReqVo.getRelateStatus())){
                    // TODO 引文库中查不到数据
                    if (Objects.isNull(quoteArticleMoList) || quoteArticleMoList.isEmpty()){
                        JSONObject normalJson = JSONObject.parseObject(JSON.toJSONString(articleInformation));
                        normalJson.put("relateStatus", "正常");
                        jsonObjectList.add(normalJson);
                    }
                }else if (StringUtils.equals("关联不上", queryFusionArticleProductMoReqVo.getRelateStatus())){
                    // TODO 关联不上  引文库中存在数据融合库中没有Pub-id
                    if (!flag && (Objects.isNull(quoteArticleMoList) || quoteArticleMoList.isEmpty())){
                        JSONObject notRelateJson = JSONObject.parseObject(JSON.toJSONString(articleInformation));
                        notRelateJson.put("relateStatus", "关联不上");
                        jsonObjectList.add(notRelateJson);
                    }
                }else if(StringUtils.equals("更新数据", queryFusionArticleProductMoReqVo.getRelateStatus())){
                    // TODO 更新数据 引文库中存在数据融合库中有Pub-id
                    if (flag && (Objects.nonNull(quoteArticleMoList) && !quoteArticleMoList.isEmpty())){
                        JSONObject updateJson = JSONObject.parseObject(JSON.toJSONString(articleInformation));
                        updateJson.put("relateStatus", "更新数据");
                        jsonObjectList.add(updateJson);
                    }
                }
            }
            resultMap.put("contentData", jsonObjectList.stream().skip(pageParam.getPageNum()).limit(pageParam.getPageSize()).toList());
        }
        return resultMap;
    }

    @Override
    public List<JSONObject> queryFusionArticleProduct(QueryFusionArticleProductMOReqVO queryFusionArticleProductMoReqVo) {
        QueryWrapper<FusionArticleProductMO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(FusionArticleProductMO::getFusionJournalId, queryFusionArticleProductMoReqVo.getFusionJournalId());
        if (StringUtils.isNotBlank(queryFusionArticleProductMoReqVo.getDoi())){
            queryWrapper.elemMatch("record",
                    new QueryWrapper<FusionArticleProductMO>().elemMatch("article-meta",
                            new QueryWrapper<FusionArticleProductMO>().elemMatch("article-id",
                                    new QueryWrapper<FusionArticleProductMO>().eq("_text", queryFusionArticleProductMoReqVo.getDoi()).eq("@pub-id-type", "doi"))));
        }
        if (StringUtils.isNotBlank(queryFusionArticleProductMoReqVo.getArticleTitle())){
            queryWrapper.elemMatch("record",
                    new QueryWrapper<FusionArticleProductMO>().elemMatch("article-meta",
                            new QueryWrapper<FusionArticleProductMO>().elemMatch("article-id",
                                    new QueryWrapper<FusionArticleProductMO>().like("article-title", queryFusionArticleProductMoReqVo.getArticleTitle()))));
        }
        if (StringUtils.isNotBlank(queryFusionArticleProductMoReqVo.getJournalTitle())){
            queryWrapper.elemMatch("record",
                    new QueryWrapper<FusionArticleProductMO>().elemMatch("source-meta",
                            new QueryWrapper<FusionArticleProductMO>().elemMatch("source-title-group",
                                    new QueryWrapper<FusionArticleProductMO>().eq("source-title", queryFusionArticleProductMoReqVo.getJournalTitle()))));
        }
        List<FusionArticleProductMO> list = this.list(queryWrapper);
        List<JSONObject> jsonObjectList = new ArrayList<>();
        for (FusionArticleProductMO fusionArticleProductMo : list) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("fusionArticleId", fusionArticleProductMo.getFusionArticleId());
            String doi = "";
            String articleTitle = "";
            String sourceTile = "";
            List<Map<String, Object>> record = fusionArticleProductMo.getRecord();
            for (Map<String, Object> map : record){
                List<Map<String, Object>> articleMeta = (List<Map<String, Object>>)map.get("article-meta");
                for (Map<String, Object> map1 : articleMeta){
                    List<Map<String, Object>> titleGroup = (List<Map<String, Object>>)map1.get("title-group");
                    for (Map<String, Object> map2 : titleGroup){
                        List<Map<String, Object>> title = (List<Map<String, Object>>)map2.get("article-title");
                        for (Map<String, Object> map3 : title){
                            articleTitle = Objects.toString(map3.get("_text"), "");
                        }
                    }
                    List<Map<String, Object>> articleId = (List<Map<String, Object>>)map1.get("article-id");
                    for (Map<String, Object> map2 : articleId){
                        String pubIdType = Objects.toString(map2.get("@pub-id-type"), "");
                        if (StringUtils.equalsIgnoreCase(pubIdType, "doi")){
                            doi = Objects.toString(map2.get("_text"), "");
                        }
                    }
                }
                List<Map<String, Object>> sourceMeta = (List<Map<String, Object>>)map.get("source-meta");
                for (Map<String, Object> map1 : sourceMeta){
                    List<Map<String, Object>> sourceTitleGroup = (List<Map<String, Object>>)map1.get("source-title-group");
                    for (Map<String, Object> map2 : sourceTitleGroup){
                        sourceTile = Objects.toString(map2.get("source-title"), "");
                    }
                }
            }
            List<QuoteArticleMO> quoteArticleMoList = quoteArticleMoService.getQuoteArticleListByDoi(doi);
            if (quoteArticleMoList != null && !quoteArticleMoList.isEmpty()) {
                jsonObject.put("relateStatus", "正常");
            }else{
                jsonObject.put("relateStatus", "关联不上");
            }
            if (StringUtils.equals("正常", queryFusionArticleProductMoReqVo.getRelateStatus())){
                jsonObject.put("doi", doi);
                jsonObject.put("articleTitle", articleTitle);
                jsonObject.put("sourceTile", sourceTile);
            }else if (StringUtils.equals("关联不上", queryFusionArticleProductMoReqVo.getRelateStatus())){
                // TODO 关联不上
                jsonObject.put("doi", doi);
                jsonObject.put("articleTitle", articleTitle);
                jsonObject.put("sourceTile", sourceTile);
            }else if(StringUtils.equals("更新数据", queryFusionArticleProductMoReqVo.getRelateStatus())){
                // TODO 更新数据
                jsonObject.put("doi", doi);
                jsonObject.put("articleTitle", articleTitle);
                jsonObject.put("sourceTile", sourceTile);
            }
            jsonObjectList.add(jsonObject);
        }
        return jsonObjectList;
    }

    private boolean existsPubIdInRecord(FusionArticleProductMO fusionArticleProductMo) {
        List<Map<String, Object>> record = fusionArticleProductMo.getRecord();
        if (record == null) {
            return false;
        }
        for (Map<String, Object> recordItem : record) {
            // 检查 ref-list 是否存在
            Object refListObj = recordItem.get("ref-list");
            if (refListObj instanceof List) {
                List<?> refList = (List<?>) refListObj;
                for (Object refListItem : refList) {
                    if (refListItem instanceof Map) {
                        Map<String, Object> refListMap = (Map<String, Object>) refListItem;
                        // 检查 ref 是否存在
                        Object refObj = refListMap.get("ref");
                        if (refObj instanceof List) {
                            List<?> ref = (List<?>) refObj;
                            for (Object refItem : ref) {
                                if (refItem instanceof Map) {
                                    Map<String, Object> refMap = (Map<String, Object>) refItem;
                                    // 检查 pub-id 是否存在
                                    Object pubIdObj = refMap.get("pub-id");
                                    if (pubIdObj instanceof List) {
                                        List<?> pubId = (List<?>) pubIdObj;
                                        for (Object pubIdItem : pubId) {
                                            if (pubIdItem instanceof Map) {
                                                Map<String, Object> pubIdMap = (Map<String, Object>) pubIdItem;
                                                // 检查 @pub-id-type 和 _text 是否存在
                                                Object pubIdType = pubIdMap.get("@pub-id-type");
                                                Object pubIdText = pubIdMap.get("_text");
                                                if (pubIdType != null && pubIdText != null) {
                                                    // 找到 pub-id
                                                    return true;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * 获取文章信息
     *
     * @param fusionArticleProductMo
     * @return 封装之后的文章信息
     */
    private Map<String, Object> getArticleInformation(FusionArticleProductMO fusionArticleProductMo) {
        Map<String, Object> articleInformation = new HashMap<>();
        List<Map<String, Object>> record = fusionArticleProductMo.getRecord();
        for (Map<String, Object> map : record){
            // 篇级信息
            List<Map<String, Object>> articleMeta = (List<Map<String, Object>>)map.get("article-meta");
            if (articleMeta != null){
                for (Map<String, Object> map1 : articleMeta){
                    List<Map<String, Object>> titleGroup = (List<Map<String, Object>>)map1.get("title-group");
                    for (Map<String, Object> map2 : titleGroup){
                        List<Map<String, Object>> title = (List<Map<String, Object>>)map2.get("article-title");
                        for (Map<String, Object> map3 : title){
                            articleInformation.put("articleTitle", Objects.toString(map3.get("_text"), ""));
                        }
                    }
                    List<Map<String, Object>> articleId = (List<Map<String, Object>>)map1.get("article-id");
                    for (Map<String, Object> map2 : articleId){
                        String pubIdType = Objects.toString(map2.get("@pub-id-type"), "");
                        if (StringUtils.equalsIgnoreCase(pubIdType, "doi")){
                            articleInformation.put("doi", Objects.toString(map2.get("_text"), ""));
                        }
                    }
                    articleInformation.put("firstPage", map1.get("fpage"));
                    articleInformation.put("lastPage", map1.get("lpage"));
                    articleInformation.put("pageRange", map1.get("page-range"));
                }
            }
            // 期刊信息
            List<Map<String, Object>> sourceMeta = (List<Map<String, Object>>)map.get("source-meta");
            if (Objects.nonNull(sourceMeta)){
                for (Map<String, Object> map1 : sourceMeta){
                    List<Map<String, Object>> sourceTitleGroup = (List<Map<String, Object>>)map1.get("source-title-group");
                    for (Map<String, Object> map2 : sourceTitleGroup){
                        articleInformation.put("sourceTitle", Objects.toString(map2.get("source-title"), ""));
                    }
                    List<Map<String, Object>> issnGroup = (List<Map<String, Object>>)map1.get("issn");
                    if (issnGroup != null){
                        for (Map<String, Object> stringObjectMap : issnGroup) {
                            Object issnObj = stringObjectMap.get("@publication-format");
                            if (issnObj != null) {
                                String type = issnObj.toString();
                                if (StringUtils.equalsIgnoreCase(type, "electronic")) {
                                    articleInformation.put("issn", Objects.toString(stringObjectMap.get("_text"), ""));
                                }
                            }
                        }
                    }
                    List<Map<String, Object>> volumeIssueGroup = (List<Map<String, Object>>)map1.get("volume-issue-group");
                    if (volumeIssueGroup != null){
                        for (int i = 0; i < volumeIssueGroup.size(); i++) {
                            Object volumeObj = volumeIssueGroup.get(0);
                            if (Objects.nonNull(volumeObj)){
                                JSONObject volumeIssueJson = (JSONObject) volumeObj;
                                articleInformation.put("volume", volumeIssueJson.getString("volume"));
                            }
                            Object issueObj = volumeIssueGroup.get(1);
                            if (Objects.nonNull(issueObj)){
                                JSONObject issueJson = (JSONObject) issueObj;
                                articleInformation.put("issue", issueJson.getString("volume"));
                            }
                        }
                    }
                    List<Map<String, Object>> pubDateGroup = (List<Map<String, Object>>)map1.get("pub-date");
                    if (Objects.nonNull(pubDateGroup)){
                        for (Map<String, Object> stringObjectMap : pubDateGroup) {
                            Object year = stringObjectMap.get("year");
                            if (Objects.nonNull(year)){
                                articleInformation.put("year", year);
                            }
                        }
                    }
                }
            }
            // 引文信息
            List<Map<String, Object>> refList = (List<Map<String, Object>>)map.get("ref-list");
            if (refList != null){
                articleInformation.put("citeCount", refList.size());
            }
        }
        return articleInformation;
    }
}
