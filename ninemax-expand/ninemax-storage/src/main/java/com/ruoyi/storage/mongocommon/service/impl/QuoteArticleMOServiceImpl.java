package com.ruoyi.storage.mongocommon.service.impl;

import com.anwen.mongo.conditions.query.LambdaQueryChainWrapper;
import com.anwen.mongo.conditions.update.LambdaUpdateChainWrapper;
import com.anwen.mongo.conditions.update.UpdateChainWrapper;
import com.anwen.mongo.model.PageParam;
import com.anwen.mongo.model.PageResult;
import com.anwen.mongo.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.bean.BeanUtils;
import com.ruoyi.storage.mongocommon.domain.BasicMO;
import com.ruoyi.storage.mongocommon.domain.QuoteArticleMO;
import com.ruoyi.storage.mongocommon.param.QuoteArticleParamVo;
import com.ruoyi.storage.mongocommon.service.IQuoteArticleMOService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/7/23 9:14
 */
@Slf4j
@Service
public class QuoteArticleMOServiceImpl extends ServiceImpl<QuoteArticleMO> implements IQuoteArticleMOService {
    @Override
    public List<QuoteArticleMO> getQuoteArticlesByArticleId(String articleId) {
        LambdaQueryChainWrapper<QuoteArticleMO> lambdaQueryChainWrapper = this.lambdaQuery().eq(QuoteArticleMO::getCitedId, articleId);
        return lambdaQueryChainWrapper.list();
    }

    @Override
    public PageResult<QuoteArticleMO> getQuoteArticlesByPage(QuoteArticleParamVo quoteArticleParamVo, PageParam pageParam) {
        LambdaQueryChainWrapper<QuoteArticleMO> lambdaQueryChainWrapper = this.lambdaQuery();
        if (StringUtils.isNotBlank(quoteArticleParamVo.getRefId())){
            lambdaQueryChainWrapper.eq(QuoteArticleMO::getRefId, quoteArticleParamVo.getRefId());
        }
        if (StringUtils.isNotBlank(quoteArticleParamVo.getRefTitle())){
            lambdaQueryChainWrapper.like(QuoteArticleMO::getRefTitle, quoteArticleParamVo.getRefTitle());
        }
        if (StringUtils.isNotBlank(quoteArticleParamVo.getRefJournalTitle())){
            lambdaQueryChainWrapper.eq(QuoteArticleMO::getRefJournalTitle, quoteArticleParamVo.getRefJournalTitle());
        }

        if (StringUtils.isNotBlank(quoteArticleParamVo.getStartCreateTime()) && StringUtils.isNotBlank(quoteArticleParamVo.getEndCreateTime())){
            lambdaQueryChainWrapper.between(QuoteArticleMO::getCreateTime,
                    DateUtils.dateTime("yyyy-MM-dd HH:mm:ss", quoteArticleParamVo.getStartCreateTime()),
                    DateUtils.dateTime("yyyy-MM-dd HH:mm:ss", quoteArticleParamVo.getEndCreateTime()), true);
        }else{
            if (StringUtils.isNotBlank(quoteArticleParamVo.getStartCreateTime())){
                lambdaQueryChainWrapper.gte(QuoteArticleMO::getCreateTime, DateUtils.dateTime("yyyy-MM-dd HH:mm:ss", quoteArticleParamVo.getStartCreateTime()));
            }
            if (StringUtils.isNotBlank(quoteArticleParamVo.getEndCreateTime())){
                lambdaQueryChainWrapper.lte(QuoteArticleMO::getCreateTime, DateUtils.dateTime("yyyy-MM-dd HH:mm:ss", quoteArticleParamVo.getEndCreateTime()));
            }
        }
        // 施引 文献
        if (StringUtils.equals("1", quoteArticleParamVo.getCiteType())){
            lambdaQueryChainWrapper.eq(QuoteArticleMO::getCiteType, "1");
            if (StringUtils.isNotBlank(quoteArticleParamVo.getYear())){
                lambdaQueryChainWrapper.eq(QuoteArticleMO::getYear, quoteArticleParamVo.getYear());
            }
            if (StringUtils.isNotBlank(quoteArticleParamVo.getVolume())){
                lambdaQueryChainWrapper.eq(QuoteArticleMO::getVolume, quoteArticleParamVo.getVolume());
            }
            if (StringUtils.isNotBlank(quoteArticleParamVo.getIssue())){
                lambdaQueryChainWrapper.eq(QuoteArticleMO::getIssue, quoteArticleParamVo.getIssue());
            }
        }else{
            lambdaQueryChainWrapper.eq(QuoteArticleMO::getCiteType, "2");
        }
        lambdaQueryChainWrapper.orderByDesc(QuoteArticleMO::getUpdateTime);
        return this.page(lambdaQueryChainWrapper, pageParam);
    }

    @Override
    public void deleteQuoteArticleByArticleId(String removeCitedIds) {
        log.info("删除被引文献：{}", removeCitedIds);
        String[] removeData = removeCitedIds.split(",");
        for (String removeCitedId : removeData) {
            LambdaUpdateChainWrapper<QuoteArticleMO> lambdaUpdateChainWrapper = this.lambdaUpdate().eq(QuoteArticleMO::getId, removeCitedId);
            lambdaUpdateChainWrapper.remove();
        }
    }

    @Override
    public void updateQuoteArticle(QuoteArticleParamVo quoteArticleParamVo) {
        log.info("更新被引文献：{}", quoteArticleParamVo);
        UpdateChainWrapper updateChainWrapper = new UpdateChainWrapper<>();
        updateChainWrapper.eq("_id", quoteArticleParamVo.getId());
        safeSet(updateChainWrapper, "issn", quoteArticleParamVo.getIssn());
        safeSet(updateChainWrapper, "pageRange", quoteArticleParamVo.getPageRange());
        if (StringUtils.isNotBlank(quoteArticleParamVo.getPageRange())){
            String[] split = quoteArticleParamVo.getPageRange().split("-");
            safeSet(updateChainWrapper, "fpage", split[0]);
            safeSet(updateChainWrapper, "lpage", split[1]);
        }
        this.update(updateChainWrapper);
    }

    private void safeSet(UpdateChainWrapper updateChainWrapper, String field, Object value) {
        if (value != null) {
            updateChainWrapper.set(field, value);
        }
    }

    @Override
    public List<QuoteArticleMO> getQuoteArticleListByRefIds(QuoteArticleParamVo quoteArticleParamVo) {
        return this.lambdaQuery().in(QuoteArticleMO::getRefId, quoteArticleParamVo.getRefId()).list();
    }

    @Override
    public PageResult<QuoteArticleMO> getQuoteArticleListByCitedIds(QuoteArticleParamVo quoteArticleParamVo, PageParam pageParam) {
        LambdaQueryChainWrapper<QuoteArticleMO> queryWrapper = this.lambdaQuery().in(QuoteArticleMO::getCitedId, quoteArticleParamVo.getCitedId()).eq(QuoteArticleMO::getCiteType, "2");
        return this.page(queryWrapper, pageParam);
    }

    @Override

    public List<QuoteArticleMO> getQuoteArticleListByDoi(String doi) {

        return this.lambdaQuery().in(QuoteArticleMO::getDoi, doi).list();
    }

    @Override
    public void deleteByDoi(String doi) {
        this.lambdaUpdate().eq(QuoteArticleMO::getDoi, doi).remove();
    }

    @Override
    public QuoteArticleMO getQuoteArticleDetail(String id) {

        return this.lambdaQuery().eq(QuoteArticleMO::getId, id).one();
    }
}
