package com.ruoyi.storage.mongocommon.service.impl;

import com.anwen.mongo.service.impl.ServiceImpl;
import com.ruoyi.storage.datapublish.service.IDataPublishService;
import com.ruoyi.storage.mongocommon.domain.SingleArticlePrepareMO;
import com.ruoyi.storage.mongocommon.domain.SingleArticleProductMO;
import com.ruoyi.storage.mongocommon.domain.SingleIssueProductMO;
import com.ruoyi.storage.mongocommon.domain.SingleJournalProductMO;
import com.ruoyi.storage.mongocommon.service.ISingleArticleProductMOService;
import com.ruoyi.storage.mongocommon.service.ISingleJournalProductMOService;
import com.ruoyi.storage.utils.Constants;
import com.ruoyi.storage.utils.IdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class SingleJournalProductMOServiceImpl extends ServiceImpl<SingleJournalProductMO> implements ISingleJournalProductMOService {

    @Autowired
    private IDataPublishService dataPublishService;

    @Override
    public String saveByArticle(SingleJournalProductMO singleJournalProductMO) {
        String singleJournalId = IdGenerator.generateId(IdGenerator.DataFlowStatus.SINGLE, IdGenerator.DocType.JOURNAL, IdGenerator.Level.VARIETY, IdGenerator.DataSource.PUBMED);

        String sourceId = singleJournalProductMO.getSourceId();

        String xmlUrl = singleJournalProductMO.getXmlUri();

        SingleJournalProductMO mop = SingleJournalProductMO.builder()
                .singleJournalId(singleJournalId)
                .sourceId(sourceId)
                .minYear(singleJournalProductMO.getMinYear())
                .maxYear(singleJournalProductMO.getMaxYear())
                .minVolume(singleJournalProductMO.getMinVolume())
                .maxVolume(singleJournalProductMO.getMaxVolume())
                .minIssue(singleJournalProductMO.getMinIssue())
                .maxIssue(singleJournalProductMO.getMaxIssue())
                .createTime(new Date())
                .record(singleJournalProductMO.getRecord())
                .xmlUri(xmlUrl).build();

        this.save(mop);

        // 发布到ES
        try {
            // 将SingleArticleProductMO转换为Document，包含所有属性
            org.bson.Document document = new org.bson.Document();
            document.put("singleJournalId", mop.getSingleJournalId());
            document.put("sourceId", mop.getSourceId());
            document.put("minYear", mop.getMinYear());
            document.put("maxYear", mop.getMaxYear());
            document.put("minVolume", mop.getMinIssue());
            document.put("maxVolume", mop.getMaxVolume());
            document.put("minIssue", mop.getMinIssue());
            document.put("maxIssue", mop.getMaxIssue());
            document.put("record", mop.getRecord());
            document.put("createTime", mop.getCreateTime());
            document.put("updateTime", mop.getUpdateTime());
            document.put("xmlUri", mop.getXmlUri());

            // 发布到ES
            dataPublishService.publishImiData(document, Constants.SINGLE_JOURNAL_PRODUCT, "singleIssueId");
        } catch (Exception e) {
            // 记录错误但不影响主流程
            System.err.println("发布到ES失败: " + e.getMessage());
        }
        return singleJournalId;
    }
}
