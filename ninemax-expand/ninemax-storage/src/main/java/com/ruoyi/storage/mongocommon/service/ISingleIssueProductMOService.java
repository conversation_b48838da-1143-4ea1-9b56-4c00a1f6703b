package com.ruoyi.storage.mongocommon.service;

import com.anwen.mongo.service.IService;
import com.ruoyi.storage.mongocommon.domain.SingleIssueProductMO;
import com.ruoyi.storage.mongocommon.domain.SingleJournalProductMO;

/**
 * <AUTHOR>
 */
public interface ISingleIssueProductMOService extends IService<SingleIssueProductMO> {


    String saveByArticle(SingleIssueProductMO singleIssueProductMO);
}
