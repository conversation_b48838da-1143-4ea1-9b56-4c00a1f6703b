package com.ruoyi.storage.processingtool.vo;

import com.ruoyi.storage.api.vo.ExFileVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AddRuleBaseReqVO {

    /**
     * 规则名称
     */
    private String name;

    /**
     * 数据来源ID
     */
    private String sourceId;

    /**
     * 文献类型
     */
    private String docType;

    /**
     * 规则类型
     */
    private String ruleType;

    /**
     * 应用状态
     */
    private String useStatus;

    /**
     * 规则处理状态
     */
    private String ruleStatus;

    /**
     * 工具状态
     */
    private String toolStatus;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 样例文件
     */
    private String sampleFile;
    /**
     * 启用环节
     */
    private String phase;

    /**
     * 文件列表
     */
    private List<ExFileVO> fileList;

}
