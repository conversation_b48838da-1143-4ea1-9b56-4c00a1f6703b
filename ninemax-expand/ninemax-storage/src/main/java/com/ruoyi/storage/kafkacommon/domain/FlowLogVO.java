package com.ruoyi.storage.kafkacommon.domain;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 流程日志
 */
@Data
public class FlowLogVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一ID(采用系统生成的标准ID，不要用MongoDB的_id或者doi、pmid等资源id)
     */
    private String Id;
    /**
     * pId(期刊ID、图书ID等一级资源ID)
     */
    private String pId;
    /**
     * 数据来源
     */
    private String sourceId;
    /**
     * 数据类型（期刊、会议、专利、图书等）
     */
    private String dataType;

    /**
     * 文章类型
     */
    private Date createTime;

    /**
     * 处理状态(数据解析映射、数据归一)
     * 待状态设计程序后再整体迁移到数据字典中，后续这个分类会很庞大，具体见ConstantsFlow工具类
     */
    private Integer status;
}