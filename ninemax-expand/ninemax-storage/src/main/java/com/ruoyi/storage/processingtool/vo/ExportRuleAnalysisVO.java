package com.ruoyi.storage.processingtool.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExportRuleAnalysisVO {

    /**
     * 序号
     */
    @Excel(name = "序号", width = 10)
    private Integer number;

    /**
     * 路径ID
     */
    @Excel(name = "路径ID")
    private String pathId;

    /**
     * 排序码
     */
    @Excel(name = "排序码")
    private Integer sort;

    /**
     * 原始路径
     */
    @Excel(name = "原始路径", width = 30)
    private String srcPath;

    /**
     * IMI路径
     */
    @Excel(name = "IMI路径", width = 30)
    private String imiPath;

    /**
     * IMI标签
     */
    @Excel(name = "IMI标签", width = 20)
    private String imiLabel;

    /**
     * IMI标签类型(属性)
     */
    @Excel(name = "IMI标签类型(属性)")
    private String imiType;

    /**
     * 出现频次
     */
    @Excel(name = "出现频次")
    private Integer frequency;

    /**
     * 标签值样例
     */
    @Excel(name = "标签值样例", width = 30)
    private String sampleValue;

    /**
     * 应用状态
     */
    @Excel(name = "应用状态", readConverterExp = "Y=启用,N=停用")
    private String status;

    /**
     * 处理状态 'Y','N','H','D'
     */
    @Excel(name = "处理状态", readConverterExp = "Y=接受,N=忽略,H=待处理,D=删除")
    private String analysisStatus;


    /**
     * 样例文件存储路径
     */
    @Excel(name = "样例文件存储路径", width = 30)
    private String samplePath;

    /**
     * 关键ID
     */
    @Excel(name = "关键ID", readConverterExp = "Y=是,N=否")
    private String iskey;



    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 映射生成时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "映射生成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;

    /**
     * 映射更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "映射更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;

}
