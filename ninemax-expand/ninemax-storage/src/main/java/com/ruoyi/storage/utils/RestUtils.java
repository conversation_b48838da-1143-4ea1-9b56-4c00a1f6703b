package com.ruoyi.storage.utils;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.storage.api.domain.DeduplicateSingleRequest;
import com.ruoyi.storage.api.domain.ParsingMappingReq;
import com.ruoyi.storage.api.domain.RuleInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class RestUtils {

    @Resource
    private RestTemplate restTemplate;

    /**
     * 通用POST请求，带详细异常处理
     *
     * @param url          请求地址
     * @param data         请求体
     * @param responseType 响应类型
     * @return 响应体对象，异常时返回null
     */
    public <T> T postForEntity(String url, Object data, Class<T> responseType) {
        try {
            // 构造请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            // 构造请求体
            HttpEntity<Object> entity = new HttpEntity<>(data, headers);
            log.info("request: url={}, body={}", url, JSONObject.toJSONString(entity.getBody()));
            ResponseEntity<T> response = restTemplate.postForEntity(url, entity, responseType);
            log.info("response：状态码: {}, 响应: {}", response.getStatusCode(), JSONObject.toJSONString(response.getBody()));
            return response.getBody();
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("HTTP状态码异常: {}", e.getStatusCode());
            log.error("响应体: {}", e.getResponseBodyAsString());
        } catch (ResourceAccessException e) {
            log.error("网络连接异常: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("非法参数异常: {}", e.getMessage());
        } catch (Exception e) {
            log.error("未知异常: {}", e.getMessage(), e);
        }
        return null;
    }

    public String analysis(String requestUrl, ParsingMappingReq dto) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            //校验文件
            List<File> files = dto.getFiles();
            if (files == null || files.isEmpty()) {
                return null;
            }
            //添加文件
            File file = files.get(0);
            body.add("files", new FileSystemResource(file));
            //添加参数
            RuleInfo info = dto.getInfo();
            info.setSourceType("PubmedArticle");
            body.add("rules", dto.getRules()==null?Collections.emptyList():dto.getRules());
            body.add("checks", dto.getChecks());
            body.add("info", info);
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            // 发送请求并获取响应
            log.info("请求参数: url={}, body={}", requestUrl, requestEntity.getBody());
            ResponseEntity<String> response = restTemplate.exchange(
                    requestUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("返回参数：状态码: {}", response.getStatusCode());
            if (response.getStatusCode() != HttpStatus.OK) {
                log.info("请求失败，响应: " + response.getBody());
                return null;
            }
            return response.getBody();
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("HTTP状态码异常: {}", e.getStatusCode());
            log.error("响应体: {}", e.getResponseBodyAsString());
        } catch (ResourceAccessException e) {
            log.error("网络连接异常: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("非法参数异常: {}", e.getMessage());
        } catch (Exception e) {
            log.error("未知异常: {}", e.getMessage(), e);
        }
        return null;
    }

    public String deduplicate(String requestUrl, DeduplicateSingleRequest dto) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HashMap<String, Object> body = new HashMap<>();
            //添加参数
            body.put("data_list", dto.getData_list()==null?Collections.emptyList():dto.getData_list());
            body.put("type", dto.getType());
            body.put("data_source_id", dto.getData_source_id());
            body.put("callback_url", dto.getCallback_url());
            body.put("top_k", dto.getTop_k());
            body.put("similarity_threshold", dto.getSimilarity_threshold());
            HttpEntity<HashMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            // 发送请求并获取响应
            log.info("请求参数: url={}, body={}", requestUrl, requestEntity.getBody());
            ResponseEntity<String> response = restTemplate.exchange(
                    requestUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("返回参数：状态码: {}", response.getStatusCode());
            if (response.getStatusCode() != HttpStatus.OK) {
                log.info("请求失败，响应: " + response.getBody());
                return null;
            }
            return response.getBody();
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("HTTP状态码异常: {}", e.getStatusCode());
            log.error("响应体: {}", e.getResponseBodyAsString());
        } catch (ResourceAccessException e) {
            log.error("网络连接异常: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("非法参数异常: {}", e.getMessage());
        } catch (Exception e) {
            log.error("未知异常: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 测试外部服务连通性
     *
     * @param baseUrl 基础URL,type 请求类型
     * @return 是否可连通
     */
    public boolean testConnection(String baseUrl, String type) {
        try {
            if (StringUtils.equals(type, "GET")) {
                restTemplate.getForEntity(baseUrl, String.class);
            } else if (StringUtils.equals(type, "POST")) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                HttpEntity<Object> entity = new HttpEntity<>(new Object(), headers);
                restTemplate.postForEntity(baseUrl, entity, Object.class);
            } else {
                restTemplate.getForEntity(baseUrl, String.class);
            }
            return true;
        } catch (Exception e) {
            log.warn("连通性测试失败: {}, 错误: {}", baseUrl, e.getMessage());
            return false;
        }
    }

    public static void main(String[] args) {
        String requestUrl = "http://*************:20255/tool_1_get_rules_and_result";
        String filePath = "C:\\Users\\<USER>\\Desktop\\接口\\example.zip";
        String md5 = "af601228f47196fbf9d3f7b178dfd429";
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        File file = new File(filePath);
        if (!file.exists()) {
            System.out.println("文件不存在: " + filePath);
            return;
        }
        body.add("files", new FileSystemResource(file));
        // 添加 rules 参数（空数组）
        body.add("rules", Collections.emptyList());
        // 添加 checks 参数（数组中包含一个对象）
        Map<String, String> checkItem = new HashMap<>();
        checkItem.put("fileName", "example.zip");
        checkItem.put("md5", md5);
        body.add("checks", Collections.singletonList(checkItem));
        // 添加 info 参数（单个对象）
        Map<String, String> info = new HashMap<>();
        info.put("sourceId", "06");
        info.put("dataType", "J");
        info.put("docType", "A");
        info.put("ruleId", "Rxxxx");
        body.add("info", info);
        // 构建 HTTP 请求实体
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        // 发送请求并获取响应
        ResponseEntity<String> response = restTemplate.exchange(
                requestUrl,
                HttpMethod.POST,
                requestEntity,
                String.class
        );
        // 处理响应
        if (response.getStatusCode() == HttpStatus.OK) {
            System.out.println("请求成功，响应内容:");
            System.out.println(response.getBody());
        } else {
            System.out.println("请求失败，状态码: " + response.getStatusCode());
            System.out.println("响应内容: " + response.getBody());
        }
    }


    @EventListener
    public void handleRefresh(RefreshScopeRefreshedEvent event) {
        log.info("RefreshScopeRefreshedEvent received");
    }

}
