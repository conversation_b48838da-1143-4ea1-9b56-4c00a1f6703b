package com.ruoyi.storage.mongocommon.param;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/7/23 15:37
 */
@Data
public class QuoteArticleParamVo {

    private String id;

    private String articleId;

    private String articleTitle;

    private String journalTitle;

    private String citedId;

    private String citedTitle;

    private String citedJournalTitle;

    private String citeType;

    private String startCreateTime;

    private String endCreateTime;

    private String year;

    private String volume;

    private String issue;

    private int pageNum;

    private int pageSize;

    private String removeData;

    private String issn;

    private String firstPage;

    private String lastPage;

    private String refId;

    private String refTitle;

    private String refJournalTitle;

    private String refIds;

    private String citedIds;

    private String doi;

    private String pageRange;
}
