package com.ruoyi.storage.datapublish.service.impl;

import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.anwen.mongo.manager.MongoPlusClient;
import com.anwen.mongo.mapper.BaseMapper;
import com.anwen.mongo.mapping.TypeReference;
import com.anwen.mongo.model.PageResult;
import com.mongodb.client.MongoDatabase;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.storage.base.service.impl.BaseServiceImpl;
import com.ruoyi.storage.config.ElasticSearchRestClient;
import com.ruoyi.storage.datapublish.domain.*;
import com.ruoyi.storage.datapublish.mapper.DataPublishMapper;
import com.ruoyi.storage.datapublish.service.IDataPublishService;
import com.ruoyi.storage.datapublish.util.KafkaProducerUtil;
import com.ruoyi.storage.datapublish.vo.StartPublishDataReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.elasticsearch.action.bulk.BulkResponse;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import com.ruoyi.storage.datapublish.util.ParseImiDataUtil;
import com.ruoyi.storage.utils.PublishDataUtils;
import com.ruoyi.storage.searchbase.domain.MappingEsField;
import org.elasticsearch.action.index.IndexResponse;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * 
 * 数据发布服务实现类
 * 
 * 使用示例：
 * 
 * // 发布IMI格式数据
 * Document imiDocument = getImiDocumentFromMongoDB();
 * // 方式1：使用文档中的doi字段作为ES唯一标识
 * R<?> imiResult1 = publishImiData(imiDocument, "imi_articles_index", "doi");
 * // 方式2：让系统自动生成文档ID
 * R<?> imiResult2 = publishImiData(imiDocument, "imi_articles_index", null);
 * 
 * // 发布基础数据
 * Document basicDocument = getBasicDocumentFromMongoDB();
 * // 方式1：使用文档中的originalArticleId字段作为ES唯一标识
 * R<?> basicResult1 = publishBasicData(basicDocument, "basic_articles_index", "originalArticleId");
 * // 方式2：让系统自动生成文档ID
 * R<?> basicResult2 = publishBasicData(basicDocument, "basic_articles_index", null);
 * 
 * 这两个方法都会：
 * 1. 解析MongoDB文档中的数据
 * 2. 将数据转换为ES文档格式
 * 3. 发布到指定的ES索引中
 * 4. 返回操作结果
 */
@Service
@Slf4j
public class DataPublishServiceImpl extends BaseServiceImpl<DataPublishMapper, DataPublish> implements IDataPublishService {

    @Resource
    private BaseMapper baseMapper;
    @Resource
    private MongoPlusClient mongoPlusClient;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private ElasticSearchRestClient elasticSearchRestClient;


    private static boolean flag = false;

    @Override
    public R<?> publishImiData(Document document, String indexName, String esDocumentIdField) {
        try {
            if (document == null) {
                log.warn("文档为空，无法发布数据");
                return R.fail("文档为空");
            }

            // 使用ParseImiDataUtil解析IMI格式数据
            String journalTitle = ParseImiDataUtil.getJournalTitle((Document) document.get("record"));
            String issn = ParseImiDataUtil.getIssn((Document) document.get("record"));
            String pubYear = ParseImiDataUtil.getPubyear((Document) document.get("record"));
            String volume = ParseImiDataUtil.getVolume((Document) document.get("record"));
            String issue = ParseImiDataUtil.getIssue((Document) document.get("record"));
            String publisher = ParseImiDataUtil.getPublish((Document) document.get("record"));
            String articleTitle = ParseImiDataUtil.getArticleTitle((Document) document.get("record"));
            Map<String,String> fpageAndLpage = ParseImiDataUtil.getFpageAndLpage((Document) document.get("record"));
            String doi = ParseImiDataUtil.getDoi((Document) document.get("record"));
            String abstractText = ParseImiDataUtil.getAbstract((Document) document.get("record"));
            String keywords = ParseImiDataUtil.getKeyword((Document) document.get("record"));
            String authors = ParseImiDataUtil.getAuthors((Document) document.get("record"));

            // 构建ES文档
            Map<String, Object> esDocument = new HashMap<>();
            if("singleArticleId".equals(esDocumentIdField)) {
                Object originalArticleId = document.get("originalArticleId");
                if(ObjectUtils.isNotEmpty(originalArticleId)) {
                    List<String> list = (List<String>) originalArticleId;
                    esDocument.put("originalNum", list.size());
                }else{
                    esDocument.put("originalNum", 0);
                }
            }

            esDocument.put("journal_title", journalTitle);
            esDocument.put("issn", issn);
            esDocument.put("pub_year", pubYear);
            esDocument.put("volume", volume);
            esDocument.put("issue", issue);
            esDocument.put("publisher", publisher);
            esDocument.put("article_title", articleTitle);
            if(StringUtils.isNotBlank(fpageAndLpage.get("fpage"))) {
                esDocument.put("fpage", fpageAndLpage.get("fpage"));
            }
            if(StringUtils.isNotBlank(fpageAndLpage.get("lpage"))) {
                esDocument.put("lpage", fpageAndLpage.get("lpage"));
            }
            esDocument.put("doi", doi);
            esDocument.put("abstract", abstractText);
            esDocument.put("keywords", keywords);
            esDocument.put("authors", authors);
            esDocument.put("data_type", "J");
            esDocument.put("create_time", new Date());
            esDocument.put("update_time", new Date());

            // 添加Document中除record外的所有顶级字段
            for (String key : document.keySet()) {
                if (!"record".equals(key) && !"_id".equals(key)) {
                    Object value = document.get(key);
                    if (value != null) {
                        // 避免重复添加已解析的字段
                        if (!esDocument.containsKey(key) && !esDocument.containsKey(key.replace("_", ""))) {
                            esDocument.put(key, value);
                        }
                    }
                }
            }

            // 发布到ES
            List<Map<String, Object>> objects = new ArrayList<>();
            objects.add(esDocument);
            BulkResponse response = elasticSearchRestClient.bulkIndex(indexName, objects, esDocumentIdField);
            
            if (response != null && (response.status().getStatus() == 200 || response.status().getStatus() == 201)) {
                log.info("IMI数据发布成功，文档ID: {}, 索引: {}", "", indexName);
                return R.ok("数据发布成功");
            } else {
                log.error("IMI数据发布失败，文档ID: {}, 索引: {}", "", indexName);
                return R.fail("数据发布失败");
            }

        } catch (Exception e) {
            log.error("发布IMI数据时发生异常", e);
            return R.fail("发布数据时发生异常: " + e.getMessage());
        }
    }

    /**
     * 生成IMI文档ID
     * @param doi DOI
     * @param articleTitle 文章标题
     * @return 文档ID
     */
    private String generateImiDocumentId(String doi, String articleTitle) {
        return StringUtils.isNotEmpty(doi) ? doi : 
               StringUtils.isNotEmpty(articleTitle) ? articleTitle.hashCode() + "" : 
               System.currentTimeMillis() + "";
    }

    /**
     * 获取基础数据字段映射规则
     * 这里应该从数据库或配置中获取实际的映射规则
     */
    private List<MappingEsField> getBasicDataMappingFields() {
        // 这里应该查询数据库获取实际的字段映射规则
        // 暂时返回一些基础字段映射
        List<MappingEsField> fieldsList = new ArrayList<>();
        
        // 添加基础字段映射
        fieldsList.add(MappingEsField.builder()
                .fieldCode("originalArticleId")
                .fieldName("原始文章ID")
                .path("")
                .fieldType("keyword")
                .build());
        
        fieldsList.add(MappingEsField.builder()
                .fieldCode("batchId")
                .fieldName("批次ID")
                .path("")
                .fieldType("keyword")
                .build());
        
        fieldsList.add(MappingEsField.builder()
                .fieldCode("sourceId")
                .fieldName("来源ID")
                .path("")
                .fieldType("keyword")
                .build());
        
        fieldsList.add(MappingEsField.builder()
                .fieldCode("dataType")
                .fieldName("数据类型")
                .path("")
                .fieldType("keyword")
                .build());
        
        fieldsList.add(MappingEsField.builder()
                .fieldCode("xmlUri")
                .fieldName("XML文件URI")
                .path("")
                .fieldType("keyword")
                .build());
        
        fieldsList.add(MappingEsField.builder()
                .fieldCode("annexUri")
                .fieldName("附件URI")
                .path("")
                .fieldType("keyword")
                .build());
        
        fieldsList.add(MappingEsField.builder()
                .fieldCode("originalvalid")
                .fieldName("原始数据有效性")
                .path("")
                .fieldType("boolean")
                .build());
        
        fieldsList.add(MappingEsField.builder()
                .fieldCode("record")
                .fieldName("原始记录数据")
                .path("")
                .fieldType("text")
                .build());
        
        return fieldsList;
    }

    /**
     * 生成基础数据文档ID
     */
    private String generateBasicDocumentId(Document document, Map<String, Object> esDocument) {
        // 优先使用originalArticleId作为文档ID
        String originalArticleId = (String) esDocument.get("originalArticleId");
        if (StringUtils.isNotEmpty(originalArticleId)) {
            return originalArticleId;
        }
        
        // 如果没有originalArticleId，使用MongoDB的_id
        Object mongoId = document.get("_id");
        if (mongoId != null) {
            return mongoId.toString();
        }
        
        // 最后使用时间戳
        return System.currentTimeMillis() + "";
    }

//    @Override
//    public R<?> publishBasicData(Document document, String indexName, String esDocumentIdField) {
//        try {
//            if (document == null) {
//                log.warn("文档为空，无法发布基础数据");
//                return R.fail("文档为空");
//            }
//
//            // 获取基础数据的字段映射规则
//            // 这里需要根据实际情况获取对应的字段映射规则
//            // 暂时使用默认的基础字段映射
//            List<MappingEsField> fieldsList = getBasicDataMappingFields();
//
//            if (fieldsList == null || fieldsList.isEmpty()) {
//                log.warn("未找到基础数据字段映射规则");
//                return R.fail("未找到字段映射规则");
//            }
//
//            // 使用PublishDataUtils解析基础数据
//            Map<String, Object> esDocument = PublishDataUtils.basicDataToEsData(document, fieldsList);
//
//            if (esDocument == null || esDocument.isEmpty()) {
//                log.warn("解析基础数据失败，文档内容为空");
//                return R.fail("解析数据失败");
//            }
//
//            // 添加基础数据特有字段
//            esDocument.put("data_type", "BASIC");
//            esDocument.put("create_time", new Date());
//            esDocument.put("update_time", new Date());
//
//            // 添加Document中除record外的所有顶级字段
//            for (String key : document.keySet()) {
//                if (!"record".equals(key)) {
//                    Object value = document.get(key);
//                    if (value != null) {
//                        // 避免重复添加已解析的字段
//                        if (!esDocument.containsKey(key) && !esDocument.containsKey(key.replace("_", ""))) {
//                            esDocument.put(key, value);
//                        }
//                    }
//                }
//            }
//
//            // 生成文档ID
//            String finalDocumentId;
//            if (StringUtils.isNotEmpty(esDocumentIdField) && document.containsKey(esDocumentIdField)) {
//                // 使用指定的字段值作为文档ID
//                Object fieldValue = document.get(esDocumentIdField);
//                finalDocumentId = fieldValue != null ? fieldValue.toString() : generateBasicDocumentId(document, esDocument);
//            } else {
//                // 使用默认的ID生成策略
//                finalDocumentId = generateBasicDocumentId(document, esDocument);
//            }
//
//            // 发布到ES
//            IndexResponse response = elasticSearchRestClient.index(indexName, finalDocumentId, esDocument);
//
//            if (response != null && (response.status().getStatus() == 200 || response.status().getStatus() == 201)) {
//                log.info("基础数据发布成功，文档ID: {}, 索引: {}", finalDocumentId, indexName);
//                return R.ok("基础数据发布成功");
//            } else {
//                log.error("基础数据发布失败，文档ID: {}, 索引: {}", finalDocumentId, indexName);
//                return R.fail("基础数据发布失败");
//            }
//
//        } catch (Exception e) {
//            log.error("发布基础数据时发生异常", e);
//            return R.fail("发布基础数据时发生异常: " + e.getMessage());
//        }
//    }
    @Override
    public R<?> publishBasicData(Document document, String indexName, String esDocumentIdField) {
        try {
            if (document == null) {
                log.warn("文档为空，无法发布基础数据");
                return R.fail("文档为空");
            }

            // 获取基础数据的字段映射规则
            // 这里需要根据实际情况获取对应的字段映射规则
            // 暂时使用默认的基础字段映射
            List<MappingEsField> fieldsList = getBasicDataMappingFields();

            if (fieldsList == null || fieldsList.isEmpty()) {
                log.warn("未找到基础数据字段映射规则");
                return R.fail("未找到字段映射规则");
            }

            // 使用PublishDataUtils解析基础数据
            Map<String, Object> esDocument = PublishDataUtils.basicDataToEsData(document, fieldsList);

            if (esDocument == null || esDocument.isEmpty()) {
                log.warn("解析基础数据失败，文档内容为空");
                return R.fail("解析数据失败");
            }

            // 添加基础数据特有字段
            esDocument.put("data_type", "BASIC");
            esDocument.put("create_time", new Date());
            esDocument.put("update_time", new Date());

            // 添加Document中除record外的所有顶级字段
            for (String key : document.keySet()) {
                if (!"record".equals(key)) {
                    Object value = document.get(key);
                    if (value != null) {
                        // 避免重复添加已解析的字段
                        if (!esDocument.containsKey(key) && !esDocument.containsKey(key.replace("_", ""))) {
                            esDocument.put(key, value);
                        }
                    }
                }
            }

            // 生成文档ID
            String finalDocumentId;
            if (StringUtils.isNotEmpty(esDocumentIdField) && document.containsKey(esDocumentIdField)) {
                // 使用指定的字段值作为文档ID
                Object fieldValue = document.get(esDocumentIdField);
                finalDocumentId = fieldValue != null ? fieldValue.toString() : generateBasicDocumentId(document, esDocument);
            } else {
                // 使用默认的ID生成策略
                finalDocumentId = generateBasicDocumentId(document, esDocument);
            }

            // 发布到ES
            IndexResponse response = elasticSearchRestClient.index(indexName, finalDocumentId, esDocument);

            if (response != null && (response.status().getStatus() == 200 || response.status().getStatus() == 201)) {
                log.info("基础数据发布成功，文档ID: {}, 索引: {}", finalDocumentId, indexName);
                return R.ok("基础数据发布成功");
            } else {
                log.error("基础数据发布失败，文档ID: {}, 索引: {}", finalDocumentId, indexName);
                return R.fail("基础数据发布失败");
            }

        } catch (Exception e) {
            log.error("发布基础数据时发生异常", e);
            return R.fail("发布基础数据时发生异常: " + e.getMessage());
        }
    }



    /**
     * 开始发布数据
     *
     * @param startPublishDataReqVO 请求参数
     */
    @Override
    public R<?> startPublishData(StartPublishDataReqVO startPublishDataReqVO) {
        log.info("开始发布数据==>{}", startPublishDataReqVO);
        //
//        sendFusionDataIndexNew();
        return R.ok("数据发布成功!");
    }

    @Override
    public R<?> publishPubmedData(StartPublishDataReqVO startPublishDataReqVO) {
        System.out.println(("----- selectAll method test ------"));
        for (int i = 1; i < 41; i++) {
            PageResult<PubmedMO> pageResult = baseMapper.page(i, 10000, PubmedMO.class);
            List<PubmedMO> pubmedDataList = pageResult.getContentData();
//        List<Map<String, Object>> pubmedDataList = baseMapper.list("match_pubmed_article_group", new TypeReference<Map<String, Object>>() {
//        });

            // 用于存储已处理的PMID，进行去重
            Set<String> processedPmids = new HashSet<>();
            List<PubmedMO> uniqueDataList = new ArrayList<>();
            List<PubmedArticleFinalMO> pubmedArticleFinalMOS = new ArrayList<>();

            for (PubmedMO data : pubmedDataList) {
                String pmid = findArticleId(data.getRecord(), "PMID");
                if (pmid != null && !processedPmids.contains(pmid)) {
                    processedPmids.add(pmid);
                    uniqueDataList.add(data);
                    PubmedArticleFinalMO build = PubmedArticleFinalMO.builder()
                            .id(data.getId())
                            .pmid(pmid)
                            .record(data.getRecord())
                            .createTime(data.getCreateTime())
                            .build();
                    pubmedArticleFinalMOS.add(build);
                } else if (pmid != null) {
                    log.info("发现重复PMID数据: {}", pmid);
                }
            }

            log.info("原始数据总数: {}, 去重后数据总数: {}, 重复数据: {}",
                    pubmedDataList.size(), uniqueDataList.size(),
                    pubmedDataList.size() - uniqueDataList.size());

            if (!pubmedArticleFinalMOS.isEmpty()) {
                Boolean pubmedArticleFinal = baseMapper.saveBatch("pubmed_article_final", pubmedArticleFinalMOS);
                log.info("pubmed_article_final数据保存成功: {}", pubmedArticleFinal);
            }
            log.info("去重后数据保存成功: {}", uniqueDataList.size());
        }

        return R.ok();

    }

    @Override
    public R<?> publishNstlData(StartPublishDataReqVO startPublishDataReqVO) {
        System.out.println(("----- selectAll method test ------"));
//        List<Map<String, Object>> nstlDataList = baseMapper.list("match_nstlzjg2020_article_group", new TypeReference<Map<String, Object>>() {
//        });
//        Set<String> processedPmids = new HashSet<>();
        for (int i = 1; i < 41; i++) {
            PageResult<NstlMO> pageResult = baseMapper.page(i, 5000, NstlMO.class);
            List<NstlMO> nstlDataList = pageResult.getContentData();
            // 用于存储已处理的PMID，进行去重
            Set<String> processedPmids = new HashSet<>();
            List<NstlMO> uniqueDataList = new ArrayList<>();
            List<NstlArticleFinalMO> nstlArticleFinalMOS = new ArrayList<>();

            for (NstlMO data : nstlDataList) {
                String guid = findArticleId(data.getRecord(), "NSTL_guid");
                if (guid != null && !processedPmids.contains(guid)) {
                    processedPmids.add(guid);
                    uniqueDataList.add(data);
                    NstlArticleFinalMO build = NstlArticleFinalMO.builder()
                            .id(data.getId())
                            .guid(guid)
                            .record(data.getRecord())
                            .createTime(data.getCreateTime())
                            .build();
                    nstlArticleFinalMOS.add(build);
                } else if (guid != null) {
                    log.warn("发现重复guid数据: {}", guid);
                }
            }

            log.info("原始数据总数: {}, 去重后数据总数: {}, 重复数据: {}",
                    nstlDataList.size(), uniqueDataList.size(),
                    nstlDataList.size() - uniqueDataList.size());
            if (!nstlArticleFinalMOS.isEmpty()) {
                Boolean nstlArticleFinal = baseMapper.saveBatch("nstl_article_final", nstlArticleFinalMOS);
                log.info("nstl_article_final数据保存成功: {}", nstlArticleFinal);
            }
        }


        return R.ok("数据发布成功!");

    }


    @Override
    public R<?> pubmedData(StartPublishDataReqVO startPublishDataReqVO) {
        for (int i = 1; i < 41; i++) {
            List<Document> list = baseMapper.list("match_pubmed_article_group", new TypeReference<Document>() {
            });
//            KafkaProducerUtil.sendPubmenData(list, "20250718");
            log.info("pubmed_article_final数据保存成功:{}: {}", i, list.size());
        }
        return R.ok("数据发布成功!");
    }

    @Override
    public R<?> publishAnnotationData(StartPublishDataReqVO startPublishDataReqVO) {
        Set<String> processedPmids = new HashSet<>();
        int count = 0;
        int count2 = 0;
        for (int i = 1; i < 31; i++) {
//            List<Document> list = baseMapper.list("match_pubmed_article_group", new TypeReference<Document>() {
//            });
            List<Document> pubList = new ArrayList<>();
            PageResult<PublishAnnotationDataMO> pageResult = baseMapper.page(i, 5000, PublishAnnotationDataMO.class);
            List<PublishAnnotationDataMO> contentData = pageResult.getContentData();
            for (PublishAnnotationDataMO contentDatum : contentData) {
                String guid = contentDatum.getNSTL_guid();
                if (guid == null) {
                    continue;
                }
                if (processedPmids.contains(guid)) {
                    count++;
                    log.info("发现重复guid数据: {}", guid);
                } else {
                    processedPmids.add(guid);
                    count2++;
                    //
                    Document document = new Document();
                    document.put("id", contentDatum.getId());
                    document.put("record", contentDatum.getRecord());
                    pubList.add(document);
                }
            }
            log.info("原始数据总数: {}, 去重后数据总数: {}, 重复数据: {}", contentData.size(), count, count2);
            log.info("待发布数据: {}", pubList.size());
            KafkaProducerUtil.sendFusionData(pubList, "20250718");
            log.info("数据发布成功:{}: {}", i, pubList.size());
        }
        log.info("pubmed_article_final数据保存成功:{}: {}", count2, count);
        log.info("数据发布成功!总发布数==>{}", processedPmids.size());
        return R.ok("数据发布成功!总发布数==>" + processedPmids.size());
    }



    /**
     * 从record字段中提取对应key字段信息
     */
    public static String findArticleId(List<?> recordList, String key) {
        if (recordList == null || recordList.isEmpty()) {
            log.debug("Record list is null or empty");
            return null;
        }

        try {
            for (Object record : recordList) {
                if (!(record instanceof Document)) {
                    log.debug("Record is not a Document type: {}", record.getClass());
                    continue;
                }

                JSONObject recordMap = JSONObject.parseObject(((Document) record).toJson());
                List<Map<String, Object>> articleMetaList = safeCastToList(recordMap.get("article-meta"));

                for (Map<String, Object> articleMeta : articleMetaList) {
                    List<Map<String, Object>> articleIdList = safeCastToList(articleMeta.get("article-id"));

                    for (Map<String, Object> articleId : articleIdList) {
                        String pubIdType = Objects.toString(articleId.get("@pub-id-type"), "");
                        if (StringUtils.equalsIgnoreCase(pubIdType, key)) {
                            return Objects.toString(articleId.get("_text"), null);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error while processing article IDs", e);
        }

        log.debug("No matching article ID found for key: {}", key);
        return null;
    }

    @SuppressWarnings("unchecked")
    private static <T> List<T> safeCastToList(Object obj) {
        if (obj instanceof List) {
            return (List<T>) obj;
        }
        return List.of();
    }
//    public void sendFusionDataIndexNew() {
//        if (!flag) {
//            flag = true;
//
//            try {
//                int dateIndex = 1;
//
//                while (true) {
//                    System.out.println("sendFusionDataIndex start");
//                    int publishCount = 10000;
//                    String source = "2025022" + dateIndex;
//                    MongoDatabase mongoDatabaseConnection = mongoTemplate.getDb();
////                    MongoDatabase mongoDatabaseConnection = MongoClientUtils.getMongoDatabaseConnection();
//                    HashMap<String, Object> subjectParams = new HashMap();
//                    subjectParams.put("=,is_match", false);
//                    List<Document> data = this.mongoService.find(subjectParams, "send_kafka_data", new ArrayList(), 0, 1);
//                    if (data.size() == 0) {
//                        System.out.println("全部数据发布完成");
//                        break;
//                    }
//
//                    Map<String, Object> map = new HashMap();
//                    int index = 0;
//
//                    for (int i = 0; i < Integer.MAX_VALUE && index < publishCount; ++i) {
//                        List<Document> matchList = new ArrayList();
//                        List<Document> subjectDataIndex = this.mongoService.find(subjectParams, "send_kafka_data", new ArrayList(), i * 2000, 2000);
//                        Iterator var12 = subjectDataIndex.iterator();
//
//                        label657:
//                        while (true) {
//                            label576:
//                            while (true) {
//                                if (!var12.hasNext()) {
//                                    break label657;
//                                }
//
//                                Document dataIndex = (Document) var12.next();
//                                String dataId = String.valueOf(dataIndex.get("data_id"));
//                                if (index >= publishCount || map.containsKey(dataId)) {
//                                    break label657;
//                                }
//
//                                Document match = this.mongoService.findById(dataId, "match_article_group");
//                                List<Document> recordDocuments = (List) match.get("record");
//                                if (recordDocuments.size() > 3) {
//                                    map.put(dataId, "");
//                                    String articleId = match.getString("id");
//                                    String mongodbId = match.getString("mongodbId");
//                                    HashMap<String, Object> paramsJou = new HashMap();
//                                    paramsJou.put("=,mongodbId", mongodbId);
//                                    List<Document> matchJournalGroup = this.mongoService.find(paramsJou, "match_journal_group");
//                                    String sourceId = ((Document) matchJournalGroup.get(0)).getString("id");
//                                    Object o = dataIndex.get("data_value");
//                                    JSONObject jsonObject = JSONObject.parseObject(String.valueOf(o));
//                                    List<Document> subjGroup = new ArrayList();
//                                    Iterator var25 = jsonObject.keySet().iterator();
//
//                                    while (true) {
//                                        Iterator var29;
//                                        Document subjects;
//                                        while (var25.hasNext()) {
//                                            String s = (String) var25.next();
//                                            Object mhs;
//                                            JSONArray objects;
//                                            Object object;
//                                            ArrayList subjectsList;
//                                            String qui;
//                                            String textEn;
//                                            ArrayList subjectList;
//                                            JSONObject json;
//                                            String qtextZh;
//                                            String textZh;
//                                            String ui;
//                                            String isTopic;
//                                            String qtextEn;
//                                            Document subjectsDoc;
//                                            if ("mh".equals(s)) {
//                                                mhs = jsonObject.get("mh");
//                                                objects = JSONObject.parseArray(String.valueOf(mhs), new Feature[0]);
//                                                var29 = objects.iterator();
//
//                                                while (var29.hasNext()) {
//                                                    object = var29.next();
//                                                    subjectsList = new ArrayList();
//                                                    subjects = new Document();
//                                                    subjectList = new ArrayList();
//                                                    json = JSONObject.parseObject(String.valueOf(object));
//                                                    qtextZh = json.getString("qtext-zh");
//                                                    if (StringUtils.isNotEmpty(qtextZh)) {
//                                                        Document subject = new Document();
//                                                        subject.put("@xml:lang", "zh");
//                                                        subject.put("_text", qtextZh);
//                                                        subjectList.add(subject);
//                                                    }
//
//                                                    textZh = json.getString("text-zh");
//                                                    if (StringUtils.isNotEmpty(textZh)) {
//                                                        Document subject = new Document();
//                                                        subject.put("@xml:lang", "zh");
//                                                        subject.put("_text", textZh);
//                                                        subjectList.add(subject);
//                                                    }
//
//                                                    ui = json.getString("ui");
//                                                    if (StringUtils.isNotEmpty(ui)) {
//                                                        subjects.put("subject-id", ui);
//                                                    }
//
//                                                    qui = json.getString("qui");
//                                                    if (StringUtils.isNotEmpty(qui)) {
//                                                        subjects.put("subject-id", qui);
//                                                    }
//
//                                                    isTopic = json.getString("is-topic");
//                                                    if (StringUtils.isNotEmpty(isTopic)) {
//                                                        subjects.put("@is-major-topic", isTopic);
//                                                    }
//
//                                                    qtextEn = json.getString("qtext-en");
//                                                    if (StringUtils.isNotEmpty(qtextEn)) {
//                                                        Document subject = new Document();
//                                                        subject.put("@xml:lang", "en");
//                                                        subject.put("_text", qtextEn);
//                                                        subjectList.add(subject);
//                                                    }
//
//                                                    textEn = json.getString("text-en");
//                                                    if (StringUtils.isNotEmpty(textEn)) {
//                                                        subjectsDoc = new Document();
//                                                        subjectsDoc.put("@xml:lang", "en");
//                                                        subjectsDoc.put("_text", textEn);
//                                                        subjectList.add(subjectsDoc);
//                                                    }
//
//                                                    if (!subjectList.isEmpty()) {
//                                                        subjects.put("subject", subjectList);
//                                                    }
//
//                                                    subjectsList.add(subjects);
//                                                    subjectsDoc = new Document();
//                                                    subjectsDoc.put("subjects", subjectsList);
//                                                    subjGroup.add(subjectsDoc);
//                                                }
//                                            } else if ("sh".equals(s)) {
//                                                mhs = jsonObject.get("sh");
//                                                objects = JSONObject.parseArray(String.valueOf(mhs), new Feature[0]);
//                                                var29 = objects.iterator();
//
//                                                while (var29.hasNext()) {
//                                                    object = var29.next();
//                                                    subjectsList = new ArrayList();
//                                                    subjects = new Document();
//                                                    subjectList = new ArrayList();
//                                                    json = JSONObject.parseObject(String.valueOf(object));
//                                                    qtextZh = json.getString("qtext-zh");
//                                                    if (StringUtils.isNotEmpty(qtextZh)) {
//                                                        Document subject = new Document();
//                                                        subject.put("@xml:lang", "zh");
//                                                        subject.put("_text", qtextZh);
//                                                        subjectList.add(subject);
//                                                    }
//
//                                                    textZh = json.getString("text-zh");
//                                                    if (StringUtils.isNotEmpty(textZh)) {
//                                                        Document subject = new Document();
//                                                        subject.put("@xml:lang", "zh");
//                                                        subject.put("_text", textZh);
//                                                        subjectList.add(subject);
//                                                    }
//
//                                                    ui = json.getString("qui");
//                                                    if (StringUtils.isNotEmpty(ui)) {
//                                                        subjects.put("subject-id", ui);
//                                                    }
//
//                                                    qui = json.getString("ui");
//                                                    if (StringUtils.isNotEmpty(qui)) {
//                                                        subjects.put("sub-subject-id", qui);
//                                                    }
//
//                                                    isTopic = json.getString("is-topic");
//                                                    if (StringUtils.isNotEmpty(isTopic)) {
//                                                        subjects.put("@is-major-topic", isTopic);
//                                                    }
//
//                                                    qtextEn = json.getString("qtext-en");
//                                                    if (StringUtils.isNotEmpty(qtextEn)) {
//                                                        Document subject = new Document();
//                                                        subject.put("@xml:lang", "en");
//                                                        subject.put("_text", qtextEn);
//                                                        subjectList.add(subject);
//                                                    }
//
//                                                    textEn = json.getString("text-en");
//                                                    if (StringUtils.isNotEmpty(textEn)) {
//                                                        subjectsDoc = new Document();
//                                                        subjectsDoc.put("@xml:lang", "en");
//                                                        subjectsDoc.put("_text", textEn);
//                                                        subjectList.add(subjectsDoc);
//                                                    }
//
//                                                    if (!subjectList.isEmpty()) {
//                                                        subjects.put("sub-subject", subjectList);
//                                                    }
//
//                                                    subjectsList.add(subjects);
//                                                    subjectsDoc = new Document();
//                                                    subjectsDoc.put("sub-subjects", subjectsList);
//                                                    subjGroup.add(subjectsDoc);
//                                                }
//                                            } else if ("ct".equals(s)) {
//                                                mhs = jsonObject.get("ct");
//                                                objects = JSONObject.parseArray(String.valueOf(mhs), new Feature[0]);
//                                                var29 = objects.iterator();
//
//                                                while (var29.hasNext()) {
//                                                    object = var29.next();
//                                                    subjectsList = new ArrayList();
//                                                    subjects = new Document();
//                                                    subjectList = new ArrayList();
//                                                    json = JSONObject.parseObject(String.valueOf(object));
//                                                    qtextZh = json.getString("qtext-zh");
//                                                    if (StringUtils.isNotEmpty(qtextZh)) {
//                                                        Document subject = new Document();
//                                                        subject.put("@xml:lang", "zh");
//                                                        subject.put("_text", qtextZh);
//                                                        subjectList.add(subject);
//                                                    }
//
//                                                    textZh = json.getString("text-zh");
//                                                    if (StringUtils.isNotEmpty(textZh)) {
//                                                        Document subject = new Document();
//                                                        subject.put("@xml:lang", "zh");
//                                                        subject.put("_text", textZh);
//                                                        subjectList.add(subject);
//                                                    }
//
//                                                    ui = json.getString("ui");
//                                                    if (StringUtils.isNotEmpty(ui)) {
//                                                        subjects.put("subject-id", ui);
//                                                    }
//
//                                                    qui = json.getString("qui");
//                                                    if (StringUtils.isNotEmpty(qui)) {
//                                                        subjects.put("subject-id", qui);
//                                                    }
//
//                                                    isTopic = json.getString("is-topic");
//                                                    if (StringUtils.isNotEmpty(isTopic)) {
//                                                        subjects.put("@is-major-topic", isTopic);
//                                                    }
//
//                                                    qtextEn = json.getString("qtext-en");
//                                                    if (StringUtils.isNotEmpty(qtextEn)) {
//                                                        Document subject = new Document();
//                                                        subject.put("@xml:lang", "en");
//                                                        subject.put("_text", qtextEn);
//                                                        subjectList.add(subject);
//                                                    }
//
//                                                    textEn = json.getString("text-en");
//                                                    if (StringUtils.isNotEmpty(textEn)) {
//                                                        subjectsDoc = new Document();
//                                                        subjectsDoc.put("@xml:lang", "en");
//                                                        subjectsDoc.put("_text", textEn);
//                                                        subjectList.add(subjectsDoc);
//                                                    }
//
//                                                    if (!subjectList.isEmpty()) {
//                                                        subjects.put("subject", subjectList);
//                                                    }
//
//                                                    subjects.put("@specific-use", "general");
//                                                    subjectsList.add(subjects);
//                                                    subjectsDoc = new Document();
//                                                    subjectsDoc.put("subjects", subjectsList);
//                                                    subjGroup.add(subjectsDoc);
//                                                }
//                                            } else if ("mhs".equals(s)) {
//                                                mhs = jsonObject.get("mhs");
//                                                objects = JSONObject.parseArray(String.valueOf(mhs), new Feature[0]);
//                                                var29 = objects.iterator();
//
//                                                while (var29.hasNext()) {
//                                                    object = var29.next();
//                                                    subjectsList = new ArrayList();
//                                                    List<Document> subSubjectsList = new ArrayList();
//                                                    subjects = new Document();
//                                                    Document subSubjects = new Document();
//                                                    subjectList = new ArrayList();
//                                                    List<Document> subSubjectList = new ArrayList();
//                                                    json = JSONObject.parseObject(String.valueOf(object));
//                                                    qui = json.getString("qtext-zh");
//                                                    if (StringUtils.isNotEmpty(qui)) {
//                                                        Document subject = new Document();
//                                                        subject.put("@xml:lang", "zh");
//                                                        subject.put("_text", qui);
//                                                        subSubjectList.add(subject);
//                                                    }
//
//                                                    isTopic = json.getString("text-zh");
//                                                    if (StringUtils.isNotEmpty(isTopic)) {
//                                                        Document subject = new Document();
//                                                        subject.put("@xml:lang", "zh");
//                                                        subject.put("_text", isTopic);
//                                                        subjectList.add(subject);
//                                                    }
//
//                                                    qtextEn = json.getString("qui");
//                                                    if (StringUtils.isNotEmpty(qtextEn)) {
//                                                        subSubjects.put("subject-id", qtextEn);
//                                                    }
//
//                                                    textEn = json.getString("ui");
//                                                    if (StringUtils.isNotEmpty(textEn)) {
//                                                        subjects.put("sub-subject-id", textEn);
//                                                    }
//
//                                                    isTopic = json.getString("is-topic");
//                                                    if (StringUtils.isNotEmpty(isTopic)) {
//                                                        subjects.put("@is-major-topic", isTopic);
//                                                    }
//
//                                                    qtextEn = json.getString("qtext-en");
//                                                    if (StringUtils.isNotEmpty(qtextEn)) {
//                                                        Document subject = new Document();
//                                                        subject.put("@xml:lang", "en");
//                                                        subject.put("_text", qtextEn);
//                                                        subSubjectList.add(subject);
//                                                    }
//
//                                                    textEn = json.getString("text-en");
//                                                    if (StringUtils.isNotEmpty(textEn)) {
//                                                        subjectsDoc = new Document();
//                                                        subjectsDoc.put("@xml:lang", "en");
//                                                        subjectsDoc.put("_text", textEn);
//                                                        subjectList.add(subjectsDoc);
//                                                    }
//
//                                                    if (!subjectList.isEmpty()) {
//                                                        subjects.put("subject", subjectList);
//                                                    }
//
//                                                    if (!subSubjectList.isEmpty()) {
//                                                        subSubjects.put("sub-subject", subSubjectList);
//                                                    }
//
//                                                    subjectsList.add(subjects);
//                                                    subSubjectsList.add(subSubjects);
//                                                    subjectsDoc = new Document();
//                                                    subjectsDoc.put("subjects", subjectsList);
//                                                    subjectsDoc.put("sub-subjects", subSubjectsList);
//                                                    subjGroup.add(subjectsDoc);
//                                                }
//                                            }
//                                        }
//
//                                        boolean flag = true;
//                                        Iterator var52 = recordDocuments.iterator();
//
//                                        while (true) {
//                                            Object o2;
//                                            do {
//                                                Document recordDocument;
//                                                do {
//                                                    if (!var52.hasNext()) {
//                                                        if (flag) {
//                                                            recordDocument = new Document();
//                                                            List<Document> classs = new ArrayList();
//                                                            Document document = new Document();
//                                                            document.put("subj-group", subjGroup);
//                                                            classs.add(document);
//                                                            recordDocument.put("subj-class-kwd", classs);
//                                                            recordDocuments.add(recordDocument);
//                                                        }
//
//                                                        Map<String, Object> dynamicData = new HashMap();
//                                                        dynamicData.put("is_match", true);
//                                                        dynamicData.put("kafka_topic", "topic_fusion_data_" + source);
//                                                        matchList.add(match);
////                                                        MongoClientUtils.editNew(mongoDatabaseConnection, "send_kafka_data", dataIndex.get("_id"), dynamicData);
//                                                        System.out.println(index++ + ";" + map.keySet().size());
//                                                        continue label576;
//                                                    }
//
//                                                    recordDocument = (Document) var52.next();
//                                                    List sorceMetas;
//                                                    Document document;
//                                                    if (recordDocument.containsKey("subj-class-kwd") && subjGroup.size() > 0) {
//                                                        flag = false;
//                                                        sorceMetas = (List) recordDocument.get("subj-class-kwd");
//                                                        var29 = sorceMetas.iterator();
//
//                                                        while (var29.hasNext()) {
//                                                            document = (Document) var29.next();
//                                                            document.put("subj-group", subjGroup);
//                                                        }
//                                                    }
//
//                                                    List sourceIds;
//                                                    if (recordDocument.containsKey("source-meta")) {
//                                                        sorceMetas = (List) recordDocument.get("source-meta");
//                                                        var29 = sorceMetas.iterator();
//
//                                                        while (var29.hasNext()) {
//                                                            document = (Document) var29.next();
//                                                            if (document.containsKey("source-id")) {
//                                                                sourceIds = (List) document.get("source-id");
//                                                                subjects = new Document();
//                                                                subjects.put("@source-id-type", "IMI");
//                                                                subjects.put("_text", sourceId);
//                                                                sourceIds.add(subjects);
//                                                            }
//                                                        }
//                                                    }
//
//                                                    if (recordDocument.containsKey("article-meta")) {
//                                                        sorceMetas = (List) recordDocument.get("article-meta");
//                                                        var29 = sorceMetas.iterator();
//
//                                                        while (var29.hasNext()) {
//                                                            document = (Document) var29.next();
//                                                            if (document.containsKey("article-id")) {
//                                                                sourceIds = (List) document.get("article-id");
//                                                                subjects = new Document();
//                                                                subjects.put("@pub-id-type", "IMI");
//                                                                subjects.put("_text", articleId);
//                                                                sourceIds.add(subjects);
//                                                            }
//                                                        }
//                                                    }
//                                                } while (!recordDocument.containsKey("process-group"));
//
//                                                o2 = recordDocument.get("process-group");
//                                            } while (!(o2 instanceof Document));
//
//                                            Document processGroup = (Document) o2;
//                                            List<Document> processDate = (List) processGroup.get("process-date");
//                                            Iterator var65 = processDate.iterator();
//
//                                            while (var65.hasNext()) {
//                                                subjects = (Document) var65.next();
//                                                Object o1 = subjects.get("_text");
//                                                if (o1 instanceof Date) {
//                                                    String time = DateUtil.getTime((Date) o1);
//                                                    subjects.put("_text", time);
//                                                }
//                                            }
//                                        }
//                                    }
//                                } else {
//                                    System.out.println("数据缺失 " + dataId);
//                                }
//                            }
//                        }
//
//                        KafkaProducerUtil.sendFusionData(matchList, source);
//                    }
//
//                    System.out.println("第一步完成 ");
//                    System.out.println("发布融合中间件完成");
//                    ++dateIndex;
//                }
//            } catch (Exception var49) {
//                var49.printStackTrace();
//            } finally {
////                MongoClientUtils.closeConnection();
//            }
//        }
//    }

}
