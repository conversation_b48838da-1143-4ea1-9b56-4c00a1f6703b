package com.ruoyi.storage.kafkacommon.controller;

import com.ruoyi.storage.kafkacommon.domain.FlowLogVO;
import com.ruoyi.storage.kafkacommon.service.KafkaProducer;
import com.ruoyi.storage.utils.ConstantsFlow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 流程日志处理控制器
 */
@Slf4j
@RestController
@RequestMapping("/flow")
public class FlowLogController {

    @Autowired
    private KafkaProducer kafkaProducer;
    //提供解析映射和数据归一样例
    /**
     * 处理解析映射
     *
     * @param sourceId 数据来源
     * @param dataType 数据类型
     * @return 处理结果
     */
    @PostMapping("/mapping")
    public String processMapping(@RequestParam String sourceId,
            @RequestParam String dataType,@RequestParam String id) {
        try {
            log.info("开始处理解析映射: sourceId={}, dataType={},id={}", sourceId, dataType, id);
            FlowLogVO vo = new FlowLogVO();
            vo.setSourceId(sourceId);
            vo.setDataType(dataType);
            vo.setId(id);
            vo.setCreateTime(new Date());
            vo.setStatus(ConstantsFlow.TOOLS_MAPPING);
            kafkaProducer.sendFlowLog(vo);
            return "解析映射处理已启动";
        } catch (Exception e) {
            log.error("解析映射处理失败: sourceId={}, dataType={}", sourceId, dataType, e);
            return "解析映射处理失败: " + e.getMessage();
        }
    }

    /**
     * 处理数据归一
     *
     * @param sourceId 数据来源
     * @param dataType 数据类型
     * @return 处理结果
     */
    @PostMapping("/deduplication")
    public String processDeduplication(@RequestParam String sourceId,
            @RequestParam String dataType,@RequestParam String id) {
        try {
            log.info("开始处理数据归一: sourceId={}, dataType={},id={}", sourceId, dataType, id);
            FlowLogVO vo = new FlowLogVO();
            vo.setSourceId(sourceId);
            vo.setDataType(dataType);
            vo.setId(id);//篇级ID
            vo.setPId("ID");//数据的一级ID，例如：期刊ID、图书ID、会议ID
            vo.setCreateTime(new Date());
            vo.setStatus(ConstantsFlow.TOOLS_DUPLICATION);
            kafkaProducer.sendFlowLog(vo);
            return "数据归一处理已启动";
        } catch (Exception e) {
            log.error("数据归一处理失败: sourceId={}, dataType={}", sourceId, dataType, e);
            return "数据归一处理失败: " + e.getMessage();
        }
    }

    /**
     * 获取处理状态常量
     *
     * @return 处理状态常量
     */
    @GetMapping("/status")
    public String getProcessStatus() {
        return String.format("""
                处理状态常量:
                - 解析映射: %d
                - 数据校验: %d
                - 著录化规范: %d
                - 数据归一(查重): %d
                - 数据融合: %d
                """,
                ConstantsFlow.TOOLS_MAPPING,
                ConstantsFlow.TOOLS_VERIFICATION,
                ConstantsFlow.TOOLS_CATALOGING,
                ConstantsFlow.TOOLS_DUPLICATION,
                ConstantsFlow.TOOLS_FUSION);
    }
}