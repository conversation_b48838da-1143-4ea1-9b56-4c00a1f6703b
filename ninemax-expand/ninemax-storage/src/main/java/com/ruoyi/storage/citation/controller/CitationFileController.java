package com.ruoyi.storage.citation.controller;

import com.alibaba.fastjson2.JSONObject;
import com.anwen.mongo.model.PageParam;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.citation.domain.CitationFile;
import com.ruoyi.storage.citation.domain.CitationIssueTask;
import com.ruoyi.storage.citation.domain.CitationJournal;
import com.ruoyi.storage.citation.mapper.CitationFileMapper;
import com.ruoyi.storage.citation.service.ICitationFileService;
import com.ruoyi.storage.citation.service.ICitationIssueTaskService;
import com.ruoyi.storage.citation.service.impl.CitationFileServiceImpl;
import com.ruoyi.storage.citation.vo.CitationFileDataVo;
import com.ruoyi.storage.citation.vo.CitationJournalDataVo;
import com.ruoyi.storage.citation.vo.CitationJournalExportData;
import com.ruoyi.storage.citation.vo.FusionArticleExportData;
import com.ruoyi.storage.mongocommon.domain.FusionArticleProductMO;
import com.ruoyi.storage.mongocommon.param.QueryFusionArticleProductMOReqVO;
import com.ruoyi.storage.mongocommon.service.IFusionArticleProductMOService;
import com.ruoyi.storage.mongocommon.service.IQuoteArticleMOService;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * 引文上传任务表 控制器
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@RestController
@RequestMapping("/citationFile")
@Validated
public class CitationFileController extends BaseController<CitationFileMapper, CitationFile> {
    
    public CitationFileController(CitationFileServiceImpl service) {
        super(service, CitationFile.class);
    }

    @Resource
    private ICitationFileService citationFileService;

    @Resource
    private IFusionArticleProductMOService  fusionArticleProductMoService;

    @Resource
    private ICitationIssueTaskService citationIssueTaskService;

    @Resource
    private IQuoteArticleMOService quoteArticleMoService;

    /**
     * 查询上传导入表列表
     */
    @PostMapping("/list")
    public R<?> citationJournalList(@RequestBody CitationFileDataVo citationFileDataVo) {
        return R.ok(citationFileService.queryList(new Page<>(citationFileDataVo.getPageNum(), citationFileDataVo.getPageSize()), citationFileDataVo));
    }

    @PostMapping("/getDetail")
    public R<?> getCitationFileDetail(@RequestBody CitationFileDataVo citationFileDataVo){
        List<CitationIssueTask> lists = citationIssueTaskService.getCitationTasListByMd5(citationFileDataVo.getMd5());
        return citationFileService.citationFileList(citationFileDataVo, lists);
    }

    @PostMapping("relateFusionArticle")
    public R<?> getCitationFileRelateFusionArticle(@RequestBody CitationFileDataVo citationFileDataVo){
        // TODO 上传导入详情页数据关联
        citationFileService.CitationFileRelateFusionArticle(citationFileDataVo);
        return null;
    }

    @DeleteMapping("deleteArticle")
    public R<?> deleteArticleData(String doi){
        // TODO 删除引文库中的内容
        quoteArticleMoService.deleteByDoi(doi);
        return R.ok();
    }

    @Log(title = "上传导入管理详情导出融合篇级数据", businessType = BusinessType.EXPORT)
    @PostMapping("/exportFusionArticle")
    public void exportCitationJournalList(HttpServletResponse response, CitationFileDataVo citationFileDataVo){
        QueryFusionArticleProductMOReqVO queryFusionArticleProductMoReqVo = new QueryFusionArticleProductMOReqVO();
        queryFusionArticleProductMoReqVo.setDoi(citationFileDataVo.getDoi());
        queryFusionArticleProductMoReqVo.setArticleTitle(citationFileDataVo.getArticleTitle());
        queryFusionArticleProductMoReqVo.setJournalTitle(citationFileDataVo.getJournalTitle());
        queryFusionArticleProductMoReqVo.setRelateStatus(citationFileDataVo.getRelateStatus());
        queryFusionArticleProductMoReqVo.setFusionJournalId(citationFileDataVo.getJournalId());
        List<JSONObject> records = fusionArticleProductMoService.queryFusionArticleProduct(queryFusionArticleProductMoReqVo);
        ExcelUtil<FusionArticleExportData> util = new ExcelUtil<>(FusionArticleExportData.class);
        List<FusionArticleExportData> exportDataList = records.stream()
                .map(cj -> {
                    FusionArticleExportData exportData = new FusionArticleExportData();
                    BeanUtils.copyProperties(cj, exportData); // 将 jsonObject 的属性复制到 FusionArticleExportData
                    return exportData;
                })
                .toList();
        util.exportExcel(response, exportDataList, "sheet1");
    }

    /**
     * 获取融合库篇级数据列表
     * @param citationFileDataVo
     * @return
     */
    @PostMapping("/getFusionArticleList")
    public R<?> getFusionArticleList(@RequestBody CitationFileDataVo citationFileDataVo) {
        List<CitationIssueTask> issueTaskList = citationIssueTaskService.getCitationTasListByMd5(citationFileDataVo.getMd5());
        List<String> journalList = issueTaskList.stream().map(CitationIssueTask::getJournalId).toList();
        QueryFusionArticleProductMOReqVO queryFusionArticleProductMoReqVo = new QueryFusionArticleProductMOReqVO();
        queryFusionArticleProductMoReqVo.setJournalIds(journalList);
        queryFusionArticleProductMoReqVo.setDoi(citationFileDataVo.getDoi());
        queryFusionArticleProductMoReqVo.setArticleTitle(citationFileDataVo.getArticleTitle());
        queryFusionArticleProductMoReqVo.setJournalTitle(citationFileDataVo.getJournalTitle());
        queryFusionArticleProductMoReqVo.setRelateStatus(citationFileDataVo.getRelateStatus());
        return R.ok(fusionArticleProductMoService.queryFusionArticleProductByPage(queryFusionArticleProductMoReqVo,
                new PageParam(citationFileDataVo.getPageNum(), citationFileDataVo.getPageSize())));
    }
}
