package com.ruoyi.storage.processingtool.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.storage.annotation.Query;
import com.ruoyi.storage.enums.QueryType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExportRuleBaseVO {

    private final static String DOC_TYPE = "yyyy-MM-dd HH:mm:ss";
    private final static String TOOL_STATUS = "DATA_SENT=样例数据已发送,DATA_RECEIVED=样例数据已接受,DATA_RECEIVED_FAIL=样例数据接收失败,REVISION_UNTREATED=修订规则待接收,REVISION_SENT=修订规则已发送,REVISION_RECEIVED=修改规则接收成功,INIT_SUCCESS=初始规则已返回,INIT_FAIL=初始规则生成失败,REVISION_RECEIVED_FAIL=修订规则接收失败";

    /**
     * 数据源
     */
    @Excel(name = "数据源", width = 20)
    private String sourceId;

    /**
     * 规则名称
     */
    @Excel(name = "规则名称", width = 20)
    private String name;

    /**
     * 文献类型
     */
    @Excel(name = "文献类型", readConverterExp = DOC_TYPE, width = 20)
    private String docType;

    /**
     * 解析规则处理状态  'EXECUTING','PENDING','COMPLETED'
     */
    @Excel(name = "规则处理状态", readConverterExp = "EXECUTING=规则生成中,PENDING=有待处理节点,COMPLETED=无待处理节点")
    private String ruleStatus;

    /**
     * 工具交互状态
     */
    @Excel(name = "工具交互状态", readConverterExp = TOOL_STATUS)
    private String toolStatus;

    /**
     * 应用状态
     */
    @Excel(name = "应用状态", readConverterExp = "Y=启用,N=停用")
    private String useStatus;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date updateTime;

}
