package com.ruoyi.storage.processingtool.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.bean.BeanUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.storage.base.controller.BaseController;
import com.ruoyi.storage.processingtool.domain.RuleAnalysis;
import com.ruoyi.storage.processingtool.mapper.RuleAnalysisMapper;
import com.ruoyi.storage.processingtool.service.IRuleAnalysisService;
import com.ruoyi.storage.processingtool.service.impl.RuleAnalysisServiceImpl;
import com.ruoyi.storage.processingtool.vo.AddRuleAnalysisReqVO;
import com.ruoyi.storage.processingtool.vo.ExportRuleAnalysisVO;
import com.ruoyi.storage.processingtool.vo.UpdateRuleAnalysisStatusReqVO;
import com.ruoyi.storage.searchbase.service.IDataOriginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ruleAnalysis")
@Validated
public class RuleAnalysisController extends BaseController<RuleAnalysisMapper, RuleAnalysis> {
    RuleAnalysisController(RuleAnalysisServiceImpl service) {
        super(service, RuleAnalysis.class);
    }

    @Autowired
    private IRuleAnalysisService ruleAnalysisService;

    @Autowired
    private IDataOriginService dataOriginService;

    /**
     * 更新路径状态
     *
     * @param updateAnalysisStatusReqVO 更改接口应用状态请求参数
     * @return 操作结果
     */
    @PostMapping("/updateAnalysisStatus")
    public R<?> updateAnalysisStatus(@Valid @RequestBody UpdateRuleAnalysisStatusReqVO updateAnalysisStatusReqVO) {
        ruleAnalysisService.updateAnalysisStatus(updateAnalysisStatusReqVO);
        return R.ok();
    }

    /**
     * 添加规则分析
     *
     * @param addRuleAnalysisReqVO 添加规则分析请求参数
     * @return 操作结果
     */
    @PostMapping("/addRuleAnalysis")
    public R<?> addRuleAnalysis(@Valid @RequestBody AddRuleAnalysisReqVO addRuleAnalysisReqVO) {
        ruleAnalysisService.addRuleAnalysis(addRuleAnalysisReqVO);
        return R.ok();
    }

    /**
     * 导出数据
     *
     * @param response HTTP响应对象
     * @param ruleAnalysis 查询条件实体
     */
    @Log(title = "通用-导出", businessType = BusinessType.EXPORT)
    @Override
    @PostMapping("/export")
    public void export(HttpServletResponse response, RuleAnalysis ruleAnalysis) {
        try {
            List<RuleAnalysis> list = service.queryList(ruleAnalysis);
            // 进行数据转换处理
            List<ExportRuleAnalysisVO> exportVOList = new ArrayList<>();
            BeanUtils.copyProperties(list, exportVOList);
            AtomicReference<Integer> number = new AtomicReference<>(1);
            exportVOList.forEach( item -> item.setNumber(number.getAndSet(number.get() + 1)));
            ExcelUtil<ExportRuleAnalysisVO> util = new ExcelUtil<>(ExportRuleAnalysisVO.class);
            util.exportExcel(response, exportVOList, "sheet1");
        } catch (Exception e) {
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

}
