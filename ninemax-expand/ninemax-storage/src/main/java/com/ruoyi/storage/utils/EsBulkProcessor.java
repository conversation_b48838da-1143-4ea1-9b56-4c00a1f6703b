package com.ruoyi.storage.utils;

import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;

/**
 * Elasticsearch批量处理器，用于将单条消息缓冲后批量写入ES
 * 支持基于数量和时间的双重触发机制，提升写入性能
 */
@Slf4j
public class EsBulkProcessor {

    // Elasticsearch客户端
    @Resource
    public RestHighLevelClient esClient;
    // 缓冲区，存储待批量处理的消息
    private final BlockingQueue<Object> bufferQueue = new LinkedBlockingQueue<>(10000);
    // 批量提交阈值，达到该数量触发批量写入
    private final int batchSize=1000;
    // 最大等待时间，超过此时长即使未达阈值也触发写入
    private final long maxWaitMillis=500;
    // Elasticsearch索引名称
    private final String indexName="logs";
    // 定时任务线程池，处理超时提交
    private final ScheduledExecutorService scheduler;
    // 防止并发提交的原子标记
    private final AtomicBoolean isCommitting = new AtomicBoolean(false);
    // 对象转换函数，将业务对象转为ES文档Map
    private final Function<Object, Map<String, Object>> objectMapper;

    /**
     * 构造函数
     * @param objectMapper        对象转Map的函数
     */
    public EsBulkProcessor(Function<Object, Map<String, Object>> objectMapper) {
        this.objectMapper = objectMapper;
        // 初始化定时任务线程池(守护线程)
        this.scheduler = Executors.newSingleThreadScheduledExecutor(runnable -> {
            Thread thread = new Thread(runnable, "es-bulk-processor-scheduler");
            thread.setDaemon(true);
            return thread;
        });
        // 启动定时任务，定期检查并提交
        this.scheduler.scheduleAtFixedRate(this::commitIfNeeded, maxWaitMillis, maxWaitMillis, TimeUnit.MILLISECONDS);
    }

    /**
     * 添加单条消息到缓冲区
     *
     * @param message 待处理的业务消息对象
     * @throws InterruptedException 如果线程被中断
     */
    public void add(Object message) throws InterruptedException {
        if (message == null) {
            log.warn("忽略空消息");
            return;
        }
        // 放入缓冲区(满时会阻塞等待)
        bufferQueue.put(message);
        // 检查是否达到批量阈值
        if (bufferQueue.size() >= batchSize) {
            commitIfNeeded();
        }
    }

    /**
     * 检查并提交缓冲区数据
     */
    private void commitIfNeeded() {
        // CAS操作确保只有一个线程执行提交
        if (!isCommitting.compareAndSet(false, true)) {
            log.trace("已有提交操作在执行，当前请求跳过");
            return;
        }
        try {
            int currentSize = bufferQueue.size();
            if (currentSize == 0) {
                log.trace("缓冲区为空，无需提交");
                return;
            }
            log.debug("准备提交批量数据，条数: {}", currentSize);
            // 从缓冲区取出所有数据
            List<Object> batch = new ArrayList<>(currentSize);
            bufferQueue.drainTo(batch);
            // 执行批量写入
            bulkIndex(batch);
        } catch (Exception e) {
            log.error("批量提交异常", e);
        } finally {
            // 释放提交锁
            isCommitting.set(false);
        }
    }

    /**
     * 执行批量写入ES操作
     */
    private void bulkIndex(List<Object> batch) {
        if (CollectionUtils.isEmpty(batch)) {
            log.trace("批量数据为空，无需处理");
            return;
        }
        BulkRequest bulkRequest = new BulkRequest();
        for (Object item : batch) {
            try {
                // 转换对象为ES文档格式
                Map<String, Object> sourceMap = objectMapper.apply(item);
                if (sourceMap == null) {
                    log.warn("对象转换失败，跳过数据: {}", item);
                    continue;
                }
                // 创建索引请求
                IndexRequest indexRequest = new IndexRequest(indexName).source(sourceMap);
                bulkRequest.add(indexRequest);
            } catch (Exception e) {
                log.error("处理单条数据异常，将重试: {}", item, e);
                // 异常数据重新加入缓冲区
                try {
                    bufferQueue.put(item);
                } catch (InterruptedException ie) {
                    log.error("重新加入缓冲区失败", ie);
                    Thread.currentThread().interrupt();
                }
            }
        }

        // 执行批量请求
        if (bulkRequest.numberOfActions() > 0) {
            try {
                log.info("执行ES批量写入: 索引={}, 条数={}", indexName, bulkRequest.numberOfActions());
                BulkResponse response = esClient.bulk(bulkRequest, RequestOptions.DEFAULT);

                if (response.hasFailures()) {
                    log.error("批量写入部分失败: {}", response.buildFailureMessage());
                    // 处理失败项(简单重试)
                    retryFailedItems(batch);
                } else {
                    log.info("批量写入成功: 索引={}, 条数={}", indexName, bulkRequest.numberOfActions());
                }
            } catch (IOException e) {
                log.error("ES批量写入IO异常，将重试所有数据", e);
                retryFailedItems(batch);
            }
        } else {
            log.warn("批量请求中无有效操作，不执行写入");
        }
    }

    /**
     * 重试失败的批量数据
     */
    private void retryFailedItems(List<Object> items) {
        for (Object item : items) {
            try {
                // 重试时使用offer避免阻塞，缓冲区满时放弃并记录
                if (!bufferQueue.offer(item, 1, TimeUnit.SECONDS)) {
                    log.error("缓冲区已满，无法重试数据: {}", item);
                    // 此处可添加死信队列处理逻辑
                }
            } catch (InterruptedException e) {
                log.error("重试数据加入缓冲区失败", e);
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 关闭处理器，释放资源并提交剩余数据
     */
    public void close() {
        log.info("关闭EsBulkProcessor，开始处理剩余数据");
        // 关闭定时任务
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                log.warn("定时任务未能正常终止，强制关闭");
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            log.warn("等待定时任务终止被中断", e);
            scheduler.shutdownNow();
        }
        // 提交剩余数据
        commitIfNeeded();
        log.info("EsBulkProcessor已关闭");
    }
}