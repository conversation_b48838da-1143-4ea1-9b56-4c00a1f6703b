package com.ruoyi.storage.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AnalysisMappingRule{

    @JsonProperty("pathId")
    private String pathId;

    @JsonProperty("srcPath")
    private String srcPath;

    @JsonProperty("imiPath")
    private String imiPath;

    @JsonProperty("status")
    private String status;

    @JsonProperty("analysisStatus")
    private String analysisStatus;

    @JsonProperty("sort")
    private Integer sort;

    // List<String>
    @JsonProperty("sampleValue")
    private Object sampleValue;

    @JsonProperty("samplePath")
    private Object samplePath;

    @JsonProperty("frequency")
    private Integer frequency;

    @JsonProperty("iskey")
    private String iskey;
}
