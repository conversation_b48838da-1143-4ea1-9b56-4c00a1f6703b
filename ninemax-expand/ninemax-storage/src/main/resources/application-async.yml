# 异步任务配置
storage:
  async:
    # 默认超时时间（分钟）
    defaultTimeoutMinutes: 30
    # 默认最大重试次数
    defaultMaxRetryCount: 3
    # 回调地址前缀
    callbackUrlPrefix: http://localhost:8080
    # 线程池配置
    corePoolSize: 10
    maxPoolSize: 20
    queueCapacity: 100
    threadNamePrefix: async-task-
    # 定时任务配置
    timeoutCheckInterval: 5  # 超时任务检查间隔（分钟）
    retryCheckInterval: 10   # 重试任务检查间隔（分钟）
    enableScheduledTasks: true  # 是否启用定时任务

# 日志配置
logging:
  level:
    com.ruoyi.storage.async: DEBUG
    com.ruoyi.storage.utils.RestUtils: DEBUG
