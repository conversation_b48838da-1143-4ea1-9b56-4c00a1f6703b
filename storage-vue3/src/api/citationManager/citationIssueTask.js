// src/api/citationIssueTask.js
export const CITATION_ISSUE_TASK_URL ='/' + import.meta.env.VITE_APP_PROJECT_NAME + '/citationIssueTask';
import request from '@/utils/request.js';

/* ================= 卷期任务管理 ================= */

/**
 * 1. 卷期任务列表（分页 + 期刊名称模糊查询）
 * 
 * @param {Object} data 请求参数
 * @param {String} data.journalTitle 期刊标题
 * @param {String} data.journalId 期刊ID
 * @param {String} data.year 年份
 * @param {String} data.volume 卷号
 * @param {String} data.issue 期号
 * @param {String} data.status 状态
 * @param {String} data.startCreateTime 开始创建时间
 * @param {String} data.endCreateTime 结束创建时间
 * @param {String} data.pageNum 页码
 * @param {String} data.pageSize 每页条数
 */
export function listCitationIssueTask(data) {
    return request({
        url: CITATION_ISSUE_TASK_URL + '/getCitationIssueTaskList',
        method: 'post',
        data
    });
}

/**
 * 2. 导入卷期任务(详情页批量导入共用)
 * 
 * @param {File} file Excel文件
 * @param {String|Number} handleType 处理类型，默认传0
 * @param {String} source 数据源，默认pubmed
 * @param {String} rule 规则，默认doi,articleId
 */
export function importCitationIssueTask(file, handleType = '0', source = 'pubmed', rule = 'doi,articleId') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('handleType', handleType);
    formData.append('source', source);
    formData.append('rule', rule);

    return request({
        url: CITATION_ISSUE_TASK_URL + '/importCitationIssueTaskList',
        method: 'post',
        data: formData,
        headers: { 'Content-Type': 'multipart/form-data' }
    });
}

/**
 * 3. 卷期任务详情
 * 
 * @param {String} taskCode 卷期任务唯一编码，如 FJB01230718ajk9E0
 */
export function getCitationIssueTaskDetail(taskCode) {
    return request({
        url: `${CITATION_ISSUE_TASK_URL}/getDetail/${taskCode}`,
        method: 'get'
    });
}

/**
 * 4. 详情页篇级数据列表
 * 
 * @param {String} journalId 期刊ID
 */
export function getArticlesByJournal(journalId) {
    return request({
        url: CITATION_ISSUE_TASK_URL + '/getArticlesByJournal',
        method: 'get',
        params: { journalId }
    });
}

/**
 * 5. 详情页篇级数据被引列表
 * 
 * @param {String} journalId 期刊ID
 */
export function getCitedInfo(journalId) {
    return request({
        url: CITATION_ISSUE_TASK_URL + '/getCitedInfo',
        method: 'get',
        params: { journalId }
    });
}

/**
 * 6. 详情页引文导入
 * 
 * @param {Object} data 引文导入数据
 * @param {String} data.articleId 文章ID
 * @param {String} data.artileTitle 文章标题
 * @param {String} data.journalId 期刊ID
 * @param {String} data.journalTitle 期刊标题
 */
export function importCitationFile(data) {
    return request({
        url: CITATION_ISSUE_TASK_URL + '/importCitationFile',
        method: 'post',
        data: data
    });
}