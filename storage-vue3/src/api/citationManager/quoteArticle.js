// src/api/quoteArticle.js
import request from '@/utils/request'

const BASE_URL = '/' + import.meta.env.VITE_APP_PROJECT_NAME +'/quoteArticle'

/* ------------------ 施引数据管理 ------------------ */

/**
 * 查询施引数据管理列表（分页 + 条件）
 * POST /quoteArticle/quoteArticleList
 * @param {Object} data { articleId, citeType, pageNum, pageSize }
 */
export function listQuoteArticle(data = { pageNum: 1, pageSize: 10 }) {
  return request({
    url: `${BASE_URL}/quoteArticleList`,
    method: 'post',
    data
  })
}

/**
 * 查询施引详情页面
 * GET /quoteArticle/getQuoteArticleDetail/{id}
 * @param {String|Number} id
 */
export function getQuoteArticleDetail(id) {
  return request({
    url: `${BASE_URL}/getQuoteArticleDetail/${id}`,
    method: 'get'
  })
}

/**
 * 施引数据更新（表单提交）
 * POST /quoteArticle/updateQuoteArticle
 * @param {Object} form { id, articleId, articleTitle, ... }
 */
export function updateQuoteArticle(form) {
  const formData = new FormData()
  Object.keys(form).forEach(key => formData.append(key, form[key]))
  return request({
    url: `${BASE_URL}/updateQuoteArticle`,
    method: 'post',
    data: formData,
  })
}

/**
 * 被引数据删除(批量)
 * POST /quoteArticle/deleteQuoteArticle
 * @param {Object} form { id }
 */
export function deleteBatchQuoteArticle(idsStr) {
  const formData = new FormData()
  formData.append('removeData', idsStr)
  return request({
    url: `${BASE_URL}/deleteQuoteArticle`,
    method: 'post',
    data: formData,
  })
}