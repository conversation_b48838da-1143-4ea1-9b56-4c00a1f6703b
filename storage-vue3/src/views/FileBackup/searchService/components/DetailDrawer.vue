<template>
  <!-- 详情抽屉 -->
  <div v-show="visible" class="drawer-content-new">
    <div class="drawer-header">
      <div class="my_drawer_title">
        <span class="drawer-title">详情信息</span>
      </div>
      <div class="my_drawer_title_right">
        <span @click="handleClose">关闭</span>
      </div>
    </div>

    <div class="drawer-content-body">
      <el-scrollbar>
        <!-- 右侧抽屉控制按钮 -->
        <span class="drawer-close" @click="handleClose">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            width="14px"
            height="36px"
          >
            <image
              x="0px"
              y="0px"
              width="14px"
              height="36px"
              xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAkCAMAAACOofuzAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAZlBMVEUAeNcTgtqZye8Sgdk3ld88l+C/3fVvs+gEetfT6PhXpuQchtvj8Pozk98sj93n8voki9xEm+Hb7PkUgtpgqubP5fcIfNh/u+u32PSXyO+byu+32fRDm+FfquY0k99YpuQ4ld////8FVq+6AAAAAWJLR0QhxGwNFgAAAAd0SU1FB+kGFBcQFgrbx3cAAABWSURBVCjPY2BAAoxMDCiAmWHIABYGBlYkLhs7BycXgsvNw8vHL4DgCwoJi4iKIfjiEgySUshcaQYpBFdGSFgWoZibRw7ZKJBF8gidCmA0RAFqdDMyAQBIegM0UmhjDwAAAABJRU5ErkJggg=="
            />
          </svg>
        </span>

        <!-- 详情表单 -->
        <div class="form-container">
          <el-form :model="detailData" label-width="auto" size="small" label-position="left">
            <!-- 文章ID -->
            <el-form-item prop="articleId">
              <template #label> 文章ID： </template>
              <el-input v-model="detailData.articleId" readonly />
            </el-form-item>

            <!-- 文章题名 -->
            <el-form-item prop="articleTitle">
              <template #label> 文章题名： </template>
              <el-input v-model="detailData.articleTitle" readonly />
            </el-form-item>

            <!-- 期刊题名 -->
            <el-form-item prop="sourceTitle">
              <template #label> 期刊题名： </template>
              <el-input v-model="detailData.sourceTitle" readonly />
            </el-form-item>

            <!-- ISSN -->
            <el-form-item prop="issn">
              <template #label> ISSN： </template>
              <el-input v-model="detailData.issn" readonly />
            </el-form-item>

            <!-- 出版年 -->
            <el-form-item prop="pubYear">
              <template #label> 出版年： </template>
              <el-input v-model="detailData.pubYear" readonly />
            </el-form-item>

            <!-- 数据源 -->
            <el-form-item prop="sourceName">
              <template #label> 数据源： </template>
              <el-input v-model="detailData.sourceName" readonly />
            </el-form-item>

            <!-- PDF链接 -->
            <el-form-item prop="pdf_web">
              <template #label> PDF链接： </template>
              <div class="link-display">
                <el-button
                  v-if="detailData.pdf_web"
                  type="primary"
                  text
                  @click="openLink(detailData.pdf_web)"
                >
                  查看PDF
                </el-button>
                <span v-else class="empty-text">-</span>
              </div>
            </el-form-item>

            <!-- 图像链接 -->
            <el-form-item prop="png_web">
              <template #label> 图像链接： </template>
              <div class="link-display">
                <el-button
                  v-if="detailData.png_web"
                  type="primary"
                  text
                  @click="openLink(detailData.png_web)"
                >
                  查看图像
                </el-button>
                <span v-else class="empty-text">-</span>
              </div>
            </el-form-item>

            <!-- Excel链接 -->
            <el-form-item prop="excel_web">
              <template #label> Excel链接： </template>
              <div class="link-display">
                <el-button
                  v-if="detailData.excel_web"
                  type="primary"
                  text
                  @click="openLink(detailData.excel_web)"
                >
                  查看Excel
                </el-button>
                <span v-else class="empty-text">-</span>
              </div>
            </el-form-item>

            <!-- 数据链接 -->
            <el-form-item prop="data_web">
              <template #label> 数据链接： </template>
              <div class="link-display">
                <el-button
                  v-if="detailData.data_web"
                  type="primary"
                  text
                  @click="openLink(detailData.data_web)"
                >
                  查看数据
                </el-button>
                <span v-else class="empty-text">-</span>
              </div>
            </el-form-item>

            <!-- 文件类型 -->
            <el-form-item prop="fileType">
              <template #label> 文件类型： </template>
              <el-input v-model="detailData.fileType" readonly />
            </el-form-item>

            <!-- 更新时间 -->
            <el-form-item prop="updateTime">
              <template #label> 更新时间： </template>
              <el-input v-model="detailData.updateTime" readonly />
            </el-form-item>
          </el-form>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  detailData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:visible', 'close'])

// 详情数据
const detailData = reactive({
  articleId: '',
  articleTitle: '',
  sourceTitle: '',
  issn: '',
  pubYear: '',
  sourceName: '',
  pdf_web: '',
  png_web: '',
  excel_web: '',
  data_web: '',
  fileType: '',
  updateTime: '',
})

// 关闭抽屉
const handleClose = () => {
  emit('close')
  emit('update:visible', false)
}

// 打开链接
const openLink = (link) => {
  if (link && link !== '-') {
    window.open(link, '_blank')
  }
}

// 监听详情数据变化
watch(() => props.detailData, (newData) => {
  if (newData) {
    Object.assign(detailData, newData)
  }
}, { immediate: true, deep: true })

// 监听visible变化，关闭时重置数据
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    Object.assign(detailData, {
      articleId: '',
      articleTitle: '',
      sourceTitle: '',
      issn: '',
      pubYear: '',
      sourceName: '',
      pdf_web: '',
      png_web: '',
      excel_web: '',
      data_web: '',
      fileType: '',
      updateTime: '',
    })
  }
})
</script>

<style scoped>
/* 抽屉样式 */
.drawer-content-new {
  width: 483px;
  max-height: calc(100vh - 37px);
  background: white;
  flex-shrink: 0;
  margin-top: 38px;
  border-left: 1px solid #bccde0;
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  z-index: 1000;
}

.drawer-header {
  padding: 10px 16px 10px 16px;
  border-bottom: 1px solid rgb(232, 232, 232);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.my_drawer_title {
  display: flex;
  justify-content: space-between;
}

.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: 'MicrosoftYaHei';
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
    cursor: pointer;
  }

  .btn_add {
    background-color: rgb(0, 120, 212);
  }
}

.drawer-title {
  font-size: 16px;
  font-family: 'MicrosoftYaHei';
  color: rgb(0, 120, 212);
}

.drawer-content-body {
  height: calc(100% - 44px);
  background-color: #eff4fa;
  position: relative;
}

.drawer-close {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(20px);
  cursor: pointer;
  z-index: 99;
}

/* 表单样式 - 增加特异性以覆盖全局样式 */
.drawer-content-new .form-container {
  border-radius: 2px;
  border: 1px solid rgb(221, 229, 235);
  background-color: rgb(255, 255, 255);
  overflow: hidden;
  margin: 10px;
}

.drawer-content-new .form-container :deep(.el-form-item--small) {
  margin-bottom: 0 !important;
}

.drawer-content-new .form-container :deep(.el-input__wrapper) {
  height: 32px !important;
  border-radius: 0 !important;
}

.drawer-content-new .form-container :deep(.el-select--small .el-select__wrapper) {
  height: 32px !important;
  border-radius: 0 !important;
}

/* 确保标签区域背景色连续 - 增加特异性 */
.drawer-content-new .form-container :deep(.el-form .el-form-item) {
  display: flex !important;
  margin-bottom: 0 !important;
}

.drawer-content-new .form-container :deep(.el-form .el-form-item .el-form-item__label) {
  font-size: 14px !important;
  font-family: 'MicrosoftYaHei' !important;
  color: rgb(0, 0, 0) !important;
  font-weight: bold !important;
  padding: 6px 0 6px 15px !important;
  height: 40px !important;
  line-height: 28px !important;
  background-color: #f7f7f7 !important;
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
  flex-shrink: 0 !important;
  text-align: left !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  border: none !important;
  margin: 0 !important;
}

.drawer-content-new .form-container :deep(.el-form .el-form-item .el-form-item__content) {
  flex: 1 !important;
  margin: 0 !important;
  padding: 6px 6px 6px 9px !important;
  background-color: #fff !important;
}

/* 只读输入框样式 */
.drawer-content-new .form-container :deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #f5f7fa !important;
  border-color: #e4e7ed !important;
  color: #606266 !important;
}

/* 状态显示样式 */
.status-display {
  padding: 6px 9px;
  background-color: #fff;
  height: 32px;
  display: flex;
  align-items: center;
}

.status-success {
  color: #000000;
  font-weight: 600;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.status-success::before {
  content: '●';
  font-size: 8px;
  color: #67c23a;
}

.status-failed {
  color: #000000;
  font-weight: 600;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.status-failed::before {
  content: '●';
  font-size: 8px;
  color: #f56c6c;
}

/* 链接显示样式 */
.link-display {
  padding: 6px 9px;
  background-color: #fff;
  height: 32px;
  display: flex;
  align-items: center;
}

.empty-text {
  color: #c0c4cc;
  font-size: 14px;
}

.link-text {
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

.link-display :deep(.el-button) {
  color: #1c6dd0;
  font-weight: 500;
  font-size: 14px;
  font-family: 'Microsoft YaHei', sans-serif;
  padding: 0;
  height: auto;
  line-height: 1;
}
</style>
