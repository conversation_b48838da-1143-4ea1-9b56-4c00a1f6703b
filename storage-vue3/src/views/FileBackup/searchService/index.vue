<template>
  <div class="research-data-view">
    <!-- 主内容区域 -->
    <div class="main-content" :class="{ 'drawer-open': detailDrawerVisible }">
      <!--论文期刊表格 -->
      <div class="upper-section">
        <!-- 查询筛选区域 -->
        <el-form
          :model="searchForm"
          ref="queryRef"
          :inline="true"
          size="default"
          label-width="0"
          class="form-query-box"
        >
          <el-input
            v-model="searchForm.articleTitle"
            placeholder="文章题名"
            clearable
            style="width: 200px; margin-right: 10px"
          />
          <el-input
            v-model="searchForm.sourceTitle"
            placeholder="期刊题名"
            clearable
            style="width: 200px; margin-right: 10px"
          />
          <el-input
            v-model="searchForm.issn"
            placeholder="ISSN"
            clearable
            style="width: 200px; margin-right: 10px"
          />
          <el-select
            v-model="searchForm.sourceName"
            placeholder="数据源"
            clearable
            style="width: 200px; margin-right: 10px"
          >
            <el-option label="自扫描" value="自扫描" />
            <el-option label="印本自采集" value="印本自采集" />
            <el-option label="E-only自采集" value="E-only自采集" />
            <el-option label="数据率采集" value="数据率采集" />
            <el-option label="PMC" value="PMC" />
            <el-option label="OA资源" value="OA资源" />
            <el-option label="汇交成果" value="汇交成果" />
          </el-select>
          <el-select
            v-model="searchForm.fileType"
            placeholder="文献类型"
            clearable
            style="width: 200px; margin-right: 10px"
          >
            <el-option label="期刊（J）" value="期刊（J）"></el-option>
            <el-option label="图书（M）" value="图书（M）"></el-option>
            <el-option label="会议（C）" value="会议（C）"></el-option>
            <el-option label="报告（T）" value="报告（T）"></el-option>
          </el-select>
          <el-select
            v-model="searchForm.fileType"
            placeholder="文件类型"
            clearable
            style="width: 200px; margin-right: 10px"
          >
            <el-option label="PDF全文" value="PDF全文"></el-option>
            <el-option label="图像" value="图像"></el-option>
            <el-option label="图表" value="图表"></el-option>
            <el-option label="研究数据" value="研究数据"></el-option>
          </el-select>
          <el-form-item>
            <el-button type="primary" class="my_primary_btn" @click="handleQuery">搜索</el-button>
            <el-button @click="handleClear" class="my_clean_btn">清空</el-button>
          </el-form-item>
        </el-form>
        <hr />

        <!-- 数据表格 -->
        <div class="table-container">
          <!-- 统计数据 -->
          <div class="table-statistics">
            <span class="statistics-text">
              <em></em>全部数据共 <b>{{ pagination.total }}</b> 条
            </span>
          </div>
          <div class="table-wrapper">
            <el-table
              :data="tableData"
              stripe
              style="width: 100%"
              :loading="loading"
              class="scrollable-table"
            >
              <el-table-column
                type="selection"
                width="55"
                align="center"
                fixed="left"
              ></el-table-column>
              <el-table-column
                prop="articleId"
                label="文章ID"
                width="180"
                align="center"
                fixed="left"
              ></el-table-column>
              <el-table-column
                prop="articleTitle"
                label="文章题名"
                align="center"
                min-width="280"
                fixed="left"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="sourceTitle"
                label="期刊题名"
                align="center"
                min-width="230"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                prop="issn"
                label="ISSN"
                align="center"
                min-width="120"
              ></el-table-column>
              <el-table-column
                prop="pubYear"
                label="出版年"
                align="center"
                min-width="100"
              ></el-table-column>
              <el-table-column
                prop="sourceName"
                label="数据源"
                align="center"
                min-width="120"
              ></el-table-column>
              <el-table-column prop="pdf_web" label="PDF链接" align="center" min-width="100">
                <template #default="scope">
                  <el-button type="primary" text @click="openLink(scope.row.pdf_web)" v-if="scope.row.pdf_web">
                    查看PDF
                  </el-button>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="png_web" label="图像链接" align="center" min-width="100">
                <template #default="scope">
                  <el-button type="primary" text @click="openLink(scope.row.png_web)" v-if="scope.row.png_web">
                    查看图像
                  </el-button>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="excel_web" label="Excel链接" align="center" min-width="100">
                <template #default="scope">
                  <el-button type="primary" text @click="openLink(scope.row.excel_web)" v-if="scope.row.excel_web">
                    查看Excel
                  </el-button>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="data_web" label="数据链接" align="center" min-width="100">
                <template #default="scope">
                  <el-button type="primary" text @click="openLink(scope.row.data_web)" v-if="scope.row.data_web">
                    查看数据
                  </el-button>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="fileType"
                label="文件类型"
                align="center"
                min-width="100"
              ></el-table-column>
              <el-table-column
                prop="updateTime"
                label="更新时间"
                align="center"
                min-width="150"
              ></el-table-column>

              <el-table-column
                label="操作"
                align="center"
                width="200"
                fixed="right"
                class-name="operation-column"
                header-class-name="operation-header"
              >
                <template #default="scope">
                  <el-button type="primary" text @click="handleEdit(scope.row)">详情</el-button>
                  <el-button text @click="handleLog(scope.row)">日志</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <span class="font">每页</span>
          <el-select
            v-model="pagination.pageSize"
            @change="handleSizeChange"
            size="small"
            class="page-size-selector"
            style="margin: 0 10px"
          >
            <el-option v-for="size in [10, 20, 50, 100]" :key="size" :label="size" :value="size" />
          </el-select>
          <span class="font">条</span>
          <el-pagination
            size="small"
            :background="true"
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            layout="prev, pager, next"
            :page-sizes="[10, 20, 50, 100]"
            :pager-count="7"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
          <span class="font">跳至</span>
          <el-input-number
            v-model="jumpPage"
            :controls="false"
            :min="1"
            :max="Math.max(1, Math.ceil(pagination.total / pagination.pageSize))"
            size="small"
            class="jump-input"
            @keyup.enter.native="handleJump"
            @change="validateJump"
            style="margin: 0 10px"
          />
          <span class="font">页</span>
        </div>
      </div>
    </div>

    <!-- 详情抽屉组件 -->
    <DetailDrawer
      v-model:visible="detailDrawerVisible"
      :detail-data="currentDetailRow"
      @close="handleDetailClose"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import DetailDrawer from './components/DetailDrawer.vue'
import { getArchiveSearchDataList } from '@/api/filebackup/archiveSearch'

// 搜索表单
const searchForm = reactive({
  articleTitle: '',
  sourceTitle: '',
  issn: '',
  sourceName: '',
  fileType: '',
  pubYear: '',
})

// 表格数据
const tableData = ref([])
const loading = ref(false)


// 详情抽屉相关状态
const detailDrawerVisible = ref(false)
const currentDetailRow = ref(null)

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
})

// 分页跳转相关
const jumpPage = ref(1)

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...searchForm
    }

    const response = await getArchiveSearchDataList(params)
    if (response && response.records) {
      tableData.value = response.records || []
      pagination.total = response.total || 0
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  pagination.currentPage = 1
  fetchData()
}

// 清空
const handleClear = () => {
  Object.assign(searchForm, {
    articleTitle: '',
    sourceTitle: '',
    issn: '',
    sourceName: '',
    fileType: '',
    pubYear: '',
  })
  pagination.currentPage = 1
  fetchData()
}

// 编辑
const handleEdit = (row) => {
  console.log('查看详情：', row)
  currentDetailRow.value = { ...row }
  detailDrawerVisible.value = true
}

// 日志
const handleLog = (row) => {
  console.log('查看日志：', row)
}

// 打开链接
const openLink = (link) => {
  console.log('打开链接：', link)
}

// 分页处理
const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.currentPage = 1
  fetchData()
}

const handleCurrentChange = (val) => {
  pagination.currentPage = val
  fetchData()
}

// 分页跳转方法
const handleJump = () => {
  const maxPage = Math.max(1, Math.ceil(pagination.total / pagination.pageSize))
  if (jumpPage.value >= 1 && jumpPage.value <= maxPage) {
    pagination.currentPage = jumpPage.value
    fetchData()
  }
}

const validateJump = () => {
  const maxPage = Math.max(1, Math.ceil(pagination.total / pagination.pageSize))
  if (jumpPage.value < 1 || jumpPage.value > maxPage) {
    console.warn('请输入有效的页码')
  }
}

// 详情抽屉关闭处理
const handleDetailClose = () => {
  detailDrawerVisible.value = false
  currentDetailRow.value = null
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.research-data-view {
  padding: 20px;
  position: relative;
}

.main-content {
  transition: all 0.3s ease;
  width: 100%;
}

.main-content.drawer-open {
  width: calc(100% - 483px);
}

.upper-section,
.lower-section,
.third-section {
  background-color: #ffffff;
  margin-bottom: 20px;
}

/* 表单查询样式 */
.form-query-box {
  margin-bottom: 10px;
  margin-top: -20px;
}

.el-form--inline .el-form-item {
  margin-bottom: 6px;
  margin-top: 6px;
  margin-right: 6px;
}

.my_primary_btn {
  background-color: rgb(0, 120, 212);
  border-color: rgb(0, 120, 212);
  padding: 0px 19px;
  border-radius: 0;
}

.my_clean_btn {
  background-color: rgb(189, 189, 189);
  font-size: 14px;
  font-family: 'MicrosoftYaHei';
  color: rgb(255, 255, 255);
  border-radius: 0;

  &:hover {
    border-color: #fff;
  }
}

.table-container {
  width: 100%;
  overflow: hidden;
  margin-top: -10px;
  padding-top: 5px;
}

.table-wrapper {
  width: 100%;
  overflow-x: auto;
}

.scrollable-table {
  width: 100% !important;
  min-width: 100%;
}

/* 全屏分割线样式 */
hr {
  border: none;
  height: 1px;
  background-color: #bccde0;
  margin-top: -10px;
  margin-left: -20px;
  margin-right: -20px;
}

/* 统计数据样式 */
.table-statistics {
  padding: 6px 10px;
  border-bottom: 1px solid #bccde0;
}

.statistics-text {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
  font-family: 'MicrosoftYaHei';
  color: rgb(56, 56, 56);
}

.statistics-text em {
  display: inline-block;
  width: 4px;
  height: 11px;
  background-color: rgb(53, 79, 156);
  border-radius: 4px;
  margin-right: 6px;
}

.statistics-text b {
  margin: 0 3px;
  font-size: 15px;
  font-weight: bold;
}

/* 表头样式 */
:deep(.el-table__header th) {
  background-color: #99ceff !important;
  color: #000000 !important;
  font-weight: bold ;
  font-family: 'Microsoft YaHei', sans-serif !important;
  font-size: 14px !important;
  height: 30px !important;
  border-color: #b3b3b3;
  border-right: 1px solid #b3b3b3;
  text-align: center;
  white-space: nowrap;
}

:deep(.el-table__header th .cell) {
  text-align: center;
  justify-content: center;
  line-height: 30px;
  white-space: nowrap !important;
  padding: 0 12px;
}

/* 表格鼠标悬停整行背景颜色 */
:deep(.el-table__row:hover > td) {
  background-color: #e7e7e7 !important;
}

/* 表格数据单元格样式 */
:deep(.el-table td) {
  white-space: nowrap !important;
  color: #000000;
  font-weight: 400;
  font-size: 14px;
  font-family: 'Microsoft YaHei', sans-serif;
  text-align: center !important;
  overflow: hidden;
  text-overflow: ellipsis;
  border-right: 1px solid #e4e7ed;
}

:deep(.el-table td .cell) {
  text-align: center;
  justify-content: center;
  white-space: nowrap !important;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 12px;
}

/* 确保表格列不会压缩过小 */
:deep(.el-table .el-table__cell) {
  white-space: nowrap !important;
  overflow: hidden;
  text-overflow: ellipsis;
  border-right: 1px solid #e4e7ed;
}

/* 表格整体布局 */
:deep(.el-table) {
  table-layout: fixed;
  border-collapse: separate;
  border-spacing: 0;
}

/* 移除最后一列的右边框 */
:deep(.el-table td:last-child),
:deep(.el-table th:last-child) {
  border-right: none;
}

/* 斑马纹样式 */
:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #f5f5f5 !important;
}

/* 操作按钮样式 */
:deep(.el-table .el-button) {
  color: #1c6dd0;
  margin-right: 1px;
  font-weight: 500;
  font-size: 14px;
  font-family: 'Microsoft YaHei', sans-serif;
}

:deep(.el-table .el-button--primary) {
  font-weight: 600;
}

/* 固定操作列的样式 */
:deep(.el-table__fixed-right) {
  box-shadow: -1px 0 8px rgba(0, 0, 0, 0.1);
}

/* 操作列表头样式 */
:deep(.el-table__fixed-right .el-table__header th) {
  background-color: #0062c7 !important;
  color: #ffffff !important;
}

/* 成功状态样式 */
.status-success {
  color: #000000;
  font-weight: 600;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.status-success::before {
  content: '●';
  font-size: 8px;
  color: #67c23a;
}

.status-failed {
  color: #000000;
  font-weight: 600;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.status-failed::before {
  content: '●';
  font-size: 8px;
  color: #f56c6c;
}

/* 分页样式 */
.pagination-container {
  background: #fff;
  padding: 10px !important;
  justify-content: flex-start !important;
  margin: 0;
  display: flex;
  align-items: center;
}

.page-size-selector {
  width: 50px;
  margin: 0 !important;
}

.font {
  font-size: 12px;
  display: flex;
  align-items: center;
  color: #606266;
  margin: 0 6px;
}

:deep(.el-pagination.is-background .btn-next),
:deep(.el-pagination.is-background .btn-prev),
:deep(.el-pagination.is-background .el-pager li) {
  background-color: #fff;
  border: 1px solid #dadada;
}

:deep(.el-pagination.is-background .btn-next.is-active),
:deep(.el-pagination.is-background .btn-prev.is-active),
:deep(.el-pagination.is-background .el-pager li.is-active) {
  background-color: #0077d3;
  border-color: #0077d3;
  color: #fff;
}

.el-input-number--small {
  width: 35px;
  margin: 0 !important;
}

:deep(.el-input-number.is-without-controls .el-input__wrapper) {
  padding: 0 !important;
}
</style>
