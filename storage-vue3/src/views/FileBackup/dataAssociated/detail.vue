<template>
  <div class="data-associated-detail">
    <!-- 返回按钮 -->
    <div class="header-section">
      <el-button @click="goBack" class="back-btn">
        <el-icon><ArrowLeft /></el-icon>
        批次数据详情
      </el-button>
    </div>

    <!-- 查询筛选区域 -->
    <el-form
      :model="searchForm"
      ref="queryRef"
      :inline="true"
      size="default"
      label-width="0"
      class="form-query-box"
    >
      <el-input
        v-model="searchForm.keyword"
        placeholder="PMC ID或文件名称"
        clearable
        style="width: 200px; margin-right: 10px"
      />
      <el-form-item>
        <el-button type="primary" class="my_primary_btn" @click="handleSearch">搜索</el-button>
        <el-button @click="handleClear" class="my_clean_btn">清空</el-button>
      </el-form-item>
    </el-form>
    <hr />

    <!-- 数据表格 -->
    <div class="table-container">
      <!-- 统计数据 -->
      <div class="table-statistics">
        <span class="statistics-text">
          <em></em>共 <b>{{ pagination.total }}</b> 条
        </span>
      </div>

      <el-table
        :data="tableData"
        stripe
        style="width: 100%"
        :loading="loading"
        class="detail-table"
      >
        <el-table-column
          prop="PMC ID"
          label="PMC ID"
          width="120"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="PMC ORG"
          label="PMC_PDF_ORG"
          align="center"
          min-width="150"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="PMC UUID"
          label="PMC_PDF_UUID"
          align="center"
          min-width="200"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="PMC WEB"
          label="PMC_PDF_WEB"
          align="center"
          min-width="200"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="PM ID"
          label="PM ID（关键）"
          width="140"
          align="center"
        ></el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <span class="font">每页</span>
        <el-select
          v-model="pagination.pageSize"
          @change="handleSizeChange"
          size="small"
          class="page-size-selector"
          style="margin: 0 10px"
        >
          <el-option v-for="size in [10, 20, 50, 100]" :key="size" :label="size" :value="size" />
        </el-select>
        <span class="font">条</span>
        <el-pagination
          size="small"
          :background="true"
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          layout="prev, pager, next"
          :page-sizes="[10, 20, 50, 100]"
          :pager-count="7"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
        <span class="font">跳至</span>
        <el-input-number
          v-model="jumpPage"
          :controls="false"
          :min="1"
          :max="Math.max(1, Math.ceil(pagination.total / pagination.pageSize))"
          size="small"
          class="jump-input"
          @keyup.enter.native="handleJump"
          @change="validateJump"
          style="margin: 0 10px"
        />
        <span class="font">页</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import { getDataLinkDetail } from '@/api/filebackup/dataassociated'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()

// 搜索表单
const searchForm = reactive({
  keyword: ''
})
const loading = ref(false)

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
})

// 分页跳转相关
const jumpPage = ref(1)

// 表格数据
const tableData = ref([])

// 获取数据关联详情列表
const fetchDataLinkDetail = async () => {
  try {
    loading.value = true
    const params = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize,
      batchId: route.params.id || route.query.batchId
    }

    // 添加搜索条件
    if (searchForm.keyword) {
      params.keyword = searchForm.keyword
    }

    // 过滤空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    console.log('请求参数：', params)
    const response = await getDataLinkDetail(params)
    console.log('API响应：', response)

    if (response && response.records) {
      tableData.value = response.records
      pagination.total = response.total || 0
      console.log('设置表格数据：', tableData.value)
      console.log('总记录数：', pagination.total)
    } else {
      console.warn('API响应格式异常：', response)
      tableData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取数据关联详情失败：', error)
    ElMessage.error('获取数据失败，请稍后重试')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 查询
const handleSearch = () => {
  console.log('查询条件：', searchForm)
  pagination.currentPage = 1
  fetchDataLinkDetail()
}

// 清空
const handleClear = () => {
  Object.assign(searchForm, {
    keyword: ''
  })
  pagination.currentPage = 1
  fetchDataLinkDetail()
}

// 分页处理
const handleSizeChange = (val) => {
  pagination.pageSize = val
  pagination.currentPage = 1
  fetchDataLinkDetail()
}

const handleCurrentChange = (val) => {
  pagination.currentPage = val
  fetchDataLinkDetail()
}

// 分页跳转方法
const handleJump = () => {
  if (jumpPage.value >= 1 && jumpPage.value <= Math.ceil(pagination.total / pagination.pageSize)) {
    pagination.currentPage = jumpPage.value
    fetchDataLinkDetail() // 跳转后获取新页面数据
  }
}

const validateJump = () => {
  if (jumpPage.value < 1 || jumpPage.value > Math.ceil(pagination.total / pagination.pageSize)) {
    console.warn('请输入有效的页码')
  }
}

// 组件挂载时获取路由参数和数据
onMounted(() => {
  // 优先从路径参数获取批次ID，如果没有则从查询参数获取
  const batchId = route.params.id || route.query.batchId
  console.log('详情页面批次ID：', batchId)
  console.log('路径参数：', route.params)
  console.log('查询参数：', route.query)

  if (batchId) {
    fetchDataLinkDetail()
  } else {
    ElMessage.error('缺少批次ID参数')
    console.error('无法获取批次ID，路径参数和查询参数都为空')
  }
})
</script>

<style scoped>
.data-associated-detail {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header-section {
  margin-bottom: 20px;
}

.back-btn {
  background-color: #fff;
  border: 1px solid #dcdfe6;
  color: #606266;
  font-size: 14px;
  padding: 8px 16px;
}

.back-btn:hover {
  color: #409eff;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}

/* 表单查询样式 */
.form-query-box {
  margin-bottom: 10px;
  margin-top: -20px;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.el-form--inline .el-form-item {
  margin-bottom: 6px;
  margin-top: 6px;
  margin-right: 6px;
}

.my_primary_btn {
  background-color: rgb(0, 120, 212);
  border-color: rgb(0, 120, 212);
  padding: 0px 19px;
  border-radius: 0;
}

.my_clean_btn {
  background-color: rgb(189, 189, 189);
  font-size: 14px;
  font-family: 'MicrosoftYaHei';
  color: rgb(255, 255, 255);
  border-radius: 0;
  padding: 0px 19px;

  &:hover {
    border-color: #fff;
  }
}

/* 全屏分割线样式 */
hr {
  border: none;
  height: 1px;
  background-color: #bccde0;
  margin-top: -10px;
  margin-left: -20px;
  margin-right: -20px;
}

.table-container {
  width: 100%;
  overflow: hidden;
  margin-top: -10px;
  padding-top: 5px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 统计数据样式 */
.table-statistics {
  padding: 6px 10px;
  border-bottom: 1px solid #bccde0;
  background-color: #fff;
}

.statistics-text {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
  font-family: 'MicrosoftYaHei';
  color: rgb(56, 56, 56);
}

.statistics-text em {
  display: inline-block;
  width: 4px;
  height: 11px;
  background-color: rgb(53, 79, 156);
  border-radius: 4px;
  margin-right: 6px;
}

.statistics-text b {
  margin: 0 3px;
  font-size: 15px;
  font-weight: bold;
}

/* 表头样式 */
:deep(.el-table__header th) {
  background-color: #99ceff !important;
  color: #000000 !important;
  font-weight: bold !important;
  font-family: 'Microsoft YaHei', sans-serif !important;
  font-size: 14px !important;
  height: 30px !important;
  border-color: #b3b3b3;
  border-right: 1px solid #b3b3b3;
  text-align: center;
  white-space: nowrap;
}

:deep(.el-table__header th .cell) {
  text-align: center;
  justify-content: center;
  line-height: 30px;
  white-space: nowrap !important;
  padding: 0 12px;
}

/* 表格鼠标悬停整行背景颜色 */
:deep(.el-table__row:hover > td) {
  background-color: #e7e7e7 !important;
}

/* 表格数据单元格样式 */
:deep(.el-table td) {
  white-space: nowrap !important;
  color: #000000;
  font-weight: 400;
  font-size: 14px;
  font-family: 'Microsoft YaHei', sans-serif;
  text-align: center !important;
  overflow: hidden;
  text-overflow: ellipsis;
  border-right: 1px solid #e4e7ed;
}

:deep(.el-table td .cell) {
  text-align: center;
  justify-content: center;
  white-space: nowrap !important;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 12px;
}

/* 确保表格列不会压缩过小 */
:deep(.el-table .el-table__cell) {
  white-space: nowrap !important;
  overflow: hidden;
  text-overflow: ellipsis;
  border-right: 1px solid #e4e7ed;
}

/* 表格整体布局 */
:deep(.el-table) {
  table-layout: fixed;
  border-collapse: separate;
  border-spacing: 0;
}

/* 移除最后一列的右边框 */
:deep(.el-table td:last-child),
:deep(.el-table th:last-child) {
  border-right: none;
}

/* 斑马纹样式 */
:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #f5f5f5 !important;
}

/* 分页样式 */
.pagination-container {
  background: #fff;
  padding: 10px !important;
  justify-content: flex-start !important;
  margin: 0;
  display: flex;
  align-items: center;
}

.page-size-selector {
  width: 50px;
  margin: 0 !important;
}

.font {
  font-size: 12px;
  display: flex;
  align-items: center;
  color: #606266;
  margin: 0 6px;
}

:deep(.el-pagination.is-background .btn-next),
:deep(.el-pagination.is-background .btn-prev),
:deep(.el-pagination.is-background .el-pager li) {
  background-color: #fff;
  border: 1px solid #dadada;
}

:deep(.el-pagination.is-background .btn-next.is-active),
:deep(.el-pagination.is-background .btn-prev.is-active),
:deep(.el-pagination.is-background .el-pager li.is-active) {
  background-color: #0077d3;
  border-color: #0077d3;
  color: #fff;
}

.el-input-number--small {
  width: 35px;
  margin: 0 !important;
}

:deep(.el-input-number.is-without-controls .el-input__wrapper) {
  padding: 0 !important;
}
</style>
