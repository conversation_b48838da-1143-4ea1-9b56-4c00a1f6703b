<template>
  <div class="document-management">
    <!-- 主内容区域 -->
    <div class="main-content" :class="{ 'drawer-open': drawerVisible }">
      <!-- 查询筛选区域 -->
      <el-form
        :model="searchForm"
        ref="queryRef"
        :inline="true"
        size="default"
        label-width="0"
        class="form-query-box"
      >
        <el-input
          v-model="searchForm.sourceName"
          placeholder="请输入文件数据源名称"
          clearable
          style="width: 200px; margin-right: 10px"
        />
        <el-select
          v-model="searchForm.documentType"
          placeholder="文献类型"
          clearable
          style="width: 200px; margin-right: 10px"
        >
          <el-option label="期刊（J）" value="期刊（J）" />
          <el-option label="图书（M）" value="图书（M）" />
          <el-option label="会议（C）" value="会议（C）" />
          <el-option label="报告（T）" value="报告（T）" />
        </el-select>
        <el-select
          v-model="searchForm.fileType"
          placeholder="文件类型"
          clearable
          style="width: 200px; margin-right: 10px"
        >
          <el-option label="PDF全文" value="PDF全文"></el-option>
          <el-option label="图像" value="图像"></el-option>
          <el-option label="图表" value="图表"></el-option>
          <el-option label="研究数据" value="研究数据"></el-option>
        </el-select>
        <el-select
          v-model="searchForm.taskStatus"
          placeholder="任务状态"
          clearable
          style="width: 200px; margin-right: 10px"
        >
          <el-option label="处理中" value="处理中"></el-option>
          <el-option label="完成" value="完成"></el-option>
          <el-option label="失败" value="失败"></el-option>
          <el-option label="暂停" value="暂停"></el-option>
        </el-select>
        <el-form-item>
          <el-button type="primary" class="my_primary_btn" @click="handleQuery">搜索</el-button>
          <el-button @click="handleClear" class="my_clean_btn">清空</el-button>
        </el-form-item>
      </el-form>
      <hr />
      <!-- 数据表格 -->
      <div class="table-container">
        <!-- 统计数据 -->
        <div class="table-statistics">
          <span class="statistics-text">
            <em></em>全部数据共计 <b>{{ pageInfo.total }}</b> 条
          </span>
        </div>
        <el-table
          :data="tableData"
          stripe
          style="width: 100%"
          :loading="loading"
          :class="{ 'drawer-open-table': drawerVisible }"
        >
          <el-table-column prop="batchId" label="批次ID" width="120" align="center"></el-table-column>
          <el-table-column
            prop="sourceName"
            label="文件数据源名称"
            align="center"
            min-width="150"
          ></el-table-column>
          <el-table-column
            prop="fileType"
            label="文件类型"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="documentType"
            label="文献类型"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column prop="taskStatus" label="任务状态" align="center" min-width="120">
            <template #default="scope">
              <div class="status-indicator">
                <span class="status-dot" :class="getStatusClass(scope.row.taskStatus)"></span>
                <span class="status-text">{{ scope.row.taskStatus }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="totalTasks"
            label="任务总数（条）"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="startTime"
            label="任务开始时间"
            align="center"
            min-width="160"
          ></el-table-column>
          <el-table-column
            prop="endTime"
            label="任务结束时间"
            align="center"
            min-width="160"
          ></el-table-column>
          <el-table-column label="操作" align="center" width="180" fixed="right">
            <template #default="scope">
              <el-button type="primary" text @click="handleEdit(scope.row)">详情</el-button>
              <el-button text @click="handleLog(scope.row)">日志</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <span class="font">每页</span>
        <el-select
          v-model="pageInfo.pageSize"
          @change="handleSizeChange"
          size="small"
          class="page-size-selector"
          style="margin: 0 10px"
        >
          <el-option v-for="size in [10, 20, 50]" :key="size" :label="size" :value="size" />
        </el-select>
        <span class="font">条</span>
        <el-pagination
          size="small"
          :background="true"
          v-model:current-page="pageInfo.pageNum"
          v-model:page-size="pageInfo.pageSize"
          layout="prev, pager, next"
          :page-sizes="[10, 20, 50]"
          :pager-count="7"
          :total="pageInfo.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
        <span class="font">跳至</span>
        <el-input-number
          v-model="jumpPage"
          :controls="false"
          :min="1"
          :max="Math.max(1, Math.ceil(pageInfo.total / pageInfo.pageSize))"
          size="small"
          class="jump-input"
          @keyup.enter.native="handleJump"
          @change="validateJump"
          style="margin: 0 10px"
        />
        <span class="font">页</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getDataLinkBatchList } from '@/api/filebackup/dataassociated'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 表格数据
const tableData = ref([])

const searchForm = reactive({
  sourceName: '',
  documentType: '',
  fileType: '',
  taskStatus: '',
})

const loading = ref(false)
const pageInfo = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})

// 分页跳转相关
const jumpPage = ref(1)

const drawerVisible = ref(false)

// 获取数据关联批次列表
const fetchDataLinkBatchList = async () => {
  try {
    loading.value = true
    const params = {
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      ...searchForm
    }

    // 过滤空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await getDataLinkBatchList(params)

    if (response && response.records) {
      tableData.value = response.records.map(item => ({
        ...item,
        // 格式化时间显示
        startTime: item.startTime ? new Date(item.startTime).toLocaleString('zh-CN') : '',
        endTime: item.endTime ? new Date(item.endTime).toLocaleString('zh-CN') : ''
      }))
      pageInfo.total = response.total || 0
    } else {
      tableData.value = []
      pageInfo.total = 0
    }
  } catch (error) {
    console.error('获取数据关联批次列表失败：', error)
    ElMessage.error('获取数据失败，请稍后重试')
    tableData.value = []
    pageInfo.total = 0
  } finally {
    loading.value = false
  }
}

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case '完成':
      return 'status-success'
    case '失败':
      return 'status-error'
    case '处理中':
      return 'status-processing'
    case '暂停':
      return 'status-warning'
    default:
      return 'status-default'
  }
}

// 查询
const handleQuery = () => {
  console.log('查询条件：', searchForm)
  pageInfo.pageNum = 1
  fetchDataLinkBatchList()
}

// 清空
const handleClear = () => {
  Object.assign(searchForm, {
    sourceName: '',
    documentType: '',
    fileType: '',
    taskStatus: '',
  })
  pageInfo.pageNum = 1
  fetchDataLinkBatchList()
}

// 编辑
const handleEdit = (row) => {
  console.log('查看详情：', row)
  console.log('批次ID：', row.batchId)

  // 检查批次ID是否存在
  if (!row.batchId) {
    ElMessage.error('批次ID不存在，无法跳转到详情页面')
    return
  }

  // 跳转到详情页面，使用路径参数传递批次ID
  router.push({
    path: `/fileBackup/dataAssociated/detail/${row.batchId}`,
    query: { batchId: row.batchId } // 同时保留查询参数作为备用
  })
}

// 日志
const handleLog = (row) => {
  console.log('查看日志：', row)
}

// 每页条数改变
const handleSizeChange = (val) => {
  pageInfo.pageSize = val
  pageInfo.pageNum = 1
  fetchDataLinkBatchList()
}

// 当前页改变
const handleCurrentChange = (val) => {
  pageInfo.pageNum = val
  fetchDataLinkBatchList()
}

// 分页跳转方法
const handleJump = () => {
  if (jumpPage.value >= 1 && jumpPage.value <= Math.ceil(pageInfo.total / pageInfo.pageSize)) {
    pageInfo.pageNum = jumpPage.value
    fetchDataLinkBatchList()
    console.log(`跳转到页码: ${jumpPage.value}`)
  }
}

const validateJump = () => {
  // 校验输入的页码范围
  if (jumpPage.value < 1 || jumpPage.value > Math.ceil(pageInfo.total / pageInfo.pageSize)) {
    console.warn('请输入有效的页码')
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchDataLinkBatchList()
})
</script>

<style scoped>
.document-management {
  padding: 20px;
  position: relative;
}

.main-content {
  transition: all 0.3s ease;
  width: 100%;
}

.main-content.drawer-open {
  width: calc(100% - 483px);
}

/* 表单查询样式 */
.form-query-box {
  margin-bottom: 10px;
  margin-top: -20px;
}

.el-form--inline .el-form-item {
  margin-bottom: 6px;
  margin-top: 6px;
  margin-right: 6px;
}

.my_primary_btn {
  background-color: rgb(0, 120, 212);
  border-color: rgb(0, 120, 212);
  padding: 0px 19px;
  border-radius: 0;
}

.my_clean_btn {
  background-color: rgb(189, 189, 189);
  font-size: 14px;
  font-family: 'MicrosoftYaHei';
  color: rgb(255, 255, 255);
  border-radius: 0;

  &:hover {
    border-color: #fff;
  }
}

.el-form--inline :deep(.left_wrapper) {
  margin-right: 0;
}

.el-form--inline :deep(.right_wrapper) {
  margin-right: 11px;
}

:deep(.left_wrapper .el-select__wrapper),
:deep(.left_wrapper .el-input__wrapper) {
  border: 1px solid #dcdfe6;
  border-right: 0;
  box-shadow: none;
  border-radius: 0;
  padding: 8px;
}

:deep(.right_wrapper .el-select__wrapper),
:deep(.right_wrapper .el-input__wrapper) {
  border: 1px solid #dcdfe6;
  box-shadow: none;
  border-radius: 0;
  padding-top: 0;
  padding-bottom: 0;
}

:deep(.left_wrapper .el-select__placeholder),
:deep(.right_wrapper .el-select__placeholder) {
  font-size: 14px;
  font-family: 'MicrosoftYaHei';
  color: rgb(1, 1, 1);
}

/* 表格容器样式 */
.table-container {
  width: 100%;
  overflow: hidden;
  margin-top: -10px;
  padding-top: 5px;
}

/* 全屏分割线样式 */
hr {
  border: none;
  height: 1px;
  background-color: #bccde0;
  margin-top: -10px;
  margin-left: -20px;
  margin-right: -20px;
}

/* 统计数据样式 */
.table-statistics {
  padding: 6px 10px;
  border-bottom: 1px solid #bccde0;
}

.statistics-text {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
  font-family: 'MicrosoftYaHei';
  color: rgb(56, 56, 56);
}

.statistics-text em {
  display: inline-block;
  width: 4px;
  height: 11px;
  background-color: rgb(53, 79, 156);
  border-radius: 4px;
  margin-right: 6px;
}

.statistics-text b {
  margin: 0 3px;
  font-size: 15px;
  font-weight: bold;
}

/* 表格样式 */
/* 表头字体加粗，背景颜色为#99ceff,高度为36 */
:deep(.el-table__header th) {
  background-color: #99ceff !important;
  color: #000000 !important;
  font-weight: bold !important;
  font-family: 'Microsoft YaHei', sans-serif !important;
  font-size: 14px !important;
  height: 30px !important;
  border-color: #b3b3b3;
  border-right: 1px solid #b3b3b3;
  text-align: center;
  white-space: nowrap;
}

:deep(.el-table__header th .cell) {
  text-align: center;
  justify-content: center;
  line-height: 30px;
  white-space: nowrap !important;
  padding: 0 12px;
}

/* 表单鼠标悬停整行背景颜色为#e7e7e7 */
:deep(.el-table__row:hover > td) {
  background-color: #e7e7e7 !important;
}

/* 表格数据单元格样式 */
:deep(.el-table td) {
  white-space: nowrap;
  color: #000000;
  font-weight: 400;
  font-size: 14px;
  font-family: 'Microsoft YaHei', sans-serif;
  text-align: center !important;
  border-right: 1px solid #e4e7ed;
}

/* 操作按钮样式 */
:deep(.el-table .el-button) {
  color: #1c6dd0;
  margin-right: 4px;
  font-weight: 500;
  font-size: 14px;
  font-family: 'Microsoft YaHei', sans-serif;
}

:deep(.el-table .el-button--primary) {
  font-weight: 600;
}

/* 抽屉展开时的表格样式 */
.drawer-open-table {
  width: 100% !important;
}

/* 当抽屉展开时，确保表格有水平滚动条 */
.main-content.drawer-open .table-container {
  overflow-x: auto;
}

/* 移除最后一列的右边框 */
:deep(.el-table td:last-child),
:deep(.el-table th:last-child) {
  border-right: none;
}

/* 确保表格列不会压缩过小 */
:deep(.el-table .el-table__cell) {
  white-space: nowrap !important;
  overflow: hidden;
  text-overflow: ellipsis;
  border-right: 1px solid #e4e7ed;
}

/* 表格整体布局 */
:deep(.el-table) {
  table-layout: fixed;
  border-collapse: separate;
  border-spacing: 0;
}

/* 确保表格数据单元格不换行 */
:deep(.el-table td .cell) {
  text-align: center;
  justify-content: center;
  white-space: nowrap !important;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 12px;
}

/* 固定操作列的样式 */
:deep(.el-table__fixed-right) {
  box-shadow: -1px 0 8px rgba(0, 0, 0, 0.1);
}

/* 操作列表头样式 */
:deep(.el-table__fixed-right .el-table__header th) {
  background-color: #0062c7 !important;
  color: #ffffff !important;
}

/* 状态指示器样式 */
.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-text {
  color: #000000;
  font-size: 14px;
  font-family: 'Microsoft YaHei', sans-serif;
}

/* 状态点颜色 */
.status-success {
  background-color: #52c41a;
}

.status-error {
  background-color: #ff4d4f;
}

.status-processing {
  background-color: #1890ff;
}

.status-warning {
  background-color: #faad14;
}

.status-default {
  background-color: #d9d9d9;
}

/* 分页样式 */
.pagination-container {
  background: #fff;
  padding: 10px !important;
  justify-content: flex-start !important;
  margin: 0;
  display: flex;
  align-items: center;
}

.page-size-selector {
  width: 50px;
  margin: 0 !important;
}

.font {
  font-size: 12px;
  display: flex;
  align-items: center;
  color: #606266;
  margin: 0 6px;
}

:deep(.el-pagination.is-background .btn-next),
:deep(.el-pagination.is-background .btn-prev),
:deep(.el-pagination.is-background .el-pager li) {
  background-color: #fff;
  border: 1px solid #dadada;
}

:deep(.el-pagination.is-background .btn-next.is-active),
:deep(.el-pagination.is-background .btn-prev.is-active),
:deep(.el-pagination.is-background .el-pager li.is-active) {
  background-color: #0077d3;
  border-color: #0077d3;
  color: #fff;
}

.el-input-number--small {
  width: 35px;
  margin: 0 !important;
}
:deep(.el-input-number.is-without-controls .el-input__wrapper) {
  padding: 0 !important;
}
</style>
