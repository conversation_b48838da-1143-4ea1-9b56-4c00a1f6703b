<template>
  <!-- 导入数据抽屉 -->
  <div v-show="visible" class="drawer-content-new">
    <div class="drawer-header">
      <div class="my_drawer_title">
        <span class="drawer-title">导入数据</span>
      </div>
      <div class="my_drawer_title_right">
        <span class="btn_add" @click="handleConfirm">确认</span>
        <span @click="handleCancel">取消</span>
      </div>
    </div>

    <div class="drawer-content-body">
      <el-scrollbar>
        <!-- 右侧抽屉控制按钮 -->
        <span class="drawer-close" @click="handleCancel">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            width="14px"
            height="36px"
          >
            <image
              x="0px"
              y="0px"
              width="14px"
              height="36px"
              xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAkCAMAAACOofuzAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAZlBMVEUAeNcTgtqZye8Sgdk3ld88l+C/3fVvs+gEetfT6PhXpuQchtvj8Pozk98sj93n8vski9xEm+Hb7PkUgtpgqubP5fcIfNh/u+u32PSXyO+byu+32fRDm+FfquY0k99YpuQ4ld////8FVq+6AAAAAWJLR0QhxGwNFgAAAAd0SU1FB+kGFBcQFgrbx3cAAABWSURBVCjPY2BAAoxMDCiAmWHIABYGBlYkLhs7BycXgsvNw8vHL4DgCwoJi4iKIfjiEgySUkhcaQYpBFdGSFgWoZibRw7ZKJBF8gidCmA0RAFqdDMyAQBIegM0UmhjDwAAAABJRU5ErkJggg=="
            />
          </svg>
        </span>

        <!-- 导入表单 -->
        <div class="form-container">
          <el-form :model="formData" label-width="120px" size="small" label-position="left">
            <!-- 文件数据源类型 -->
            <el-form-item prop="dataSourceType">
              <template #label> 文件数据源类型： </template>
              <el-input v-model="formData.dataSourceType" placeholder="请输入数据源类型" />
            </el-form-item>

            <!-- 文献类型 -->
            <el-form-item prop="literatureType">
              <template #label> 文献类型： </template>
              <el-select v-model="formData.literatureType" placeholder="请选择文献类型">
                <el-option label="期刊（J）" value="期刊（J）" />
                <el-option label="图书（M）" value="图书（M）" />
                <el-option label="会议（C）" value="会议（C）" />
                <el-option label="报告（T）" value="报告（T）" />
              </el-select>
            </el-form-item>

            <!-- 收割方式 -->
            <el-form-item prop="harvestMethod">
              <template #label> 收割方式： </template>
              <el-select v-model="formData.harvestMethod" placeholder="请选择收割方式">
                <el-option label="解析关联数据" value="解析关联数据" />
                <el-option label="系统交互" value="系统交互" />
              </el-select>
            </el-form-item>

            <!-- 文件上传 -->
            <el-form-item prop="files">
              <template #label> 选择文件： </template>
              <el-upload
                v-model:file-list="fileList"
                class="upload-demo"
                drag
                action="#"
                multiple
                :auto-upload="false"
                :on-change="handleFileChange"
                :on-remove="handleFileRemove"
              >
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    支持多种文件格式，单个文件大小不超过 100MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-form>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { UploadFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { importData } from '@/api/filebackup/dataupload'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

// 表单数据
const formData = reactive({
  dataSourceType: '',
  literatureType: '',
  harvestMethod: '',
})

// 文件列表
const fileList = ref([])

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    dataSourceType: '',
    literatureType: '',
    harvestMethod: '',
  })
  fileList.value = []
}

// 文件变化处理
const handleFileChange = (file, files) => {
  console.log('文件变化：', file, files)
}

// 文件移除处理
const handleFileRemove = (file, files) => {
  console.log('文件移除：', file, files)
}

// 确认导入
const handleConfirm = async () => {
  try {
    // 验证表单数据
    if (!formData.dataSourceType || !formData.literatureType || !formData.harvestMethod) {
      ElMessage.warning('请填写完整的表单信息')
      return
    }

    if (fileList.value.length === 0) {
      ElMessage.warning('请选择要上传的文件')
      return
    }

    console.log('确认导入数据：', formData, fileList.value)

    // 调用API导入数据
    const response = await importData({
      ...formData,
      files: fileList.value
    })

    if (response.code === 200 || response.success) {
      ElMessage.success('数据导入成功')
      emit('confirm', {
        formData: { ...formData },
        files: [...fileList.value]
      })
      resetFormData()
      emit('update:visible', false)
    } else {
      ElMessage.error(response.message || '导入失败')
    }
  } catch (error) {
    console.error('导入数据失败：', error)
    ElMessage.error('导入失败，请稍后重试')
  }
}

// 取消
const handleCancel = () => {
  resetFormData()
  emit('cancel')
  emit('update:visible', false)
}

// 监听visible变化，关闭时重置表单
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    resetFormData()
  }
})
</script>

<style scoped>
/* 抽屉样式 */
.drawer-content-new {
  width: 483px;
  max-height: calc(100vh - 37px);
  background: white;
  flex-shrink: 0;
  margin-top: 38px;
  border-left: 1px solid #bccde0;
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  z-index: 1000;
}

.drawer-header {
  padding: 10px 16px 10px 16px;
  border-bottom: 1px solid rgb(232, 232, 232);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.my_drawer_title {
  display: flex;
  justify-content: space-between;
}

.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: 'MicrosoftYaHei';
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
    cursor: pointer;
  }

  .btn_add {
    background-color: rgb(0, 120, 212);
  }
}

.drawer-title {
  font-size: 16px;
  font-family: 'MicrosoftYaHei';
  color: rgb(0, 120, 212);
}

.drawer-content-body {
  height: calc(100% - 44px);
  background-color: #eff4fa;
  position: relative;
}

.drawer-close {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(20px);
  cursor: pointer;
  z-index: 99;
}

/* 表单样式 - 增加特异性以覆盖全局样式 */
.drawer-content-new .form-container {
  border-radius: 2px;
  border: 1px solid rgb(221, 229, 235);
  background-color: rgb(255, 255, 255);
  overflow: hidden;
  margin: 10px;
}

.drawer-content-new .form-container :deep(.el-form-item--small) {
  margin-bottom: 0 !important;
}

.drawer-content-new .form-container :deep(.el-input__wrapper) {
  height: 32px !important;
  border-radius: 0 !important;
}

.drawer-content-new .form-container :deep(.el-select--small .el-select__wrapper) {
  height: 32px !important;
  border-radius: 0 !important;
}

/* 确保标签区域背景色连续 - 增加特异性 */
.drawer-content-new .form-container :deep(.el-form .el-form-item) {
  display: flex !important;
  margin-bottom: 0 !important;
}

.drawer-content-new .form-container :deep(.el-form .el-form-item .el-form-item__label) {
  font-size: 14px !important;
  font-family: 'MicrosoftYaHei' !important;
  color: rgb(0, 0, 0) !important;
  font-weight: bold !important;
  padding: 6px 0 6px 15px !important;
  height: 40px !important;
  line-height: 28px !important;
  background-color: #f7f7f7 !important;
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
  flex-shrink: 0 !important;
  text-align: left !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  border: none !important;
  margin: 0 !important;
}

.drawer-content-new .form-container :deep(.el-form .el-form-item .el-form-item__content) {
  flex: 1 !important;
  margin: 0 !important;
  padding: 6px 6px 6px 9px !important;
  background-color: #fff !important;
}

/* 上传组件样式 */
:deep(.el-upload-dragger) {
  width: 100%;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

:deep(.el-upload-dragger:hover) {
  border-color: #409eff;
}

:deep(.el-upload__text) {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

:deep(.el-upload__text em) {
  color: #409eff;
  font-style: normal;
}

:deep(.el-upload__tip) {
  font-size: 12px;
  color: #606266;
  margin-top: 7px;
}
</style>
