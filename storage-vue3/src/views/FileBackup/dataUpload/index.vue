<template>
  <div class="combined-data-view">
    <!-- 主内容区域 -->
    <div class="main-content" :class="{ 'drawer-open': drawerVisible || editDrawerVisible }">
      <!-- 上半部分：文件数据源表格 -->
      <div class="upper-section">
        <!-- 查询筛选区域 -->
        <el-form
          :model="upperSearchForm"
          ref="queryRef"
          :inline="true"
          size="default"
          label-width="0"
          class="form-query-box"
        >
          <el-input
            v-model="upperSearchForm.name"
            placeholder="请输入文件数据源类型"
            clearable
            style="width: 200px; margin-right: 10px"
          />
          <el-select
            v-model="upperSearchForm.literatureType"
            placeholder="文献类型"
            clearable
            style="width: 200px; margin-right: 10px"
          >
            <el-option label="期刊（J）" value="期刊（J）" />
            <el-option label="图书（M）" value="图书（M）" />
            <el-option label="会议（C）" value="会议（C）" />
            <el-option label="报告（T）" value="报告（T）" />
          </el-select>
          <el-select
            v-model="upperSearchForm.fileType"
            placeholder="文件类型"
            clearable
            style="width: 200px; margin-right: 10px"
          >
            <el-option label="PDF全文" value="PDF全文"></el-option>
            <el-option label="图像" value="图像"></el-option>
            <el-option label="图表" value="图表"></el-option>
            <el-option label="研究数据" value="研究数据"></el-option>
          </el-select>
          <el-form-item>
            <el-button type="primary" class="my_primary_btn" @click="handleUpperQuery"
              >搜索</el-button
            >
            <el-button @click="handleClear" class="my_clean_btn">清空</el-button>
            <el-button @click="handleImportData" style="margin-left: 20px">导入数据</el-button>
          </el-form-item>
        </el-form>
        <hr />
        <!-- 上半部分数据表格 -->
        <div class="table-container">
          <!-- 统计数据 -->
          <div class="table-statistics">
            <span class="statistics-text">
              <em></em>全部数据共计 <b>{{ upperPagination.total }}</b> 条
            </span>
          </div>
          <el-table
            :data="upperTableData"
            border
            stripe
            style="width: 100%"
            :loading="upperLoading"
            :row-class-name="getUpperRowClassName"
          >
            <el-table-column prop="id" label="编号" width="80" align="center"></el-table-column>
            <el-table-column
              prop="name"
              label="文件数据源名称"
              align="center"
              min-width="100"
            ></el-table-column>
            <el-table-column
              prop="fileType"
              label="文件类型"
              align="center"
              min-width="auto"
            ></el-table-column>
            <el-table-column
              prop="literatureType"
              label="文献类型"
              align="center"
              min-width="auto"
            ></el-table-column>
            <el-table-column
              prop="harvestMethod"
              label="收割方式"
              align="center"
              min-width="95"
            ></el-table-column>
            <el-table-column
              prop="sourceType"
              label="来源类型（权益）"
              align="center"
              min-width="110"
            ></el-table-column>
            <!-- 文献状态 -->
            <el-table-column label="文献状态" width="auto">
              <template #default="scope">
                <el-switch
                  v-model="scope.row.statusSwitch"
                  active-text="启用"
                  inactive-text="停用"
                  active-color="#0062c7"
                  inactive-color="#dcdfe6"
                  inline-prompt
                  @change="handleStatusChange(scope.row)"
                  style="--el-switch-on-color: #0062c7; --el-switch-off-color: #dcdfe6"
                />
              </template>
            </el-table-column>
            <!-- 最后一次上传任务状态 -->
            <el-table-column label="最后一次上传任务状态" align="center" min-width="150">
              <template #default="scope">
                <span
                  :class="
                    scope.row.lastUploadStatus === '成功' ? 'status-success' :
                    scope.row.lastUploadStatus === '处理中' ? 'status-processing' : 'status-failed'
                  "
                >
                  {{ scope.row.lastUploadStatus }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="createTime"
              label="创建时间"
              align="center"
              min-width="130"
            ></el-table-column>
            <el-table-column
              prop="updateTime"
              label="更新时间"
              align="center"
              min-width="130"
            ></el-table-column>
            <el-table-column
              prop="total"
              label="总数"
              align="center"
              width="100"
              class-name="total-column"
            ></el-table-column>
            <el-table-column
              label="操作"
              align="center"
              width="230"
              fixed="right"
              class-name="operation-column"
              header-class-name="operation-header"
            >
              <template #default="scope">
                <el-button type="primary" text @click="handleEdit(scope.row)">编辑</el-button>
                <el-button text @click="handleLog(scope.row)">日志</el-button>
                <el-button
                  v-if="scope.row.lastUploadStatus === '失败'"
                  text
                  @click="showFailureReason(scope.row)"
                  style="color: red"
                >
                  失败原因
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 上半部分分页 -->
        <div class="pagination-container">
          <span class="font">每页</span>
          <el-select
            v-model="upperPagination.pageSize"
            @change="handleUpperSizeChange"
            size="small"
            class="page-size-selector"
            style="margin: 0 10px"
          >
            <el-option v-for="size in [10, 20, 50, 100]" :key="size" :label="size" :value="size" />
          </el-select>
          <span class="font">条</span>
          <el-pagination
            size="small"
            :background="true"
            v-model:current-page="upperPagination.currentPage"
            v-model:page-size="upperPagination.pageSize"
            layout="prev, pager, next"
            :page-sizes="[10, 20, 50, 100]"
            :pager-count="7"
            :total="upperPagination.total"
            @size-change="handleUpperSizeChange"
            @current-change="handleUpperCurrentChange"
          />
          <span class="font">跳至</span>
          <el-input-number
            v-model="jumpPage"
            :controls="false"
            :min="1"
            :max="Math.max(1, Math.ceil(upperPagination.total / upperPagination.pageSize))"
            size="small"
            class="jump-input"
            @keyup.enter.native="handleJump"
            @change="validateJump"
            style="margin: 0 10px"
          />
          <span class="font">页</span>
        </div>
      </div>

      <hr />

      <div>
        <el-button style="background-color: #0062c7; color: white; margin-right: 10px"
          >上传任务列表</el-button
        >
        <el-button
          style="background-color: transparent; color: #0062c7; margin-left: 1600px; border: 0px"
          >全部列表></el-button
        >
      </div>

      <!-- 下半部分：上传任务表格 -->
      <div class="lower-section">
        <!-- 下半部分数据表格 -->
        <div class="table-container">
          <el-table
            :data="lowerTableData"
            border
            stripe
            style="width: 100%"
            :loading="lowerLoading"
            max-height="400"
            :row-class-name="getLowerRowClassName"
          >
            <el-table-column
              prop="id"
              label="批次号"
              align="center"
              min-width="80"
            ></el-table-column>
            <el-table-column
              prop="soulName"
              label="文件数据源名称"
              align="center"
              min-width="100"
            ></el-table-column>
            <el-table-column
              prop="literatureType"
              label="文献类型"
              align="center"
              min-width="60"
            ></el-table-column>
            <el-table-column
              prop="fileType"
              label="文件类型"
              align="center"
              min-width="60"
            ></el-table-column>
            <el-table-column label="任务状态" align="center" min-width="60"
              ><template #default="scope">
                <span :class="
                  scope.row.taskStatus === '成功' ? 'status-success' :
                  scope.row.taskStatus === '处理中' ? 'status-processing' : 'status-failed'
                ">
                  {{ scope.row.taskStatus }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="fileName"
              label="文件名称"
              align="center"
              min-width="60"
            ></el-table-column>
            <el-table-column
              prop="fileSize"
              label="文件大小"
              align="center"
              min-width="60"
            ></el-table-column>
            <el-table-column
              prop="startTime"
              label="任务开始时间"
              align="center"
              min-width="100"
            ></el-table-column>
            <el-table-column
              prop="endTime"
              label="任务结束时间"
              align="center"
              min-width="100"
            ></el-table-column>
            <el-table-column prop="remarks" label="备注" align="center" min-width="60">
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 导入数据抽屉组件 -->
    <ImportDataDrawer
      v-model:visible="drawerVisible"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />

    <!-- 编辑抽屉组件 -->
    <EditDataUploadDrawer
      v-model:visible="editDrawerVisible"
      :edit-data="currentEditRow"
      @save="handleEditSave"
      @cancel="handleEditCancel"
    />

  </div>
</template>

<script setup>
import { ElButton, ElMessageBox } from 'element-plus'
import { ref, reactive, computed, onMounted } from 'vue'
import { UploadFilled, Document } from '@element-plus/icons-vue'

import ImportDataDrawer from './components/ImportDataDrawer.vue'
import EditDataUploadDrawer from './components/EditDataUploadDrawer.vue'
import { getFileDataUploadList, getUploadTaskList } from '@/api/filebackup/dataupload'
// 上半部分搜索表单
const upperSearchForm = reactive({
  name: '',
  dataSourceType: '',
  literatureType: '',
  fileType: '',
  harvestMethod: '',
})

// 上半部分表格数据（用于显示）
const upperTableData = ref([])

// 上半部分分页数据
const upperPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
})

// 下半部分表格数据
const lowerTableData = ref([])

// 下半部分分页数据
const lowerPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
})

const upperLoading = ref(false)
const lowerLoading = ref(false)

// 抽屉相关状态
const drawerVisible = ref(false)
const editDrawerVisible = ref(false)
const currentEditRow = ref(null)

// 导入表单数据
const importFormData = reactive({
  dataSourceName: '',
  fileType: '',
  literatureType: '',
  harvestMethod: '',
})

// 编辑表单数据
const editFormData = reactive({
  id: '',
  name: '',
  fileType: '',
  literatureType: '',
  harvestMethod: '',
  relatedDataType: '',
  sourceType: '',
  relatedField: '',
  remarks: '',
})

// 上传的文件列表
const uploadedFiles = ref([
  {
    name: '文件名.zip',
    status: '智能取数',
    statusClass: 'status-processing',
    progress: '50%',
  },
  {
    name: '文件名.zip',
    status: '重复取数',
    statusClass: 'status-error',
    progress: '',
  },
  {
    name: '文件名.zip',
    status: '',
    statusClass: 'status-success',
    progress: '',
  },
])

// 计算是否有活跃的筛选条件
const hasActiveFilters = computed(() => {
  return !!(
    upperSearchForm.name ||
    upperSearchForm.literatureType ||
    upperSearchForm.fileType ||
    upperSearchForm.harvestMethod
  )
})

// 获取上半部分表格数据
const fetchUpperTableData = async () => {
  try {
    upperLoading.value = true
    const params = {
      pageNum: upperPagination.currentPage,
      pageSize: upperPagination.pageSize,
      sourceName: upperSearchForm.name || undefined,
      documentType: upperSearchForm.literatureType || undefined,
      fileType: upperSearchForm.fileType || undefined,
    }

    const response = await getFileDataUploadList(params)

    if (response && response.records) {
      // 映射API返回的数据到页面显示的字段
      upperTableData.value = response.records.map(item => ({
        id: item.sourceId,
        name: item.sourceName,
        fileType: item.fileType,
        literatureType: item.documentType,
        harvestMethod: item.harvestMethod,
        sourceType: item.sourceType,
        lastUploadStatus: item.task_status === '完成' ? '成功' : item.task_status === '处理中' ? '处理中' : '失败',
        createTime: '', // API中没有提供创建时间
        updateTime: item.endTime ? new Date(item.endTime).toLocaleString('zh-CN').replace(/\//g, '-') : '',
        total: item.totalTasks || 0,
        statusSwitch: item.sourceStatus === '启用',
        failureReason: item.failureReason || '',
        batchId: item.batchId,
        // 抽屉表单需要的额外字段
        relatedDataType: item.relatedDataType || '',
        relatedField: item.relatedField || '',
        remarks: item.remarks || item.remark || ''
      }))
      upperPagination.total = response.total || 0
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    upperTableData.value = []
    upperPagination.total = 0
  } finally {
    upperLoading.value = false
  }
}

// 获取下半部分表格数据（上传任务列表）
const fetchLowerTableData = async () => {
  try {
    lowerLoading.value = true
    const params = {
      pageNum: lowerPagination.currentPage,
      pageSize: lowerPagination.pageSize,
    }

    const response = await getUploadTaskList(params)

    if (response && response.records) {
      // 映射API返回的数据到页面显示的字段
      lowerTableData.value = response.records.map(item => ({
        id: item.batchId,
        soulName: item.sourceName,
        literatureType: item.documentType,
        fileType: item.fileType,
        taskStatus: item.taskStatus === '完成' ? '成功' : item.taskStatus === '处理中' ? '处理中' : '失败',
        fileName: item.fileName,
        fileSize: item.fileSize ? item.fileSize.toFixed(1) : '0',
        startTime: item.startTime ? new Date(item.startTime).toLocaleString('zh-CN').replace(/\//g, '-') : '',
        endTime: item.endTime ? new Date(item.endTime).toLocaleString('zh-CN').replace(/\//g, '-') : '',
        remarks: item.remark || '',
      }))
      lowerPagination.total = response.total || 0
    }
  } catch (error) {
    console.error('获取上传任务列表失败:', error)
    lowerTableData.value = []
    lowerPagination.total = 0
  } finally {
    lowerLoading.value = false
  }
}

// 上半部分查询
const handleUpperQuery = () => {
  console.log('上半部分查询条件：', upperSearchForm)
  upperPagination.currentPage = 1
  fetchUpperTableData()
}

// 清空
const handleClear = () => {
  Object.assign(upperSearchForm, {
    name: '',
    dataSourceType: '',
    literatureType: '',
    fileType: '',
    harvestMethod: '',
  })
  upperPagination.currentPage = 1
  fetchUpperTableData()
}

// 移除单个筛选条件
const removeFilter = (filterKey) => {
  upperSearchForm[filterKey] = ''
  handleUpperQuery()
}

// 状态切换处理
const handleStatusChange = (row) => {
  console.log('状态切换：', row)
}

// 编辑
const handleEdit = (row) => {
  console.log('编辑行数据：', row)
  currentEditRow.value = { ...row }
  editDrawerVisible.value = true
}

// 日志
const handleLog = (row) => {
  console.log('查看行日志：', row)
}

// 显示失败原因
const showFailureReason = (row) => {
  if (row.failureReason) {
    ElMessageBox.alert(row.failureReason, '失败原因', {
      confirmButtonText: '确定',
      type: 'error',
    })
  }
}

// 获取上半部分表格行样式类名
const getUpperRowClassName = ({ row }) => {
  const classes = []

  // 失败状态：淡红色背景
  if (row.lastUploadStatus === '失败') {
    classes.push('row-failed')
  }

  // 停用状态：淡灰色字体
  if (!row.statusSwitch) {
    classes.push('row-disabled')
  }

  return classes.join(' ')
}

// 获取下半部分表格行样式类名
const getLowerRowClassName = ({ row }) => {
  const classes = []

  // 失败状态：淡红色背景
  if (row.taskStatus === '失败') {
    classes.push('row-failed')
  }

  return classes.join(' ')
}

// 导入数据
const handleImportData = () => {
  console.log('点击导入数据')
  drawerVisible.value = true
}

const handleConfirm = () => {
  console.log('确认导入数据：', importFormData)
  // 这里可以添加导入逻辑
  drawerVisible.value = false
}

const handleCancel = () => {
  // 重置表单数据
  Object.assign(importFormData, {
    dataSourceName: '',
    fileType: '',
    literatureType: '',
    harvestMethod: '',
  })
  drawerVisible.value = false
}

// 编辑抽屉相关方法
const handleEditSave = () => {
  console.log('保存编辑数据：', editFormData)
  // 这里可以添加保存逻辑，实际项目中应该调用更新API
  // 暂时刷新数据
  fetchUpperTableData()
  editDrawerVisible.value = false
}

const handleEditCancel = () => {
  // 重置编辑表单数据
  Object.assign(editFormData, {
    id: '',
    name: '',
    fileType: '',
    literatureType: '',
    harvestMethod: '',
    relatedDataType: '',
    sourceType: '',
    relatedField: '',
    remarks: '',
  })
  editDrawerVisible.value = false
}

// 文件上传处理
const handleFileChange = (file, fileList) => {
  console.log('文件变化：', file, fileList)
}

// 分页跳转相关
const jumpPage = ref(1)

// 上半部分分页处理
const handleUpperSizeChange = (val) => {
  upperPagination.pageSize = val
  upperPagination.currentPage = 1
  fetchUpperTableData()
}

const handleUpperCurrentChange = (val) => {
  upperPagination.currentPage = val
  fetchUpperTableData()
}

// 分页跳转方法
const handleJump = () => {
  const maxPage = Math.max(1, Math.ceil(upperPagination.total / upperPagination.pageSize))
  if (jumpPage.value >= 1 && jumpPage.value <= maxPage) {
    upperPagination.currentPage = jumpPage.value
    fetchUpperTableData()
    console.log(`跳转到页码: ${jumpPage.value}`)
  }
}

const validateJump = () => {
  // 校验输入的页码范围
  const maxPage = Math.max(1, Math.ceil(upperPagination.total / upperPagination.pageSize))
  if (jumpPage.value < 1 || jumpPage.value > maxPage) {
    console.warn('请输入有效的页码')
  }
}

// 下半部分分页处理
const handleLowerSizeChange = (val) => {
  lowerPagination.pageSize = val
  lowerPagination.currentPage = 1
  fetchLowerTableData()
}

const handleLowerCurrentChange = (val) => {
  lowerPagination.currentPage = val
  fetchLowerTableData()
}

// 页面加载时初始化数据
onMounted(() => {
  fetchUpperTableData()
  fetchLowerTableData()
})
</script>

<style scoped>
.combined-data-view {
  padding: 20px;
  position: relative;
}

.main-content {
  transition: all 0.3s ease;
  width: 100%;
}

.main-content.drawer-open {
  width: calc(100% - 483px);
}

.upper-section {
  background-color: #ffffff;
  margin-bottom: 50px;
  border: #1c6dd0 1px;
}

.lower-section {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 表单查询样式 */
.form-query-box {
  margin-bottom: 10px;
  margin-top: -20px;
}

.el-form--inline .el-form-item {
  margin-bottom: 6px;
  margin-top: 6px;
  margin-right: 6px;
}

.my_primary_btn {
  background-color: rgb(0, 120, 212);
  border-color: rgb(0, 120, 212);
  padding: 0px 19px;
  border-radius: 0;
}

.my_clean_btn {
  background-color: rgb(189, 189, 189);
  font-size: 14px;
  font-family: 'MicrosoftYaHei';
  color: rgb(255, 255, 255);
  border-radius: 0;

  &:hover {
    border-color: #fff;
  }
}

.el-form--inline :deep(.left_wrapper) {
  margin-right: 0;
}

.el-form--inline :deep(.right_wrapper) {
  margin-right: 11px;
}

:deep(.left_wrapper .el-select__wrapper),
:deep(.left_wrapper .el-input__wrapper) {
  border: 1px solid #dcdfe6;
  border-right: 0;
  box-shadow: none;
  border-radius: 0;
  padding: 8px;
}

:deep(.right_wrapper .el-select__wrapper),
:deep(.right_wrapper .el-input__wrapper) {
  border: 1px solid #dcdfe6;
  box-shadow: none;
  border-radius: 0;
  padding-top: 0;
  padding-bottom: 0;
}

:deep(.left_wrapper .el-select__placeholder),
:deep(.right_wrapper .el-select__placeholder) {
  font-size: 14px;
  font-family: 'MicrosoftYaHei';
  color: rgb(1, 1, 1);
}

.filter-tags {
  margin-top: -16px;
  margin-bottom: -21px;
  padding: 6px 0;
  display: flex;
  align-items: center;
  gap: 1px;
  background-color: transparent;
}

.table-container {
  width: 100%;
  overflow: hidden;
  margin-top: -10px;
  padding-top: 5px;
}

/* 全屏分割线样式 */
hr {
  border: none;
  height: 1px;
  background-color: #bccde0;
  margin-top: -10px;
  margin-left: -20px;
  margin-right: -20px;
}

/* 统计数据样式 */
.table-statistics {
  padding: 6px 10px;
  border-bottom: 1px solid #bccde0;
}

.statistics-text {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
  font-family: 'MicrosoftYaHei';
  color: rgb(56, 56, 56);
}

.statistics-text em {
  display: inline-block;
  width: 4px;
  height: 11px;
  background-color: rgb(53, 79, 156);
  border-radius: 4px;
  margin-right: 6px;
}

.statistics-text b {
  margin: 0 3px;
  font-size: 15px;
  font-weight: bold;
}

:deep(.el-switch__core) {
  height: 24px;
  width: 60px;
  border-radius: 10px;
  display: flex;
  align-items: center; 
}

:deep(.el-switch__action) {
  width: 16px;
  height: 16px;
  top: 50%; 
  transform: translateY(-50%); 
  left: 2px;
}

:deep(.el-switch.is-checked .el-switch__action) {
  left: calc(100% - 18px);
  top: 50%; 
  transform: translateY(-50%); 
}

:deep(.el-switch__label) {
  font-size: 14px;
  font-weight: 500;
  line-height: 24px; 
  display: flex;
  align-items: center; 
}

/* 表头样式 */
:deep(.el-table__header th) {
  background-color: #99ceff !important;
  color: #000000 !important;
  font-weight: bold !important;
  font-family: 'Microsoft YaHei', sans-serif !important;
  font-size: 14px !important;
  height: 30px !important;
  border-color: #b3b3b3;
  text-align: center;
  white-space: nowrap;
}

:deep(.el-table__header th .cell) {
  text-align: center;
  justify-content: center;
  line-height: 30px;
  white-space: nowrap !important;
}

/* 表格鼠标悬停整行背景颜色 */
:deep(.el-table__row:hover > td) {
  background-color: #e7e7e7 !important;
}

/* 表格数据单元格样式 */
:deep(.el-table td) {
  white-space: nowrap;
  color: #000000;
  font-weight: 400;
  font-size: 14px;
  font-family: 'Microsoft YaHei', sans-serif;
  text-align: center !important;
}

:deep(.el-table td .cell) {
  text-align: center;
  justify-content: center;
}

/* 总数列蓝色字体 */
:deep(.total-column) {
  color: #0062c7 !important;
  font-weight: 600 !important;
}

/* 操作按钮样式 */
:deep(.el-table .el-button) {
  color: #1c6dd0;
  margin-right: 1px;
  font-weight: 500;
  font-size: 14px;
  font-family: 'Microsoft YaHei', sans-serif;
}

:deep(.el-table .el-button--primary) {
  font-weight: 600;
}

/* 固定操作列的样式 */
:deep(.el-table__fixed-right) {
  box-shadow: -1px 0 8px rgba(0, 0, 0, 0.1);
}

/* 操作列表头样式 */
:deep(.el-table__fixed-right .el-table__header th) {
  background-color: #0062c7 !important;
  color: #ffffff !important;
}

/* 成功状态样式 */
.status-success {
  color: #000000;
  font-weight: 600;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.status-success::before {
  content: '●';
  font-size: 8px;
  color: #67c23a;
}

.status-failed {
  color: #000000;
  font-weight: 600;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.status-failed::before {
  content: '●';
  font-size: 8px;
  color: #f56c6c;
}

.status-processing {
  color: #000000;
  font-weight: 600;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.status-processing::before {
  content: '●';
  font-size: 8px;
  color: #409eff;
}

.bottom-notice {
  margin-top: 20px;
  padding: 10px;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
  text-align: center;
}

.notice-text {
  color: #ff8c00;
  font-size: 14px;
  font-weight: 500;
}

/* 表格行状态样式 */
:deep(.el-table .row-failed > td) {
  background-color: #fef0f0 !important;
}

:deep(.el-table .row-disabled > td) {
  color: #c0c4cc !important;
}

/* 鼠标悬停时保持状态样式 */
:deep(.el-table .row-failed:hover > td) {
  background-color: #fde2e2 !important;
}

:deep(.el-table .row-disabled:hover > td) {
  background-color: #d7edff !important;
  color: #c0c4cc !important;
}

/* 抽屉样式 */
.drawer-content-new {
  width: 483px;
  max-height: calc(100vh - 37px);
  background: white;
  flex-shrink: 0;
  margin-top: 38px;
  border-left: 1px solid #bccde0;
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  z-index: 1000;
}

.drawer-header {
  padding: 10px 16px 10px 16px;
  border-bottom: 1px solid rgb(232, 232, 232);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.my_drawer_title {
  display: flex;
  justify-content: space-between;
}

.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: 'MicrosoftYaHei';
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
    cursor: pointer;
  }

  .btn_add {
    background-color: rgb(0, 120, 212);
  }
}

.drawer-title {
  font-size: 16px;
  font-family: 'MicrosoftYaHei';
  color: rgb(0, 120, 212);
}

.drawer-content-body {
  height: calc(100% - 44px);
  background-color: #eff4fa;
  position: relative;
}

.drawer-close {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(20px);
  cursor: pointer;
  z-index: 99;
}

/* 表单样式 */
.form-container {
  border-radius: 2px;
  border: 1px solid rgb(221, 229, 235);
  background-color: rgb(255, 255, 255);
  overflow: hidden;
  margin: 10px;

  :deep(.el-form-item--small) {
    margin-bottom: 0;
  }

  :deep(.el-input__wrapper) {
    height: 32px;
  }

  :deep(.el-select--small .el-select__wrapper) {
    height: 32px;
  }

  :deep(.el-form-item__content) {
    padding-top: 6px !important;
    padding-left: 9px !important;
    padding-right: 6px !important;
    background-color: transparent;
  }

  /* 确保标签区域背景色连续 */
  :deep(.el-form-item) {
    display: flex;

    .el-form-item__label {
      font-size: 14px;
      font-family: 'Microsoft YaHei', sans-serif;
      color: #000000;
      font-weight: bold;
      padding-left: 15px !important;
      padding-top: 6px !important;
      padding-right: 0 !important;
      height: 40px;
      line-height: 40px;
      background-color: #fafafa !important;
      width: 150px !important;
      flex-shrink: 0;
      text-align: left !important;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      em {
        font-style: normal;
        color: #eb3037;
      }
    }
  }

  /* upload项的特殊样式 */
  :deep(.upload-item) {
    .el-form-item__label {
      height: 196px !important;
      align-items: flex-start;
      padding-top: 6px !important;
      text-align: left !important;
      display: flex;
      justify-content: flex-start;
      background-color: #99ceff;
      font-family: 'Microsoft YaHei', sans-serif;
      font-weight: bold;
      color: #000000;
    }
  }
}

.warning-text {
  color: #ff8c00;
  font-size: 14px;
  margin: 10px;
  padding: 8px 12px;
  background-color: #fff8e1;
  border: 0px solid #ffd54f;
  border-radius: 4px;
}

/* 文件上传区域样式 */
.upload-area {
  width: 100%;
}

.upload-dragger {
  width: 100%;
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.2s;
}

:deep(.el-upload-dragger:hover) {
  border-color: #409eff;
}

:deep(.el-upload-dragger .el-icon--upload) {
  font-size: 40px;
  color: #c0c4cc;
  margin: 20px 0 16px;
  line-height: 50px;
}

:deep(.el-upload__text) {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

:deep(.el-upload__text em) {
  color: #409eff;
  font-style: normal;
}

:deep(.el-upload__tip) {
  font-size: 12px;
  color: #606266;
  margin-top: 7px;
}

.upload-info {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

/* 文件列表样式 */
.file-list {
  margin-top: 15px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item .el-icon {
  margin-right: 8px;
  color: #606266;
}

.file-name {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.file-status {
  margin-right: 10px;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
}

.file-status.status-processing {
  background-color: #e6f7ff;
  color: #1890ff;
}

.file-status.status-error {
  background-color: #fff2f0;
  color: #ff4d4f;
}

.file-status.status-success {
  background-color: #f6ffed;
  color: #52c41a;
}

.file-progress {
  font-size: 12px;
  color: #666;
}

/* 分页样式 */
.pagination-container {
  background: #fff;
  padding: 10px !important;
  justify-content: flex-start !important;
  margin: 0;
  display: flex;
  align-items: center;
}

.page-size-selector {
  width: 50px;
  margin: 0 !important;
}

.font {
  font-size: 12px;
  display: flex;
  align-items: center;
  color: #606266;
  margin: 0 6px;
}

:deep(.el-pagination.is-background .btn-next),
:deep(.el-pagination.is-background .btn-prev),
:deep(.el-pagination.is-background .el-pager li) {
  background-color: #fff;
  border: 1px solid #dadada;
}

:deep(.el-pagination.is-background .btn-next.is-active),
:deep(.el-pagination.is-background .btn-prev.is-active),
:deep(.el-pagination.is-background .el-pager li.is-active) {
  background-color: #0077d3;
  border-color: #0077d3;
  color: #fff;
}

.el-input-number--small {
  width: 35px;
  margin: 0 !important;
}
:deep(.el-input-number.is-without-controls .el-input__wrapper) {
  padding: 0 !important;
}
</style>

