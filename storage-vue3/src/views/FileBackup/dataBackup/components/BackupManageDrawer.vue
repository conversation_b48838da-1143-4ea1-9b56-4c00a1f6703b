<template>
  <!-- 备份管理抽屉 -->
  <div v-show="visible" class="drawer-content-new">
    <div class="drawer-header">
      <div class="my_drawer_title">
        <span class="drawer-title">备份管理</span>
      </div>
      <div class="my_drawer_title_right">
        <span class="btn_add" @click="handleSave">保存</span>
        <span @click="handleClose">取消</span>
      </div>
    </div>

    <div class="drawer-content-body">
      <el-scrollbar>
        <!-- 右侧抽屉控制按钮 -->
        <span class="drawer-close" @click="handleClose">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            width="14px"
            height="36px"
          >
            <image
              x="0px"
              y="0px"
              width="14px"
              height="36px"
              xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAkCAMAAACOofuzAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAZlBMVEUAeNcTgtqZye8Sgdk3ld88l+C/3fVvs+gEetfT6PhXpuQchtvj8Pozk98sj93n8voki9xEm+Hb7PkUgtpgqubP5fcIfNh/u+u32PSXyO+byu+32fRDm+FfquY0k99YpuQ4ld////8FVq+6AAAAAWJLR0QhxGwNFgAAAAd0SU1FB+kGFBcQFgrbx3cAAABWSURBVCjPY2BAAoxMDCiAmWHIABYGBlYkLhs7BycXgsvNw8vHL4DgCwoJi4iKIfjiEgySUshcaQYpBFdGSFgWoZibRw7ZKJBF8gidCmA0RAFqdDMyAQBIegM0UmhjDwAAAABJRU5ErkJggg=="
            />
          </svg>
        </span>

        <!-- 备份配置表单 -->
        <div class="form-container">
          <el-form :model="formData" label-width="auto" size="small" label-position="left">
            <!-- 备份源位置 -->
            <el-form-item prop="sourceLocation">
              <template #label> 
                <span class="required-mark">*</span>备份源位置： 
              </template>
              <el-input v-model="formData.sourceLocation" placeholder="备份源路径" />
            </el-form-item>

            <!-- 备份目标位置 -->
            <el-form-item prop="targetLocation">
              <template #label> 
                <span class="required-mark">*</span>备份目标位置： 
              </template>
              <el-input v-model="formData.targetLocation" placeholder="备份目标位置" />
            </el-form-item>

            <!-- 备份类型 -->
            <el-form-item prop="backupType">
              <template #label> 
                <span class="required-mark">*</span>备份类型： 
              </template>
              <el-select v-model="formData.backupType" placeholder="请选择备份类型">
                <el-option label="自动备份" value="自动备份" />
                <el-option label="手动备份" value="手动备份" />
              </el-select>
            </el-form-item>

            <!-- 备份频率 -->
            <el-form-item prop="backupFrequency">
              <template #label> 
                <span class="required-mark">*</span>备份频率： 
              </template>
              <el-select v-model="formData.backupFrequency" placeholder="请选择备份频率">
                <el-option label="三月一次" value="三月一次" />
                <el-option label="一月一次" value="一月一次" />
                <el-option label="一周一次" value="一周一次" />
                <el-option label="一天一次" value="一天一次" />
              </el-select>
            </el-form-item>

            <!-- 备份时间 -->
            <el-form-item prop="backupTime">
              <template #label> 
                <span class="required-mark">*</span>备份时间： 
              </template>
              <el-time-picker
                v-model="formData.backupTime"
                placeholder="选择时间"
                format="HH:mm:ss"
                value-format="HH:mm:ss"
              />
            </el-form-item>

            <!-- 下次备份开始时间 -->
            <el-form-item prop="nextBackupTime">
              <template #label> 
                <span class="required-mark">*</span>下次备份开始时间： 
              </template>
              <div class="readonly-field">
                {{ formData.nextBackupTime || '2025-01-08 07:00:00' }}
              </div>
            </el-form-item>

            <!-- 清除机制 -->
            <el-form-item prop="cleanupMechanism">
              <template #label> 
                <span class="required-mark">*</span>清除机制： 
              </template>
              <el-select v-model="formData.cleanupMechanism" placeholder="请选择清除机制">
                <el-option label="无" value="无" />
                <el-option label="保留最近7天" value="保留最近7天" />
                <el-option label="保留最近30天" value="保留最近30天" />
                <el-option label="保留最近90天" value="保留最近90天" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { createBackupRecord } from '@/api/filebackup/dataBackup'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'close', 'success'])

// 表单数据
const formData = reactive({
  sourceLocation: '',
  targetLocation: '',
  backupType: '自动备份',
  backupFrequency: '三月一次',
  backupTime: '',
  nextBackupTime: '2025-01-08 07:00:00',
  cleanupMechanism: '无',
})

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    sourceLocation: '',
    targetLocation: '',
    backupType: '自动备份',
    backupFrequency: '三月一次',
    backupTime: '',
    nextBackupTime: '2025-01-08 07:00:00',
    cleanupMechanism: '无',
  })
}

// 保存配置
const handleSave = async () => {
  try {
    // 验证表单数据
    if (!formData.sourceLocation || !formData.targetLocation || !formData.backupTime) {
      ElMessage.warning('请填写完整的备份配置信息')
      return
    }

    // 构造API请求数据
    const requestData = {
      backupSource: formData.sourceLocation,
      backupTarget: formData.targetLocation,
      backupType: formData.backupType,
      backupFrequency: formData.backupFrequency,
      startTime: formData.nextBackupTime,
      cleanupPolicy: formData.cleanupMechanism,
    }

    console.log('保存备份配置：', requestData)

    // 调用API创建备份记录
    const response = await createBackupRecord(requestData)

    if (response && response.code === 200) {
      ElMessage.success('备份配置保存成功')
      emit('success') // 通知父组件刷新数据
      handleClose()
    } else {
      ElMessage.error(response?.msg || '保存失败，请稍后重试')
    }
  } catch (error) {
    console.error('保存备份配置失败：', error)
    ElMessage.error('保存失败，请稍后重试')
  }
}

// 关闭抽屉
const handleClose = () => {
  resetFormData()
  emit('close')
  emit('update:visible', false)
}

// 监听visible变化，关闭时重置表单
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    resetFormData()
  }
})
</script>

<style scoped>
/* 抽屉样式 */
.drawer-content-new {
  width: 483px;
  max-height: calc(100vh - 37px);
  background: white;
  flex-shrink: 0;
  margin-top: 38px;
  border-left: 1px solid #bccde0;
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  z-index: 1000;
}

.drawer-header {
  padding: 10px 16px 10px 16px;
  border-bottom: 1px solid rgb(232, 232, 232);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.my_drawer_title {
  display: flex;
  justify-content: space-between;
}

.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: 'MicrosoftYaHei';
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
    cursor: pointer;
  }

  .btn_add {
    background-color: rgb(0, 120, 212);
  }
}

.drawer-title {
  font-size: 16px;
  font-family: 'MicrosoftYaHei';
  color: rgb(0, 120, 212);
}

.drawer-content-body {
  height: calc(100% - 44px);
  background-color: #eff4fa;
  position: relative;
}

.drawer-close {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(20px);
  cursor: pointer;
  z-index: 99;
}

/* 表单样式 - 增加特异性以覆盖全局样式 */
.drawer-content-new .form-container {
  border-radius: 2px;
  border: 1px solid rgb(221, 229, 235);
  background-color: rgb(255, 255, 255);
  overflow: hidden;
  margin: 10px;
}

.drawer-content-new .form-container :deep(.el-form-item--small) {
  margin-bottom: 0 !important;
}

.drawer-content-new .form-container :deep(.el-input__wrapper) {
  height: 32px !important;
  border-radius: 0 !important;
}

.drawer-content-new .form-container :deep(.el-select--small .el-select__wrapper) {
  height: 32px !important;
  border-radius: 0 !important;
}

/* 确保标签区域背景色连续 - 增加特异性 */
.drawer-content-new .form-container :deep(.el-form .el-form-item) {
  display: flex !important;
  margin-bottom: 0 !important;
}

.drawer-content-new .form-container :deep(.el-form .el-form-item .el-form-item__label) {
  font-size: 14px !important;
  font-family: 'MicrosoftYaHei' !important;
  color: rgb(0, 0, 0) !important;
  font-weight: bold !important;
  padding: 6px 0 6px 15px !important;
  height: 40px !important;
  line-height: 28px !important;
  background-color: #f7f7f7 !important;
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
  flex-shrink: 0 !important;
  text-align: left !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  border: none !important;
  margin: 0 !important;
}

.drawer-content-new .form-container :deep(.el-form .el-form-item .el-form-item__content) {
  flex: 1 !important;
  margin: 0 !important;
  padding: 6px 6px 6px 9px !important;
  background-color: #fff !important;
}

/* 必填标记样式 */
.required-mark {
  color: #f56c6c;
  margin-right: 4px;
}

/* 只读字段样式 */
.readonly-field {
  padding: 6px 9px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  height: 32px;
  line-height: 20px;
  display: flex;
  align-items: center;
}

/* 时间选择器样式 */
.drawer-content-new .form-container :deep(.el-time-picker) {
  width: 100%;
}

.drawer-content-new .form-container :deep(.el-time-picker .el-input__wrapper) {
  height: 32px !important;
  border-radius: 0 !important;
}
</style>
