<template>
  <div class="data-backup-view">
    <div class="main-content" :class="{ 'drawer-open': backupManageDrawerVisible }">
      <!-- 查询筛选区域 -->
      <el-form
        :model="searchForm"
        ref="queryRef"
        :inline="true"
        size="default"
        label-width="0"
        class="form-query-box"
      >
        <el-select
          v-model="searchForm.backupType"
          placeholder="备份类型"
          clearable
          style="width: 200px; margin-right: 10px"
        >
          <el-option label="自动备份" value="自动备份" />
          <el-option label="手动备份" value="手动备份" />
        </el-select>
        <el-select
          v-model="searchForm.backupStatus"
          placeholder="备份状态"
          clearable
          style="width: 200px; margin-right: 10px"
        >
          <el-option label="成功" value="成功" />
          <el-option label="失败" value="失败" />
          <el-option label="进行中" value="进行中" />
        </el-select>
        <el-form-item>
          <el-button type="primary" class="my_primary_btn" @click="handleSearch">查询</el-button>

          <el-button type="info" @click="handleBackup" class="my_info_btn">备份管理</el-button>
          <el-button type="info" @click="handleImmediateBackup" class="my_info_btn">立即备份</el-button>
          <el-button type="success" @click="handleExport" class="my_success_btn"
            >导出数据</el-button
          >
        </el-form-item>
      </el-form>
      <hr />
      <!-- 数据表格 -->
      <div class="table-container">
        <!-- 统计数据 -->
        <div class="table-statistics">
          <span class="statistics-text">
            <em></em>全部数据共计 <b>{{ pageInfo.total }}</b> 条
          </span>
        </div>
        <el-table :data="displayTableData"  stripe style="width: 100%" :loading="loading">
          <el-table-column prop="backupId" label="备份ID" width="80" align="center"></el-table-column>
          <el-table-column
            prop="backupSource"
            label="备份源位置"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="backupTarget"
            label="备份目标位置"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="backupSize"
            label="备份数据量"
            align="center"
            min-width="120"
          >
            <template #default="scope">
              <span>{{ scope.row.backupSize || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="backupFileCount"
            label="备份文件数量"
            align="center"
            min-width="150"
          ></el-table-column>
          <el-table-column
            prop="backupType"
            label="备份类型"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="backupFrequency"
            label="备份频率"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="operator"
            label="操作人"
            align="center"
            min-width="100"
          >
            <template #default="scope">
              <span>{{ scope.row.operator || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="startTime"
            label="开始时间"
            align="center"
            min-width="180"
          ></el-table-column>
          <el-table-column
            prop="nextBackupTime"
            label="下次备份时间"
            align="center"
            min-width="180"
          ></el-table-column>
          <el-table-column
            prop="cleanupPolicy"
            label="清理策略"
            align="center"
            min-width="120"
          ></el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <span class="font">每页</span>
        <el-select
          v-model="pageInfo.pageSize"
          @change="handleSizeChange"
          size="small"
          class="page-size-selector"
          style="margin: 0 10px"
        >
          <el-option v-for="size in [10, 20, 50]" :key="size" :label="size" :value="size" />
        </el-select>
        <span class="font">条</span>
        <el-pagination
          size="small"
          :background="true"
          v-model:current-page="pageInfo.page"
          v-model:page-size="pageInfo.pageSize"
          layout="prev, pager, next"
          :page-sizes="[10, 20, 50]"
          :pager-count="7"
          :total="pageInfo.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
        <span class="font">跳至</span>
        <el-input-number
          v-model="jumpPage"
          :controls="false"
          :min="1"
          :max="Math.max(1, Math.ceil(pageInfo.total / pageInfo.pageSize))"
          size="small"
          class="jump-input"
          @keyup.enter.native="handleJump"
          @change="validateJump"
          style="margin: 0 10px"
        />
        <span class="font">页</span>
      </div>
    </div>

    <!-- 备份管理抽屉组件 -->
    <BackupManageDrawer
      v-model:visible="backupManageDrawerVisible"
      @close="handleBackupManageClose"
      @success="handleBackupSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import BackupManageDrawer from './components/BackupManageDrawer.vue'
import { getDataBackupList } from '@/api/filebackup/dataBackup'

// 搜索表单
const searchForm = reactive({
  backupType: '',
  backupStatus: '',
})

// 表格数据
const tableData = ref([])
const loading = ref(false)
const pageInfo = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
})

// 分页跳转相关
const jumpPage = ref(1)

// 备份管理抽屉相关状态
const backupManageDrawerVisible = ref(false)

// 计算是否有活跃的筛选条件
// const hasActiveFilters = computed(() => {
//   return !!(searchForm.backupType || searchForm.backupStatus)
// })

// 计算属性：确保表格始终显示10行数据
const displayTableData = computed(() => {
  const currentData = [...tableData.value]
  const targetRows = 10

  // 如果数据不足10行，填充空行
  while (currentData.length < targetRows) {
    currentData.push({
      backupId: '',
      backupSource: '',
      backupTarget: '',
      backupSize: '',
      backupFileCount: '',
      backupType: '',
      backupFrequency: '',
      operator: '',
      startTime: '',
      nextBackupTime: '',
      cleanupPolicy: '',
    })
  }

  return currentData
})



// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pageInfo.page,
      pageSize: pageInfo.pageSize,
      ...searchForm
    }

    const response = await getDataBackupList(params)
    if (response && response.data) {
      tableData.value = response.data.records || []
      pageInfo.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    tableData.value = []
    pageInfo.total = 0
  } finally {
    loading.value = false
  }
}

// 查询
const handleSearch = () => {
  pageInfo.page = 1
  fetchData()
}

// 导出数据
const handleExport = () => {
  console.log('导出数据')
}

// 备份管理
const handleBackup = () => {
  console.log('备份管理')
  backupManageDrawerVisible.value = true
}

// 立即备份
const handleImmediateBackup = () => {
  console.log('立即备份')
}

// 备份管理抽屉关闭处理
const handleBackupManageClose = () => {
  backupManageDrawerVisible.value = false
}

// 备份创建成功处理
const handleBackupSuccess = () => {
  fetchData() // 刷新数据
}



// 每页条数改变
const handleSizeChange = (val) => {
  pageInfo.pageSize = val
  pageInfo.page = 1
  fetchData()
}

// 当前页改变
const handleCurrentChange = (val) => {
  pageInfo.page = val
  fetchData()
}

// 分页跳转方法
const handleJump = () => {
  const maxPage = Math.max(1, Math.ceil(pageInfo.total / pageInfo.pageSize))
  if (jumpPage.value >= 1 && jumpPage.value <= maxPage) {
    pageInfo.page = jumpPage.value
    fetchData()
  }
}

const validateJump = () => {
  const maxPage = Math.max(1, Math.ceil(pageInfo.total / pageInfo.pageSize))
  if (jumpPage.value < 1 || jumpPage.value > maxPage) {
    console.warn('请输入有效的页码')
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.data-backup-view {
  padding: 20px;
  position: relative;
}

.main-content {
  transition: all 0.3s ease;
  width: 100%;
}

.main-content.drawer-open {
  width: calc(100% - 483px);
}

/* 表单查询样式 */
.form-query-box {
  margin-bottom: 10px;
  margin-top: -20px;
}

.el-form--inline .el-form-item {
  margin-bottom: 6px;
  margin-top: 6px;
  margin-right: 6px;
}

.my_primary_btn {
  background-color: rgb(0, 120, 212);
  border-color: rgb(0, 120, 212);
  padding: 0px 19px;
  border-radius: 0;
}

.my_clean_btn {
  background-color: rgb(189, 189, 189);
  font-size: 14px;
  font-family: 'MicrosoftYaHei';
  color: rgb(255, 255, 255);
  border-radius: 0;
  padding: 0px 19px;

  &:hover {
    border-color: #fff;
  }
}

.my_success_btn {
  background-color: rgb(103, 194, 58);
  border-color: rgb(103, 194, 58);
  padding: 0px 19px;
  border-radius: 0;
}

.my_info_btn {
  background-color: rgb(255, 196, 0);
  border-color: rgb(248, 190, 0);
  padding: 0px 19px;
  border-radius: 0;
}

.left_wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.right_wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-left: auto;
}

:deep(.left_wrapper .el-select__wrapper),
:deep(.left_wrapper .el-input__wrapper) {
  border: 1px solid #dcdfe6;
  box-shadow: none;
  border-radius: 0;
  padding-top: 0;
  padding-bottom: 0;
}

:deep(.right_wrapper .el-select__wrapper),
:deep(.right_wrapper .el-input__wrapper) {
  border: 1px solid #dcdfe6;
  box-shadow: none;
  border-radius: 0;
  padding-top: 0;
  padding-bottom: 0;
}

:deep(.left_wrapper .el-select__placeholder),
:deep(.right_wrapper .el-select__placeholder) {
  font-size: 14px;
  font-family: 'MicrosoftYaHei';
  color: rgb(1, 1, 1);
}

/* 筛选标签样式 */
.filter-tags {
  margin-top: -16px;
  margin-bottom: -21px;
  padding: 6px 0;
  display: flex;
  align-items: center;
  gap: 1px;
  background-color: transparent;
}

.filter-label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
}

.filter-tag-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-tag {
  margin-right: 8px;
}

/* 表格容器样式 */
.table-container {
  width: 100%;
  overflow: hidden;
  margin-top: -10px;
  padding-top: 5px;
}

/* 全屏分割线样式 */
hr {
  border: none;
  height: 1px;
  background-color: #bccde0;
  margin-top: -10px;
  margin-left: -20px;
  margin-right: -20px;
}

/* 统计数据样式 */
.table-statistics {
  padding: 6px 10px;
  border-bottom: 1px solid #bccde0;
}

.statistics-text {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
  font-family: 'MicrosoftYaHei';
  color: rgb(56, 56, 56);
}

.statistics-text em {
  display: inline-block;
  width: 4px;
  height: 11px;
  background-color: rgb(53, 79, 156);
  border-radius: 4px;
  margin-right: 6px;
}

.statistics-text b {
  margin: 0 3px;
  font-size: 15px;
  font-weight: bold;
}

/* 表格样式 */
/* 表头字体加粗，背景颜色为#99ceff,高度为36 */
:deep(.el-table__header th) {
  background-color: #99ceff !important;
  color: #000000 !important;
  font-weight: bold !important;
  font-family: 'Microsoft YaHei', sans-serif !important;
  font-size: 14px !important;
  height: 30px !important;
  border-color: #b3b3b3;
  border-right: 1px solid #b3b3b3;
  text-align: center;
  white-space: nowrap;
}

:deep(.el-table__header th .cell) {
  text-align: center;
  justify-content: center;
  line-height: 30px;
}

/* 表单鼠标悬停整行背景颜色为#e7e7e7 */
:deep(.el-table__row:hover > td) {
  background-color: #e7e7e7 !important;
}

/* 表格数据单元格样式 */
:deep(.el-table td) {
  white-space: nowrap;
  color: #000000;
  font-weight: 400;
  font-size: 14px;
  font-family: 'Microsoft YaHei', sans-serif;
  text-align: center !important;
  height: 40px !important;
  border-right: 1px solid #e4e7ed;
}

:deep(.el-table td .cell) {
  text-align: center;
  justify-content: center;
  line-height: 40px;
}

/* 移除最后一列的右边框 */
:deep(.el-table td:last-child),
:deep(.el-table th:last-child) {
  border-right: none;
}

/* 确保表格列不会压缩过小 */
:deep(.el-table .el-table__cell) {
  white-space: nowrap !important;
  overflow: hidden;
  text-overflow: ellipsis;
  border-right: 1px solid #e4e7ed;
}

/* 表格整体布局 */
:deep(.el-table) {
  table-layout: fixed;
  border-collapse: separate;
  border-spacing: 0;
}

/* 斑马纹样式 */
:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #f5f5f5 !important;
}

/* 状态指示器样式 */
.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-text {
  color: #000000;
  font-size: 14px;
  font-family: 'Microsoft YaHei', sans-serif;
}

/* 状态点颜色 */
.status-success {
  background-color: #52c41a;
}

.status-error {
  background-color: #ff4d4f;
}

.status-processing {
  background-color: #1890ff;
}

.status-default {
  background-color: #d9d9d9;
}

/* 分页样式 */
.pagination-container {
  background: #fff;
  padding: 10px !important;
  justify-content: flex-start !important;
  margin: 0;
  display: flex;
  align-items: center;
}

.page-size-selector {
  width: 50px;
  margin: 0 !important;
}

.font {
  font-size: 12px;
  display: flex;
  align-items: center;
  color: #606266;
  margin: 0 6px;
}

:deep(.el-pagination.is-background .btn-next),
:deep(.el-pagination.is-background .btn-prev),
:deep(.el-pagination.is-background .el-pager li) {
  background-color: #fff;
  border: 1px solid #dadada;
}

:deep(.el-pagination.is-background .btn-next:hover),
:deep(.el-pagination.is-background .btn-prev:hover),
:deep(.el-pagination.is-background .el-pager li:hover) {
  color: #0062c7;
}

:deep(.el-pagination.is-background .el-pager li.is-active) {
  background-color: #0062c7;
  color: #fff;
}

.jump-input {
  width: 50px;
}

:deep(.jump-input .el-input__wrapper) {
  border: 1px solid #dadada;
  border-radius: 0;
}
</style>
