<template>
  <!-- 编辑抽屉 -->
  <div v-show="visible" class="drawer-content-new edit-drawer">
    <div class="drawer-header">
      <div class="my_drawer_title">
        <span class="drawer-title">编辑文件数据源</span>
      </div>
      <div class="my_drawer_title_right">
        <span class="btn_add" @click="handleSave">保存</span>
        <span @click="handleCancel">取消</span>
      </div>
    </div>

    <div class="drawer-content-body">
      <el-scrollbar>
        <!-- 右侧抽屉控制按钮 -->
        <span class="drawer-close" @click="handleCancel">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            width="14px"
            height="36px"
          >
            <image
              x="0px"
              y="0px"
              width="14px"
              height="36px"
              xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAkCAMAAACOofuzAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAZlBMVEUAeNcTgtqZye8Sgdk3ld88l+C/3fVvs+gEetfT6PhXpuQchtvj8Pozk98sj93n8vski9xEm+Hb7PkUgtpgqubP5fcIfNh/u+u32PSXyO+byu+32fRDm+FfquY0k99YpuQ4ld////8FVq+6AAAAAWJLR0QhxGwNFgAAAAd0SU1FB+kGFBcQFgrbx3cAAABWSURBVCjPY2BAAoxMDCiAmWHIABYGBlYkLhs7BycXgsvNw8vHL4DgCwoJi4iKIfjiEgySUkhcaQYpBFdGSFgWoZibRw7ZKJBF8gidCmA0RAFqdDMyAQBIegM0UmhjDwAAAABJRU5ErkJggg=="
            />
          </svg>
        </span>
        <!-- 使用参考文件的表单布局 -->
        <div class="form-container">
          <el-form :model="formData" label-width="auto" size="small" label-position="left">
            <!-- 文件数据源名称 -->
            <el-form-item prop="name">
              <template #label> 文件数据源名称<em>*</em>： </template>
              <el-input v-model="formData.name" placeholder="数据源名称" />
            </el-form-item>

            <!-- 文件类型 -->
            <el-form-item prop="fileType">
              <template #label> 文件类型<em>*</em>： </template>
              <el-select v-model="formData.fileType" placeholder="文件类型">
                <el-option label="PDF全文" value="PDF全文" />
                <el-option label="图像" value="图像" />
                <el-option label="图表" value="图表" />
                <el-option label="研究数据" value="研究数据" />
              </el-select>
            </el-form-item>

            <!-- 文献类型 -->
            <el-form-item prop="literatureType">
              <template #label> 文献类型<em>*</em>： </template>
              <el-select v-model="formData.literatureType" placeholder="文献类型">
                <el-option label="期刊（J）" value="期刊（J）" />
                <el-option label="图书（M）" value="图书（M）" />
                <el-option label="会议（C）" value="会议（C）" />
                <el-option label="报告（T）" value="报告（T）" />
              </el-select>
            </el-form-item>

            <!-- 收割方式 -->
            <el-form-item prop="harvestMethod">
              <template #label> 收割方式<em>*</em>： </template>
              <el-select
                v-model="formData.harvestMethod"
                placeholder="收割方式"
              >
                <el-option label="解析关联数据" value="解析关联数据" />
                <el-option label="系统交互" value="系统交互" />
              </el-select>
            </el-form-item>

            <!-- 关联数据类型 -->
            <!-- <el-form-item prop="relatedDataType">
              <template #label> 关联数据类型： </template>
              <el-select v-model="formData.relatedDataType" placeholder="请选择">
                <el-option label="元数据" value="元数据" />
                <el-option label="全文数据" value="全文数据" />
                <el-option label="引用数据" value="引用数据" />
              </el-select>
            </el-form-item> -->

            <!-- 来源类型（权益） -->
            <el-form-item prop="sourceType">
              <template #label> 来源类型（权益）： </template>
              <el-select v-model="formData.sourceType" placeholder="来源类型">
                <el-option label="商业订购（附赠）" value="商业订购（附赠）" />
                <el-option label="开放获取" value="开放获取" />
                <el-option label="自建资源" value="自建资源" />
              </el-select>
            </el-form-item>

            <!-- 关联连接字段 -->
            <el-form-item prop="relatedFields">
              <template #label> 关联字段： </template>
              <el-input v-model="formData.relatedFields" placeholder="字段名称" />
            </el-form-item>

            <!-- 备注 -->
            <!-- <el-form-item prop="remarks" class="textarea-item">
              <template #label> 备注： </template>
              <el-input
                v-model="formData.remarks"
                type="textarea"
                :rows="4"
                placeholder="请输入备注信息"
              />
            </el-form-item> -->
          </el-form>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { updateFileDataSource } from '@/api/filebackup/filedatasource'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:visible', 'save', 'cancel'])

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  fileType: '',
  literatureType: '',
  harvestMethod: '',
  sourceType: '',
  relatedFields: '', // 与表格保持一致
})

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    id: '',
    name: '',
    fileType: '',
    literatureType: '',
    harvestMethod: '',
    sourceType: '',
    relatedFields: '',
  })
}

// 填充编辑数据
const fillEditData = (data) => {
  Object.assign(formData, {
    id: data.id || '',
    name: data.name || '',
    fileType: data.fileType || '',
    literatureType: data.literatureType || '',
    harvestMethod: data.harvestMethod || '',
    sourceType: data.sourceType || '',
    relatedFields: data.relatedFields || '',
  })
}

// 保存
const handleSave = async () => {
  try {
    console.log('编辑保存数据：', formData)
    console.log('关联字段值：', formData.relatedFields)
    
    const response = await updateFileDataSource(formData)
    
    if (response.code === 200) {
      ElMessage.success('数据源更新成功')
      emit('save', { ...formData })
      emit('update:visible', false)
    }
  } catch (error) {
    console.error('更新失败：', error)
    ElMessage.error('更新失败，请稍后重试')
  }
}

// 取消
const handleCancel = () => {
  resetFormData()
  emit('cancel')
  emit('update:visible', false)
}

// 监听editData变化
watch(() => props.editData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    fillEditData(newData)
  }
}, { deep: true, immediate: true })

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    resetFormData()
  } else if (props.editData && Object.keys(props.editData).length > 0) {
    fillEditData(props.editData)
  }
})
</script>

<style scoped>
/* 抽屉样式 */
.drawer-content-new {
  width: 483px;
  max-height: calc(100vh - 37px);
  background: white;
  flex-shrink: 0;
  margin-top: 38px;
  border-left: 1px solid #bccde0;
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  z-index: 1000;
}

.drawer-header {
  padding: 10px 16px 10px 16px;
  border-bottom: 1px solid rgb(232, 232, 232);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.my_drawer_title {
  display: flex;
  justify-content: space-between;
}

.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: 'MicrosoftYaHei';
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
    cursor: pointer;
  }

  .btn_add {
    background-color: rgb(0, 120, 212);
  }
}

.drawer-title {
  font-size: 16px;
  font-family: 'MicrosoftYaHei';
  color: rgb(0, 120, 212);
}

.drawer-content-body {
  height: calc(100% - 44px);
  background-color: #eff4fa;
  position: relative;
}

.drawer-close {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(20px);
  cursor: pointer;
  z-index: 99;
}

/* 编辑抽屉特定样式 */
.edit-drawer {
  z-index: 1001; /* 确保编辑抽屉在新建抽屉之上 */
}

/* 表单样式 - 增加特异性以覆盖全局样式 */
.drawer-content-new .form-container {
  border-radius: 2px;
  border: 1px solid rgb(221, 229, 235);
  background-color: rgb(255, 255, 255);
  overflow: hidden;
  margin: 10px;
}

.drawer-content-new .form-container :deep(.el-form-item--small) {
  margin-bottom: 0 !important;
}

.drawer-content-new .form-container :deep(.el-input__wrapper) {
  height: 32px !important;
  border-radius: 0 !important;
}

.drawer-content-new .form-container :deep(.el-select--small .el-select__wrapper) {
  height: 32px !important;
  border-radius: 0 !important;
}

.drawer-content-new .form-container :deep(.el-textarea__inner) {
  border-radius: 0 !important;
  resize: none !important;
}

/* 确保标签区域背景色连续 - 增加特异性 */
.drawer-content-new .form-container :deep(.el-form .el-form-item) {
  display: flex !important;
  margin-bottom: 0 !important;
}

.drawer-content-new .form-container :deep(.el-form .el-form-item .el-form-item__label) {
  font-size: 14px !important;
  font-family: 'MicrosoftYaHei' !important;
  color: rgb(0, 0, 0) !important;
  font-weight: bold !important;
  padding: 6px 0 6px 15px !important;
  height: 40px !important;
  line-height: 28px !important;
  background-color: #f7f7f7 !important;
  width: 150px !important;
  min-width: 150px !important;
  max-width: 150px !important;
  flex-shrink: 0 !important;
  text-align: left !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  border: none !important;
  margin: 0 !important;
}

.drawer-content-new .form-container :deep(.el-form .el-form-item .el-form-item__label em) {
  font-style: normal !important;
  color: #eb3037 !important;
}

.drawer-content-new .form-container :deep(.el-form .el-form-item .el-form-item__content) {
  flex: 1 !important;
  margin: 0 !important;
  padding: 6px 6px 6px 9px !important;
  background-color: transparent !important;
}

/* textarea项的特殊样式 */
.drawer-content-new .form-container :deep(.el-form .textarea-item .el-form-item__label) {
  height: 196px !important;
  align-items: flex-start !important;
  padding-top: 6px !important;
  text-align: left !important;
  display: flex !important;
  justify-content: flex-start !important;
  background-color: #f7f7f7 !important;
  font-family: 'MicrosoftYaHei' !important;
  font-weight: bold !important;
  color: rgb(0, 0, 0) !important;
  line-height: 28px !important;
}

.drawer-content-new .form-container :deep(.el-form .textarea-item .el-form-item__content) {
  padding: 6px 6px 6px 9px !important;
}

/* 隐藏默认的必填星号 */
.drawer-content-new .form-container :deep(.el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label-wrap>.el-form-item__label:before),
.drawer-content-new .form-container :deep(.el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label:before) {
  display: none !important;
}
</style>




