<template>
  <div class="full-text-data-source">
    <!-- 主内容区域 -->
    <div class="main-content" :class="{ 'drawer-open': drawerVisible || editDrawerVisible }">
      <!-- 查询筛选区域 -->
      <el-form
        :model="searchForm"
        ref="queryRef"
        :inline="true"
        size="default"
        label-width="0"
        class="form-query-box"
      >
        <el-input
          v-model="searchForm.name"
          placeholder="请输入数据源名称"
          clearable
          style="width: 200px; margin-right: 10px"
        />
        <el-select
          v-model="searchForm.literatureType"
          placeholder="文献类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in filterOptions.documentType"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
        <el-select
          v-model="searchForm.fileType"
          placeholder="文件类型"
          clearable
          style="width: 200px; margin-right: 10px"
        >
          <el-option
            v-for="item in filterOptions.fileType"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
        <el-select
          v-model="searchForm.harvestMethod"
          placeholder="收割方式"
          clearable
          style="width: 200px; margin-right: 10px"
        >
          <el-option
            v-for="item in filterOptions.harvestMethod"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
        <el-form-item>
          <el-button type="primary" class="my_primary_btn" @click="handleQuery">搜索</el-button>
          <el-button @click="handleClear" class="my_clean_btn">清空</el-button>
          <el-button type="success" @click="handleCreate" style="margin-left: 20px"
            >新建文件数据源</el-button
          >
        </el-form-item>
      </el-form>

      <hr />
      <!-- 数据表格 -->
      <div class="table-container">
        <!-- 统计数据 -->
        <div class="table-statistics">
          <span class="statistics-text">
            <em></em>全部数据共计 <b>{{ pageInfo.total }}</b> 条
          </span>
        </div>
        <el-table
          :data="tableData"
          stripe
          style="width: 100%"
          :loading="loading"
          :class="{ 'drawer-open-table': drawerVisible || editDrawerVisible }"
          :cell-style="{ borderRight: '2px solid #ffffff' }"
        >
          <el-table-column prop="id" label="编号" width="80" align="center"></el-table-column>
          <el-table-column
            prop="name"
            label="文件数据源名称"
            align="center"
            min-width="150"
          ></el-table-column>
          <el-table-column
            prop="fileType"
            label="文件类型"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="literatureType"
            label="文献类型"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="harvestMethod"
            label="收割方式"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="sourceType"
            label="来源类型（权益）"
            align="center"
            min-width="150"
          ></el-table-column>
          <el-table-column
            prop="relatedFields"
            label="关联字段"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            align="center"
            min-width="160"
          ></el-table-column>
          <el-table-column
            prop="updateTime"
            label="更新时间"
            align="center"
            min-width="160"
          ></el-table-column>
          <el-table-column label="操作" align="center" width="180" fixed="right">
            <template #default="scope">
              <el-button type="primary" text @click="handleEdit(scope.row)">编辑</el-button>
              <el-button text @click="handleLog(scope.row)">日志</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <span class="font">每页</span>
        <el-select
          v-model="pageInfo.pageSize"
          @change="handleSizeChange"
          size="small"
          class="page-size-selector"
          style="margin: 0 10px"
        >
          <el-option v-for="size in [10, 20, 50]" :key="size" :label="size" :value="size" />
        </el-select>
        <span class="font">条</span>
        <el-pagination
          size="small"
          :background="true"
          v-model:current-page="pageInfo.page"
          v-model:page-size="pageInfo.pageSize"
          layout="prev, pager, next"
          :page-sizes="[10, 20, 50]"
          :pager-count="7"
          :total="pageInfo.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
        <span class="font">跳至</span>
        <el-input-number
          v-model="jumpPage"
          :controls="false"
          :min="1"
          :max="Math.max(1, Math.ceil(pageInfo.total / pageInfo.pageSize))"
          size="small"
          class="jump-input"
          @keyup.enter.native="handleJump"
          @change="validateJump"
          style="margin: 0 10px"
        />
        <span class="font">页</span>
      </div>
    </div>

    <!-- 添加抽屉组件 -->
    <AddFileDataSourceDrawer
      v-model:visible="drawerVisible"
      @save="handleSave"
      @cancel="handleCancel"
    />

    <!-- 编辑抽屉组件 -->
    <EditFileDataSourceDrawer
      v-model:visible="editDrawerVisible"
      :edit-data="currentEditRow"
      @save="handleEditSave"
      @cancel="handleEditCancel"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import AddFileDataSourceDrawer from './components/AddFileDataSourceDrawer.vue'
import EditFileDataSourceDrawer from './components/EditFileDataSourceDrawer.vue'
import { getFileDataSourceList, getSourceMsg } from '@/api/filebackup/filedatasource'

const tableData = ref([])

const searchForm = reactive({
  name: '',
  literatureType: '',
  fileType: '',
  harvestMethod: '',
})

// 筛选选项数据
const filterOptions = reactive({
  documentType: [],
  harvestMethod: [],
  sourceName: [],
  fileType: []
})

const loading = ref(false)
const pageInfo = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
})

// 分页跳转相关
const jumpPage = ref(1)

// 抽屉相关状态
const drawerVisible = ref(false)
const editDrawerVisible = ref(false)
const currentEditRow = ref(null)

// 获取筛选选项
const getFilterOptions = async () => {
  try {
    console.log('开始获取筛选选项数据...')
    const response = await getSourceMsg()
    console.log('获取筛选选项接口响应：', response)
    console.log('响应数据类型：', typeof response)
    console.log('响应数据结构：', JSON.stringify(response, null, 2))

    // 检查不同的响应格式
    let data = null
    if (response && response.code === 200 && response.data) {
      data = response.data
    } else if (response && !response.code) {
      // 如果直接返回数据，没有包装
      data = response
    }

    if (data) {
      console.log('准备更新的数据：', data)
      Object.assign(filterOptions, data)
      console.log('筛选选项数据更新成功：', filterOptions)
    } else {
      console.warn('获取筛选选项失败，响应格式不正确：', response)
    }
  } catch (error) {
    console.error('获取筛选选项失败：', error)
  }
}

// 获取数据列表
const getList = async () => {
  try {
    loading.value = true
    const params = {
      pageNum: pageInfo.page,
      pageSize: pageInfo.pageSize,
      sourceName: searchForm.name || undefined,
      documentType: searchForm.literatureType || undefined,
      fileType: searchForm.fileType || undefined,
      harvestMethod: searchForm.harvestMethod || undefined,
      sourceType: searchForm.sourceType || undefined,
      relatedFields: searchForm.relatedFields || undefined,
    }

    const response = await getFileDataSourceList(params)

    if (response.code === 200) {
      // 转换字段映射
      tableData.value = response.data.records.map(item => ({
        id: item.sourceId,
        name: item.sourceName,
        fileType: item.fileType,
        literatureType: item.documentType,
        harvestMethod: item.harvestMethod,
        sourceType: item.sourceType,
        relatedFields: item.relatedFields,
        createTime: item.createTime,
        updateTime: item.updateTime,
      }))
      pageInfo.total = response.data.total
    }
  } catch (error) {
    console.error('获取数据列表失败：', error)
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  pageInfo.page = 1
  getList()
}

// 清空
const handleClear = () => {
  Object.assign(searchForm, {
    name: '',
    literatureType: '',
    fileType: '',
    harvestMethod: '',
    sourceType: '',
    relatedFields: '',
  })

  pageInfo.page = 1
  getList()
}

// 新建
const handleCreate = () => {
  console.log('点击新建文件数据源')
  drawerVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  console.log('编辑行数据：', row)
  currentEditRow.value = { ...row }
  editDrawerVisible.value = true
}

// 日志
const handleLog = (row) => {
  console.log('查看行日志：', row)
}

// 每页条数改变
const handleSizeChange = (val) => {
  pageInfo.pageSize = val
  pageInfo.page = 1
  getList()
}

// 当前页改变
const handleCurrentChange = (val) => {
  pageInfo.page = val
  getList()
}

// 分页跳转方法
const handleJump = () => {
  const maxPage = Math.max(1, Math.ceil(pageInfo.total / pageInfo.pageSize))
  if (jumpPage.value >= 1 && jumpPage.value <= maxPage) {
    pageInfo.page = jumpPage.value
    getList()
  }
}

const validateJump = () => {
  // 校验输入的页码范围
  const maxPage = Math.max(1, Math.ceil(pageInfo.total / pageInfo.pageSize))
  if (jumpPage.value < 1 || jumpPage.value > maxPage) {
    console.warn('请输入有效的页码')
  }
}

const handleSave = (formData) => {
  console.log('保存数据源：', formData)
  // 刷新列表
  getList()
}

const handleCancel = () => {
  console.log('取消新建')
}

// 编辑抽屉保存
const handleEditSave = (editFormData) => {
  console.log('保存编辑数据：', editFormData)
  // 刷新列表
  getList()
  currentEditRow.value = null
}

// 编辑抽屉取消
const handleEditCancel = () => {
  console.log('取消编辑')
  currentEditRow.value = null
}

// 页面加载时获取数据
onMounted(() => {
  console.log('页面加载完成，开始初始化数据...')
  getFilterOptions()
  getList()
})
</script>

<style scoped>
.full-text-data-source {
  padding: 20px;
  position: relative;
}

.main-content {
  transition: all 0.3s ease;
  width: 100%;
}

.main-content.drawer-open {
  width: calc(100% - 483px);
}

/* 表单查询样式 */
.form-query-box {
  margin-bottom: 10px;
  margin-top: -20px;
}

.el-form--inline .el-form-item {
  margin-bottom: 6px;
  margin-top: 6px;
  margin-right: 6px;
}

.my_primary_btn {
  background-color: rgb(0, 120, 212);
  border-color: rgb(0, 120, 212);
  padding: 0px 19px;
  border-radius: 0;
}

.my_clean_btn {
  background-color: rgb(189, 189, 189);
  font-size: 14px;
  font-family: 'MicrosoftYaHei';
  color: rgb(255, 255, 255);
  border-radius: 0;

  &:hover {
    border-color: #fff;
  }
}

.el-form--inline :deep(.left_wrapper) {
  margin-right: 0;
}

.el-form--inline :deep(.right_wrapper) {
  margin-right: 11px;
}

:deep(.left_wrapper .el-select__wrapper),
:deep(.left_wrapper .el-input__wrapper) {
  border: 1px solid #dcdfe6;
  border-right: 0;
  box-shadow: none;
  border-radius: 0;
  padding: 8px;
}

:deep(.right_wrapper .el-select__wrapper),
:deep(.right_wrapper .el-input__wrapper) {
  border: 1px solid #dcdfe6;
  box-shadow: none;
  border-radius: 0;
  padding-top: 0;
  padding-bottom: 0;
}

:deep(.left_wrapper .el-select__placeholder),
:deep(.right_wrapper .el-select__placeholder) {
  font-size: 14px;
  font-family: 'MicrosoftYaHei';
  color: rgb(1, 1, 1);
}

/* 表格容器样式 */
.table-container {
  width: 100%;
  overflow: hidden;
  margin-top: -10px;
  padding-top: 5px;
}

/* 全屏分割线样式 */
hr {
  border: none;
  height: 1px;
  background-color: #bccde0;
  margin-top: -10px;
  margin-left: -20px;
  margin-right: -20px;
}

/* 统计数据样式 */
.table-statistics {
  padding: 6px 10px;
  border-bottom: 1px solid #bccde0;
}

.statistics-text {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
  font-family: 'MicrosoftYaHei';
  color: rgb(56, 56, 56);
}

.statistics-text em {
  display: inline-block;
  width: 4px;
  height: 11px;
  background-color: rgb(53, 79, 156);
  border-radius: 4px;
  margin-right: 6px;
}

.statistics-text b {
  margin: 0 3px;
  font-size: 15px;
  font-weight: bold;
}

/* 表格样式 */
/* 表头字体加粗，背景颜色为#99ceff,高度为36 */
:deep(.el-table__header th) {
  background-color: #99ceff !important;
  color: #000000 !important;
  font-weight: bold !important;
  font-family: 'Microsoft YaHei', sans-serif !important;
  font-size: 14px !important;
  height: 30px !important;
  border-color: #b3b3b3;
  text-align: center;
  white-space: nowrap;
  border-right: 2px solid #ffffff !important; /* 添加列间空隙 */
}

/* 最后一列表头不需要右边框 */
:deep(.el-table__header th:last-child) {
  border-right: 1px solid #b3b3b3 !important;
}

:deep(.el-table__header th .cell) {
  text-align: center;
  justify-content: center;
  line-height: 30px;
}

/* 表单鼠标悬停整行背景颜色为#d7edff */
:deep(.el-table__row:hover > td) {
  background-color: #e7e7e7 !important;
  border-right: 2px solid #ffffff !important; /* 悬停时保持列间空隙 */
}

/* 悬停时最后一列保持原有边框 */
:deep(.el-table__row:hover > td:last-child) {
  border-right: 1px solid #dcdfe6 !important;
}

/* 表格数据单元格样式 */
:deep(.el-table td) {
  white-space: nowrap;
  color: #000000;
  font-weight: 400;
  font-size: 14px;
  font-family: 'Microsoft YaHei', sans-serif;
  text-align: left !important;
  border-right: 2px solid #ffffff !important; /* 添加列间空隙 */
}

/* 最后一列数据单元格不需要右边框 */
:deep(.el-table td:last-child) {
  border-right: 1px solid #dcdfe6 !important;
}

/* 条纹行样式 - 确保条纹行也有列间空隙 */
:deep(.el-table__row--striped td) {
  border-right: 2px solid #ffffff !important;
}

:deep(.el-table__row--striped td:last-child) {
  border-right: 1px solid #dcdfe6 !important;
}

/* 操作按钮样式 */
:deep(.el-table .el-button) {
  color: #1c6dd0;
  margin-right: 4px;
  font-weight: 500;
  font-size: 14px;
  font-family: 'Microsoft YaHei', sans-serif;
}

:deep(.el-table .el-button--primary) {
  font-weight: 600;
}

/* 抽屉展开时的表格样式 */
.drawer-open-table {
  width: 100% !important;
}

/* 当抽屉展开时，确保表格有水平滚动条 */
.main-content.drawer-open .table-container {
  overflow-x: auto;
}

/* 确保表头不换行 */
:deep(.el-table__header th .cell) {
  white-space: nowrap;
  overflow: hidden;
}

/* 确保表格数据单元格不换行 */
:deep(.el-table td .cell) {
  white-space: nowrap;
  overflow: hidden;
}

/* 固定操作列的样式 */
:deep(.el-table__fixed-right) {
  box-shadow: -1px 0 8px rgba(0, 0, 0, 0.1);
}

/* 操作列表头样式 */
:deep(.el-table__fixed-right .el-table__header th) {
  background-color: #0062c7 !important;
  color: #ffffff !important;
}

/* 分页样式 */
.pagination-container {
  background: #fff;
  padding: 10px !important;
  justify-content: flex-start !important;
  margin: 0;
  display: flex;
  align-items: center;
}

.page-size-selector {
  width: 50px;
  margin: 0 !important;
}

.font {
  font-size: 12px;
  display: flex;
  align-items: center;
  color: #606266;
  margin: 0 6px;
}

:deep(.el-pagination.is-background .btn-next),
:deep(.el-pagination.is-background .btn-prev),
:deep(.el-pagination.is-background .el-pager li) {
  background-color: #fff;
  border: 1px solid #dadada;
}

:deep(.el-pagination.is-background .btn-next.is-active),
:deep(.el-pagination.is-background .btn-prev.is-active),
:deep(.el-pagination.is-background .el-pager li.is-active) {
  background-color: #0077d3;
  border-color: #0077d3;
  color: #fff;
}

.el-input-number--small {
  width: 35px;
  margin: 0 !important;
}
:deep(.el-input-number.is-without-controls .el-input__wrapper) {
  padding: 0 !important;
}


</style>
