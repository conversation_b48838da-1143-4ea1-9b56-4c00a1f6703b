<template>
  <div class="app-container">
    <el-tabs v-model="interfaceMode" class="tabs">
      <el-tab-pane label="内部接口" name="0">
        <el-card>
          <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item v-show="false" label="时间范围" prop="searchCreatTime">
              <el-input
                  v-model="queryParams.searchCreatTime"
                  placeholder="时间范围"
                  clearable
                  style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="接口名称" prop="ifaceName">
              <el-input
                  v-model="queryParams.ifaceName"
                  placeholder="接口名称"
                  clearable
                  style="width: 200px"
                  @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="接口类型" prop="ifaceType">
              <el-select v-model="queryParams.ifaceType" placeholder="接口类型" clearable style="width: 200px">
                <el-option
                    v-for="dict in sysDict.storage_iface_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="应用状态" prop="ifaceStatus">
              <el-select v-model="queryParams.ifaceStatus" placeholder="应用状态" clearable style="width: 200px">
                <el-option
                    v-for="dict in sysDict.storage_iface_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间" style="width: 308px">
              <el-date-picker
                  v-model="dateRange"
                  value-format="YYYY-MM-DD"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="handleDateChange"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                  type="primary"
                  plain
                  icon="Plus"
                  @click="handleAdd"
                  v-hasPermi="['interfaces:interface:add']"
              >新建接口
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                  type="warning"
                  plain
                  icon="Download"
                  @click="handleExport"
                  v-hasPermi="['interfaces:interface:export']"
              >导出列表
              </el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
          </el-row>

          <el-table
              v-loading="loading"
              :data="interfaceList"
              max-height="200"
              @row-click="handleRowClick"
              :row-class-name="getRowClassName"
              highlight-current-row
              ref="interfaceTableRef"
          >
            <el-table-column type="index" align="center" label="序号"/>
            <el-table-column label="接口名称" align="center" prop="ifaceName"/>
            <el-table-column label="接口描述" align="center" prop="ifaceDesc"/>
            <el-table-column label="接口类型" align="center" prop="ifaceType">
              <template #default="scope">
                <dict-tag :options="sysDict.storage_iface_type" :value="scope.row.ifaceType"/>
              </template>
            </el-table-column>
            <el-table-column label="请求方式" align="center" prop="ifaceMethod">
              <template #default="scope">
                <dict-tag :options="sysDict.storage_iface_method" :value="scope.row.ifaceMethod"/>
              </template>
            </el-table-column>
            <el-table-column label="请求url" align="center" prop="ifaceUrl" width="150"/>
            <el-table-column label="调用次数" align="center" prop="callNum"/>
            <el-table-column label="应用状态" align="center" key="ifaceStatus" prop="ifaceStatus">
              <template #default="scope">
                <el-switch
                    v-model="scope.row.ifaceStatus"
                    active-value="启用"
                    inactive-value="停用"
                    @change="handleIfaceStatusChange(scope.row)"
                    @click.stop
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column label="接口创建时间" align="center" min-width="120" prop="createTime"/>
            <el-table-column label="接口更新时间" align="center" min-width="120" prop="updateTime"/>
            <el-table-column label="操作" width="140" align="center" class-name="small-padding fixed-width">
              <template #default="scope">
                <el-button link type="primary" icon="Edit" @click.stop="handleUpdate(scope.row)"
                           v-hasPermi="['interfaces:interface:edit']">编辑
                </el-button>
                <el-button link type="primary" icon="Delete" @click.stop="handleDelete(scope.row)"
                           v-hasPermi="['interfaces:interface:remove']">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
          />
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="外部接口" name="1">
        <el-card>
          <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item v-show="false" label="时间范围" prop="searchCreatTime">
              <el-input
                  v-model="queryParams.searchCreatTime"
                  placeholder="时间范围"
                  clearable
                  style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="接口名称" prop="ifaceName">
              <el-input
                  v-model="queryParams.ifaceName"
                  placeholder="接口名称"
                  clearable
                  style="width: 200px"
                  @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="接口类型" prop="ifaceType">
              <el-select v-model="queryParams.ifaceType" placeholder="接口类型" clearable style="width: 200px">
                <el-option
                    v-for="dict in sysDict.storage_iface_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="服务对象" prop="serviceObject">
              <el-select v-model="queryParams.serviceObject" placeholder="服务对象" clearable style="width: 200px">
                <el-option
                    v-for="dict in sysDict.storage_service_object"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="应用状态" prop="ifaceStatus">
              <el-select v-model="queryParams.ifaceStatus" placeholder="应用状态" clearable style="width: 200px">
                <el-option
                    v-for="dict in sysDict.storage_iface_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="频率" prop="frequency">
              <el-select v-model="queryParams.frequency" placeholder="频率" clearable style="width: 200px">
                <el-option
                    v-for="dict in sysDict.storage_iface_frequency"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间" style="width: 308px">
              <el-date-picker
                  v-model="dateRange"
                  value-format="YYYY-MM-DD"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="handleDateChange"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                  type="primary"
                  plain
                  icon="Plus"
                  @click="handleAdd"
                  v-hasPermi="['interfaces:interface:add']"
              >新建接口
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                  type="warning"
                  plain
                  icon="Download"
                  @click="handleExport"
                  v-hasPermi="['interfaces:interface:export']"
              >导出列表
              </el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
          </el-row>

          <el-table
              v-loading="loading"
              :data="interfaceList"
              max-height="200"
              @row-click="handleRowClick"
              :row-class-name="getRowClassName"
              highlight-current-row
              ref="interfaceTableRef"
          >
            <el-table-column type="index" align="center" label="序号"/>
            <el-table-column label="接口名称" align="center" prop="ifaceName"/>
            <el-table-column label="接口描述" align="center" prop="ifaceDesc"/>
            <el-table-column label="接口类型" align="center" prop="ifaceType">
              <template #default="scope">
                <dict-tag :options="sysDict.storage_iface_type" :value="scope.row.ifaceType"/>
              </template>
            </el-table-column>
            <el-table-column label="服务对象" align="center" prop="serviceObject">
              <template #default="scope">
                <dict-tag :options="sysDict.storage_service_object" :value="scope.row.serviceObject"/>
              </template>
            </el-table-column>
            <el-form-item label="数据量" prop="maxDataNum">
              <el-input v-model="form.maxDataNum" placeholder="数据量"/>
            </el-form-item>
            <el-table-column label="启停状态" align="center" key="ifaceStatus" prop="ifaceStatus">
              <template #default="scope">
                <el-switch
                    v-model="scope.row.ifaceStatus"
                    active-value="启用"
                    inactive-value="停用"
                    @change="handleIfaceStatusChange(scope.row)"
                    @click.stop
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column label="频率" align="center" prop="frequency">
              <template #default="scope">
                <dict-tag :options="sysDict.storage_iface_frequency" :value="scope.row.frequency"/>
              </template>
            </el-table-column>
            <el-table-column label="任务数" align="center" prop="callNum"/>
            <el-table-column label="接口创建时间" align="center" min-width="120" prop="createTime"/>
            <el-table-column label="下次任务开始时间" align="center" min-width="120" prop="startTime"/>
            <el-table-column label="操作" width="140" align="center" class-name="small-padding fixed-width">
              <template #default="scope">
                <el-button link type="primary" icon="Edit" @click.stop="handleUpdate(scope.row)"
                           v-hasPermi="['interfaces:interface:edit']">编辑
                </el-button>
                <el-button link type="primary" icon="Delete" @click.stop="handleDelete(scope.row)"
                           v-hasPermi="['interfaces:interface:remove']">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
          />
        </el-card>
      </el-tab-pane>
    </el-tabs>
    <el-card v-if="interfaceId" style="margin-top: 8px">
      <template #header>
        <div class="header-content">
          <el-text tag="b">接口调用记录--接口名称： {{ interfaceName }}</el-text>
          <el-button link type="primary" @click="handleAllInterfaceTask">全部列表></el-button>
        </div>
      </template>
      <el-table v-loading="loading" :data="interfaceTaskList" max-height="160">
        <!--            <el-table-column type="selection" width="55" align="center"/>-->
        <el-table-column type="index" align="center" label="序号"/>
        <template v-if="interfaceMode === '0'" v-for="(col) in internalInterfaceTaskColumns">
          <el-table-column v-if="col.visible" :label="col.label" :width="col.width"
                           align="center" :key="col.key"
                           :prop="col.key" :show-overflow-tooltip="true"
          />
        </template>
        <template v-else v-for="(col) in externalInterfaceTaskColumns">
          <el-table-column v-if="col.visible" :label="col.label" :width="col.width"
                           align="center" :key="col.key"
                           :prop="col.key" :show-overflow-tooltip="true"
          />
        </template>
      </el-table>
    </el-card>
    <!-- 右侧抽屉式表单 -->
    <el-drawer v-model="open" :show-close="false" destroy-on-close>
      <template #header="{ close, titleId, titleClass }">
        <h4 :id="titleId" :class="titleClass">{{ title }}</h4>
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="close">取 消</el-button>
      </template>
      <el-form v-if="interfaceMode === '0'" ref="interfaceRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="接口名称" prop="ifaceName">
          <el-input v-model="form.ifaceName" placeholder="接口名称"/>
        </el-form-item>
        <el-form-item label="接口描述" prop="ifaceDesc">
          <el-input v-model="form.ifaceDesc" placeholder="接口描述"/>
        </el-form-item>
        <el-form-item label="接口类型" prop="ifaceType">
          <el-select v-model="form.ifaceType" placeholder="接口类型" clearable style="width: 200px">
            <el-option
                v-for="dict in sysDict.storage_iface_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="请求方式" prop="ifaceMethod">
          <el-select v-model="form.ifaceMethod" placeholder="请求方式" clearable style="width: 200px">
            <el-option
                v-for="dict in sysDict.storage_iface_method"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="请求地址" prop="ifaceUrl">
          <el-input v-model="form.ifaceUrl" placeholder="请求地址"/>
        </el-form-item>
        <el-form-item label="" prop="">
          <el-button type="primary" @click="handleTestConnection">测试连接</el-button>
        </el-form-item>
        <el-form-item label="应用状态" prop="ifaceStatus">
          <el-select v-model="form.ifaceStatus" placeholder="应用状态" clearable style="width: 200px">
            <el-option
                v-for="dict in sysDict.storage_iface_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据量阈值" prop="maxDataNum">
          <el-select v-model="form.maxDataNum" placeholder="数据量阈值" clearable style="width: 200px">
            <el-option
                v-for="dict in sysDict.storage_iface_threshold"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <el-form v-if="interfaceMode === '1'" ref="interfaceTaskRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="接口名称" prop="ifaceName">
          <el-input v-model="form.ifaceName" placeholder="接口名称"/>
        </el-form-item>
        <el-form-item label="接口描述" prop="ifaceDesc">
          <el-input v-model="form.ifaceDesc" placeholder="接口描述"/>
        </el-form-item>
        <el-form-item label="接口类型" prop="ifaceType">
          <el-select v-model="form.ifaceType" placeholder="接口类型" clearable>
            <el-option
                v-for="dict in sysDict.storage_iface_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="服务对象" prop="serviceObject">
          <el-select v-model="form.serviceObject" placeholder="服务对象" clearable>
            <el-option
                v-for="dict in sysDict.storage_service_object"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据类型" prop="ifaceDataType">
          <el-select v-model="form.ifaceDataType" placeholder="数据类型" clearable>
            <el-option
                v-for="dict in sysDict.storage_iface_datatype"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据源" prop="sourceData">
          <el-select v-model="form.sourceData" placeholder="数据源" filterable multiple clearable>
            <el-option
                v-for="dict in bizDict.dataOriginOptions"
                :key="dict.value"
                :label="dict.text"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="发布字段" prop="publishFields">
          <el-tree-select
              v-model="form.publishFields"
              :data="bizDict.dataImiTreeOptions"
              placeholder="发布字段"
              clearable
              :render-after-expand="false"
              show-checkbox
              multiple
          />
        </el-form-item>
        <el-form-item label="数据范围" prop="dataRange">
          <el-select v-model="form.dataRange" placeholder="数据范围" clearable>
            <el-option
                v-for="dict in sysDict.storage_iface_datarange"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据量" prop="maxDataNum">
          <el-input v-model="form.maxDataNum" placeholder="数据量"/>
          条
        </el-form-item>
        <el-form-item label="启停状态" prop="ifaceStatus">
          <el-select v-model="form.ifaceStatus" placeholder="应用状态" clearable>
            <el-option
                v-for="dict in sysDict.storage_iface_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="频率" prop="frequency" required>
          <el-radio-group v-model="form.frequency">
            <el-radio style="width: 120px;" value="immediate">立即收割</el-radio>
            <el-radio style="width: 100px;" value="daily">每日收割</el-radio>
            <el-radio style="width: 100px;" value="weekly">每周收割</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <el-divider/>
    </el-drawer>
  </div>
</template>

<script setup name="Interface">
import {watch, nextTick} from "vue";
import {
  listInterface,
  addInterface,
  updateInterface,
  deleteInterface,
  STORAGE_INTERFACE_URL, updateIfaceStatusApi
} from "@/api/interfaces/interface.js";
import {listInterfaceTask} from "@/api/interfaces/interfaceTask.js";
import {useRouter} from "vue-router";
import {queryDataOriginSelect} from "@/api/searchbase/dataOrigin.js";
import {queryDataImiTreeSelect} from "@/api/searchbase/dataImi.js";

const {proxy} = getCurrentInstance();
const router = useRouter();
/**数据字典*/
const sysDict = reactive(
    {
      ...proxy.useDict(
          'storage_iface_type',
          'storage_iface_status',
          'storage_iface_method',
          'storage_iface_threshold',
          'storage_iface_datatype',
          'storage_iface_datarange',
          'storage_iface_frequency',
          'storage_service_object')
    })
/**业务字典*/
const bizDict = reactive({dataOriginOptions: [], dataImiTreeOptions: []})

const interfaceList = ref([]);
const interfaceTaskList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const interfaceId = ref("");
const interfaceName = ref("");
const currentRow = ref(null); // 当前选中的行

const data = reactive({
  form: {frequency: 'immediate'},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    ifaceName: undefined,
    ifaceType: undefined,
    ifaceStatus: undefined,
    serviceObject: undefined,
    frequency: undefined,
    searchCreatTime: ''
  },
  arrayField: ['sourceData', 'publishFields'],
  rules: {
    ifaceName: [
      {required: true, message: "接口名称不能为空", trigger: "blur"},
      {min: 2, max: 50, message: "接口名称长度在 2 到 50 个字符", trigger: "blur"}
    ],
    ifaceDesc: [
      {required: true, message: "接口描述不能为空", trigger: "blur"},
      {min: 5, max: 200, message: "接口描述长度在 5 到 200 个字符", trigger: "blur"}
    ],
    ifaceType: [
      {required: true, message: "接口类型不能为空", trigger: "change"}
    ],
    ifaceMethod: [
      {required: true, message: "请求方式不能为空", trigger: "change"}
    ],
    ifaceUrl: [
      {required: true, message: "请求URL不能为空", trigger: "blur"},
      {
        pattern: /^(https?:\/\/)?(([\da-z\.-]+)\.([a-z\.]{2,6})|(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}))(\:\d+)?([\/\w \.-]*)*\/?$/,
        message: "请输入正确的URL格式（支持域名或IP地址）",
        trigger: "blur"
      }
    ],
    ifaceStatus: [
      {required: true, message: "应用状态不能为空", trigger: "change"}
    ],
    maxDataNum: [
      {required: true, message: "数据量阈值不能为空", trigger: "change"}
    ],
    serviceObject: [
      {required: true, message: "服务对象不能为空", trigger: "change"}
    ],
    ifaceDataType: [
      {required: true, message: "数据类型不能为空", trigger: "change"}
    ],
    sourceData: [{
      required: true, message: "数据源不能为空", trigger: "change"
    }],
    publishFields: [{
      required: true, message: "发布字段不能为空", trigger: "change"
    }],
    dataRange: [{
      required: true, message: "数据范围不能为空", trigger: "change"
    }],
    frequency: 'immediate',
  }
});

const {queryParams, form, rules} = toRefs(data);

const interfaceMode = ref('0');

// 接口任务字段
const internalInterfaceTaskColumns = reactive([
  {key: 'id', label: `id`, visible: false},
  // 内部接口字段
  {key: 'ifaceName', label: `接口名称`, visible: true},
  {key: 'ifaceParams', label: `接口参数`, visible: true},
  {key: 'respCode', label: `响应状态码`, visible: true},
  {key: 'respCodeDesc', label: `状态码描述`, visible: true},
  {key: 'reqTime', label: `请求发送时间`, visible: true},
  {key: 'respTime', label: `响应返回时间`, visible: true},
  {key: 'timeConsuming', label: `历时`, visible: true},
]);
const externalInterfaceTaskColumns = reactive([
  {key: 'id', label: `id`, visible: false},
  // 外部接口字段
  {key: 'taskName', label: `任务名称`, visible: true},
  {key: 'taskStatus', label: `数据库`, visible: true},
  {key: 'publishCount', label: `发布数据量`, visible: true},
  {key: 'consumeCount', label: `消费数据量`, visible: true},
  {key: 'taskStatus', label: `任务状态`, visible: true},
  {key: 'ifaceName', label: `接口名称`, visible: true},
  {key: 'reqTime', label: `开始时间`, visible: true},
  {key: 'respTime', label: `结束时间`, visible: true},
  {key: 'remark', label: `备注`, visible: true},
]);

/** 查询接口列表 */
async function getList() {
  loading.value = true;
  queryParams.value.ifaceMode = interfaceMode.value;
  const {data} = await listInterface(queryParams.value);
  interfaceList.value = data.records;
  total.value = data.total;

  // 如果有数据，自动选中第一条
  if (data.records && data.records.length > 0) {
    const firstRecord = data.records[0];
    interfaceId.value = firstRecord.id;
    interfaceName.value = firstRecord.ifaceName;
    currentRow.value = firstRecord;
    console.log("自动选中第一条接口:", firstRecord.ifaceName, "ID:", firstRecord.id);
  } else {
    // 如果没有数据，清空选中状态
    interfaceId.value = "";
    interfaceName.value = "";
    currentRow.value = null;
  }
  loading.value = false;
}

/** 表单重置 */
function reset() {
  form.value = {
    ifaceName: undefined,
    ifaceDesc: undefined,
    ifaceType: undefined,
    ifaceMode: undefined,
    ifaceUrl: "",
    maxDataNum: undefined,
    ifaceStatus: undefined,
    serviceObject: undefined,
    ifaceDataType: undefined,
    sourceData: undefined,
    publishFields: undefined,
    dataRange: undefined,
    frequency: 'immediate',
  };
  // 清除表单验证状态
  nextTick(() => {
    if (proxy.$refs["interfaceRef"]) {
      proxy.$refs["interfaceRef"].clearValidate();
    }
    if (proxy.$refs["interfaceTaskRef"]) {
      proxy.$refs["interfaceTaskRef"].clearValidate();
    }
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "新增接口";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  //数组字段处理
  if (data.arrayField) data.arrayField.forEach(field => row[field] = row[field] ? row[field].split(',') : [])
  open.value = true;
  title.value = "编辑接口";
  form.value = {...row};
}

/** 提交按钮 */
async function submitForm() {
  try {
    // 使用await等待验证结果
    if (interfaceMode.value === '0') await proxy.$refs['interfaceRef'].validate();
    if (interfaceMode.value === '1') await proxy.$refs['interfaceTaskRef'].validate();
    console.log('验证通过，提交表单数据:', form.value);
    // 处理arrayField字段的多选逻辑
    if (data.arrayField) data.arrayField.forEach(field => form.value[field] = form.value[field] ? form.value[field].join(',') : '')

    console.log('数据组字段处理后的表单数据信息:', form.value);
    if (form.value.id) {
      await updateInterface(form.value);
      proxy.$modal.msgSuccess("修改成功");
    } else {
      form.value.ifaceMode = interfaceMode.value;
      await addInterface(form.value);
      proxy.$modal.msgSuccess("新增成功");
    }
    open.value = false;
    await getList();
  } catch (error) {
    console.error("操作失败:", error);
    //数组字段处理
    if (data.arrayField) data.arrayField.forEach(field => form.value[field] = form.value[field].split(','))
  }
}

/** 删除按钮操作 */
async function handleDelete(row) {
  const confirmRes = await proxy.$modal.confirm('是否确认删除接口名称为' + row.ifaceName + '"的数据项？').catch(() => {
  });
  if (!confirmRes) return;
  const localIds = row ? [row.id] : [interfaceId.value];
  await deleteInterface(localIds);
  proxy.$modal.msgSuccess("删除成功！")
  await getList();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(STORAGE_INTERFACE_URL + "/export", {
    ...queryParams.value
  }, `接口数据_${new Date().getTime()}.xlsx`);
}

async function handleIfaceStatusChange(row) {
  console.log("接口应用状态改变", row);
  let text = row.ifaceStatus === "启用" ? "启用" : "停用";
  const confirmRes = await proxy.$modal.confirm('确认要' + text + '吗?').catch(() => {
  });
  if (!confirmRes) {
    undoChange(row);
    return;
  }
  const updateRes = await updateIfaceStatusApi(row).catch(() => {
    undoChange(row)
  });
  if (!updateRes) return;
  proxy.$modal.msgSuccess(text + "成功");

  // 后续可能调整字典的数据值，这里暂时不处理
  function undoChange(row) {
    row.ifaceStatus = row.ifaceStatus === "启用" ? "停用" : "启用";
  }

}

function handleTestConnection() {
  // todo 测试连接函数实现
  proxy.$modal.msgSuccess("测试连接成功");
}

function handleRowClick(row) {
  // 设置当前选中行
  currentRow.value = row;
  interfaceId.value = row.id;
  interfaceName.value = row.ifaceName;
  console.log("选中的接口:", row.ifaceName, "ID:", row.id);
}

async function getInterfaceTaskList(ifaceId) {
  // 调用接口获取接口任务记录数据
  if (!ifaceId) {
    return;
  }
  loading.value = true;
  const query = {
    ifaceId: ifaceId,
    pageNum: 1,
    pageSize: 5
  };
  const {data} = await listInterfaceTask(query);
  interfaceTaskList.value = data.records;
  loading.value = false;
}

function handleAllInterfaceTask() {
  // 跳转到接口任务列表页面
  router.push({
    name: 'InterfaceTask',
    query: {
      interfaceMode: interfaceMode.value
    }
  });
}

function handleDateChange(range) {
  let searchTime = "";
  if (range && range.length === 2) {
    searchTime = range.join(',');
  }
  // 可以在这里将 dateRange 用于查询参数
  console.log("日期范围改变:", searchTime);
  queryParams.value.searchCreatTime = searchTime;
}

/**获取对应的数据源字典信息*/
async function getDataOriginSelect() {
  const {data} = await queryDataOriginSelect();
  console.log("获取数据源字典信息", data);
  bizDict.dataOriginOptions = data;
}

/**获取对应的数据源字典信息*/
async function getDataImiTreeSelect() {
  const {data} = await queryDataImiTreeSelect();
  console.log("获取数据源字典信息", data);
  bizDict.dataImiTreeOptions = data;
}

watch(() => interfaceMode.value, (newValue) => {
  if (newValue) {
    console.log(newValue);
    showSearch.value = true;
    proxy.resetForm("queryRef");
    getList()
  }
}, {immediate: true});

watch(() => interfaceId.value, (newValue) => {
  if (newValue) {
    console.log(newValue);
    // 查询关联接口任务记录数据
    getInterfaceTaskList(newValue);
  }
}, {immediate: true});


function getRowClassName({row, rowIndex}) {
  let className = '';
  // 斑马纹效果
  if (rowIndex % 2 === 0) {
    className += 'even-row ';
  } else {
    className += 'odd-row ';
  }
  // 选中行高亮
  if (currentRow.value && currentRow.value.id === row.id) {
    className += 'selected-row ';
  }
  return className.trim();
}

getList();
getDataOriginSelect();
getDataImiTreeSelect();
</script>

<style scoped>
/* 表格行样式 */
:deep(.el-table .even-row) {
  background-color: #fafafa;
}

:deep(.el-table .odd-row) {
  background-color: #ffffff;
}

/* 选中行高亮样式 */
:deep(.el-table .selected-row) {
  background-color: #e6f7ff !important;
  border-left: 3px solid #1890ff;
}

:deep(.el-table .selected-row:hover) {
  background-color: #bae7ff !important;
}

/* 行悬停效果 */
:deep(.el-table tbody tr:hover) {
  background-color: #f5f5f5 !important;
  cursor: pointer;
}

/* 当前行高亮 */
:deep(.el-table .current-row) {
  background-color: #ecf5ff !important;
}

/* 表格边框和圆角 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #ebeef5;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

/* 卡片样式 */
:deep(.el-card__header) {
  background-color: #fafafa;
  border-bottom: 1px solid #ebeef5;
  font-weight: 600;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 0;
}
</style>
