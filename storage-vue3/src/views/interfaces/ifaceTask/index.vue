<template>
  <div class="app-container">
    <el-tabs v-model="interfaceMode" @tab-change="handleTabClick" class="tabs">
      <el-tab-pane label="内部接口" name="0">
        <el-card>
          <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item v-show="false" label="时间范围" prop="searchReqTime">
              <el-input
                  v-model="queryParams.searchReqTime"
                  placeholder="时间范围"
                  clearable
                  style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="接口名称" prop="ifaceName">
              <el-input
                  v-model="queryParams.ifaceName"
                  placeholder="接口名称"
                  clearable
                  style="width: 200px"
                  @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="接口参数" prop="ifaceParams">
              <el-input
                  v-model="queryParams.ifaceParams"
                  placeholder="接口参数"
                  clearable
                  style="width: 200px"
                  @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="时间范围" style="width: 308px">
              <el-date-picker
                  v-model="dateRange"
                  value-format="YYYY-MM-DD"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="handleDateChange"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
          <el-row :gutter="10" class="mb8">
            <!--            <el-col :span="1.5">-->
            <!--              <el-button-->
            <!--                  type="warning"-->
            <!--                  plain-->
            <!--                  icon="Download"-->
            <!--                  @click="handleExport"-->
            <!--                  v-hasPermi="['interfaces:interfaceTask:export']"-->
            <!--              >导出列表-->
            <!--              </el-button>-->
            <!--            </el-col>-->
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
          </el-row>
          <el-table v-loading="loading" :data="interfaceTaskList" :max-height="tableHeight">
            <el-table-column type="index" align="center" label="序号"/>
            <template v-for="(col) in internalInterfaceTaskColumns">
              <el-table-column v-if="col.visible" :label="col.label" :width="col.width"
                               align="center" :key="col.key"
                               :prop="col.key" :show-overflow-tooltip="true"
              />
            </template>
          </el-table>

          <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
          />
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="外部接口" name="1">
        <el-card>
          <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item v-show="false" label="时间范围" prop="searchReqTime">
              <el-input
                  v-model="queryParams.searchReqTime"
                  placeholder="时间范围"
                  clearable
                  style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="任务名称" prop="taskName">
              <el-input
                  v-model="queryParams.taskName"
                  placeholder="任务名称"
                  clearable
                  style="width: 200px"
                  @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="接口名称" prop="ifaceName">
              <el-input
                  v-model="queryParams.ifaceName"
                  placeholder="接口名称"
                  clearable
                  style="width: 200px"
                  @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="任务状态" prop="ifaceStatus">
              <el-select v-model="queryParams.ifaceStatus" placeholder="任务状态" clearable style="width: 200px">
                <el-option
                    v-for="dict in sysDict.storage_iface_taskstatus"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间" style="width: 308px">
              <el-date-picker
                  v-model="dateRange"
                  value-format="YYYY-MM-DD"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="handleDateChange"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
          <el-row :gutter="10" class="mb8">
            <!--            <el-col :span="1.5">-->
            <!--              <el-button-->
            <!--                  type="warning"-->
            <!--                  plain-->
            <!--                  icon="Download"-->
            <!--                  @click="handleExport"-->
            <!--                  v-hasPermi="['interfaces:interfaceTask:export']"-->
            <!--              >导出列表-->
            <!--              </el-button>-->
            <!--            </el-col>-->
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
          </el-row>
          <el-table v-loading="loading" :data="interfaceTaskList" :max-height="tableHeight">
            <el-table-column type="index" align="center" label="序号"/>
            <template v-for="(col) in externalInterfaceTaskColumns">
              <el-table-column v-if="col.visible" :label="col.label" :width="col.width"
                               align="center" :key="col.key"
                               :prop="col.key" :show-overflow-tooltip="true"
              />
            </template>
          </el-table>

          <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
          />
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="Interface">
import {watch} from "vue";
import {listInterfaceTask, STORAGE_INTERFACE_TASK_URL} from "@/api/interfaces/interfaceTask.js";

const {proxy} = getCurrentInstance();
import {useRoute} from 'vue-router'

const route = useRoute();
const interfaceTaskList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const dateRange = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    ifaceName: undefined,
    ifaceType: undefined,
    ifaceStatus: undefined,
    serviceObject: undefined,
    frequency: undefined,
    taskName: undefined,
    searchReqTime: undefined,
  },
  rules: {}
});

const {queryParams} = toRefs(data);

/**数据字典*/
const sysDict = reactive({...proxy.useDict('storage_iface_taskstatus')})

const interfaceMode = ref('0');

// 接口任务字段
const internalInterfaceTaskColumns = reactive([
  {key: 'id', label: `id`, visible: false},
  // 内部接口字段
  {key: 'ifaceName', label: `接口名称`, visible: true},
  {key: 'ifaceParams', label: `接口参数`, visible: true},
  {key: 'respCode', label: `响应状态码`, visible: true},
  {key: 'respCodeDesc', label: `状态码描述`, visible: true},
  {key: 'reqTime', label: `请求发送时间`, visible: true},
  {key: 'respTime', label: `响应返回时间`, visible: true},
  {key: 'timeConsuming', label: `历时`, visible: true},
]);
const externalInterfaceTaskColumns = reactive([
  {key: 'id', label: `id`, visible: false},
  // 外部接口字段
  {key: 'taskName', label: `任务名称`, visible: true},
  {key: 'taskStatus', label: `数据库`, visible: true},
  {key: 'publishCount', label: `发布数据量`, visible: true},
  {key: 'consumeCount', label: `消费数据量`, visible: true},
  {key: 'taskStatus', label: `任务状态`, visible: true},
  {key: 'ifaceName', label: `接口名称`, visible: true},
  {key: 'reqTime', label: `开始时间`, visible: true},
  {key: 'respTime', label: `结束时间`, visible: true},
  {key: 'remark', label: `备注`, visible: true},
]);

/** 查询接口列表 */
async function getList() {
  loading.value = true;
  queryParams.value.ifaceMode = interfaceMode.value;
  const {data} = await listInterfaceTask(queryParams.value);
  interfaceTaskList.value = data.records;
  total.value = data.total;
  loading.value = false;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(STORAGE_INTERFACE_TASK_URL + "/export", {
    ...queryParams.value
  }, `接口任务数据_${new Date().getTime()}.xlsx`);
}

function handleDateChange(range) {
  let searchTime = "";
  if (range && range.length === 2) {
    searchTime = range.join(',');
  }
  // 可以在这里将 dateRange 用于查询参数
  console.log("日期范围改变:", searchTime);
  queryParams.value.searchReqTime = searchTime;
}

onMounted(() => {
  console.log(route.query.interfaceMode);
  interfaceMode.value = route.query.interfaceMode || '0';
  getList()
  console.log('=======================');
})

function handleTabClick(tabName) {
  console.log(tabName);
  interfaceMode.value = tabName;
  getList()
}

const tableHeight = ref(null);
onMounted(() => {
  watch(showSearch, async (value) => {
    if (value) {
      await nextTick(() => {
        tableHeight.value = window.innerHeight - proxy.$refs.queryRef.$el.clientHeight - 385
      })
    } else {
      tableHeight.value = window.innerHeight - 254
    }
  }, {immediate: true})
})

</script>

