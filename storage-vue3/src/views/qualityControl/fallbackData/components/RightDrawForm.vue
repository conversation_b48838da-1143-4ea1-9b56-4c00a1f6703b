<template>
  <div class="form-container">
    <!-- 编辑侧边栏 -->
    <el-form
      v-if="props.type==='edit'"
      ref="datasourceform"
      :model="formData"
      :rules="rules"
      label-width="140px"
      size="small"
      label-position="left"
    ><!-- 一、单篇文献元素集 -->
    <el-divider content-position="left">内容详情</el-divider>
    <el-form-item label="快速搜索">
      <el-input
        v-model="searchForm.keyword"
        placeholder="请输入关键词"
        clearable
        style="width: 260px;"
      >
        <template #append>
          <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
        </template>
      </el-input>
    </el-form-item>

    <!-- <el-form-item>
      <el-button :icon="Document"  @click="handleDetail">内容详情</el-button>
      <el-button :icon="Search"     @click="handleSearch">内容检索</el-button>
      <el-button :icon="Folder"     @click="handleZip">ZIP</el-button>
      <el-button :icon="QuestionFilled" @click="handleQuery">Q 查询</el-button>
      <el-button :icon="Refresh"    @click="handleReset">重置</el-button>
    </el-form-item> -->
    <el-divider content-position="left">单篇文献元素集</el-divider>
    <el-form-item label="article-id" prop="articleId">
      <el-input v-model="formData.articleId" disabled />
    </el-form-item>

    <el-form-item label="article-type" prop="articleType">
      <el-select v-model="formData.articleType" placeholder="请选择文章类型" clearable>
        <el-option label="Paper"  value="paper" />
        <el-option label="Review" value="review" />
        <el-option label="Letter" value="letter" />
      </el-select>
    </el-form-item>

    <el-form-item label="fpage" prop="fpage">
      <el-input v-model="formData.fpage" placeholder="请输入起始页码" />
    </el-form-item>

    <el-form-item label="lpage" prop="lpage">
      <el-input v-model="formData.lpage" placeholder="请输入结束页码" />
    </el-form-item>

    <el-form-item label="DOI" prop="doi">
      <el-input v-model="formData.doi" placeholder="10.1159/000046431" />
    </el-form-item>

    <el-form-item label="article-title" prop="articleTitle">
      <el-input
        v-model="formData.articleTitle"
        type="textarea"
        :rows="2"
        placeholder="请输入文章标题"
      />
    </el-form-item>

    <el-form-item label="subject" prop="subject">
      <el-input v-model="formData.subject" placeholder="请输入主题关键词" />
    </el-form-item>

    <el-form-item label="pub-date" prop="pubDate">
      <el-date-picker
        v-model="formData.pubDate"
        type="date"
        placeholder="选择出版日期"
        style="width: 100%"
      />
    </el-form-item>

    <el-form-item label="history" prop="history">
      <el-date-picker
        v-model="formData.history"
        type="date"
        placeholder="选择记录日期"
        style="width: 100%"
      />
    </el-form-item>

    <!-- 二、来源元素集 -->
    <el-divider content-position="left">来源元素集</el-divider>

    <el-form-item label="Source Id" prop="sourceId">
      <el-input v-model="formData.sourceId" placeholder="来源唯一标识" />
    </el-form-item>

    <el-form-item label="ISSN" prop="issn">
      <el-input v-model="formData.issn" placeholder="例如 1422-6405" />
    </el-form-item>

    <el-form-item label="pub-date (source)" prop="sourcePubDate">
      <el-date-picker
        v-model="formData.sourcePubDate"
        type="year"
        placeholder="选择来源出版年"
        style="width: 100%"
      />
    </el-form-item>

    <el-form-item label="Source title" prop="sourceTitle">
      <el-input v-model="formData.sourceTitle" placeholder="例如 Cells Tissues Organs" />
    </el-form-item>

    <el-form-item label="Publisher Name" prop="publisherName">
      <el-input v-model="formData.publisherName" placeholder="例如 S. Karger AG" />
    </el-form-item>

    <el-form-item label="frequence" prop="frequence">
      <el-select v-model="formData.frequence" placeholder="请选择出版频率" clearable>
        <el-option label="Monthly"   value="monthly" />
        <el-option label="Weekly"    value="weekly" />
        <el-option label="Quarterly" value="quarterly" />
        <el-option label="Annual"    value="annual" />
      </el-select>
    </el-form-item>

    <el-form-item label="Regions" prop="regions">
      <el-input v-model="formData.regions" placeholder="例如 SWITZERLAND" />
    </el-form-item>

    <el-form-item label="notes" prop="notes">
      <el-input
        v-model="formData.notes"
        type="textarea"
        :rows="2"
        placeholder="备注信息"
      />
    </el-form-item>

    <el-form-item label="abstract" prop="abstract">
      <el-input
        v-model="formData.abstract"
        type="textarea"
        :rows="3"
        placeholder="请输入摘要"
      />
    </el-form-item>

    <el-form-item label="edition" prop="edition">
      <el-input v-model="formData.edition" placeholder="版本信息" />
    </el-form-item>

    <el-form-item label="语言" prop="language">
      <el-select v-model="formData.language" placeholder="请选择语言" clearable>
        <el-option label="eng" value="eng" />
        <el-option label="chi" value="chi" />
        <el-option label="fra" value="fra" />
      </el-select>
    </el-form-item>
    </el-form>
    <!-- 级别管理侧边栏 -->
    <el-form
      v-if="props.type==='levelManage'"
      ref="datasourceform"
      :model="formData"
      :rules="rules"
      label-width="140px"
      size="small"
      label-position="left"
    >
      <!-- 一级引文年份 -->
      <el-form-item label="一级引文" prop="level1Year">
        <el-input-number
          v-model="formData.level1Year"
          :min="1900"
          :max="2100"
          :precision="0"
          controls-position="right"
          style="width: 100%"
          placeholder="请输入一级引文年份"
        />
      </el-form-item>

      <!-- 二级引文年份 -->
      <el-form-item label="二级引文" prop="level2Year">
        <el-input-number
          v-model="formData.level2Year"
          :min="1900"
          :max="2100"
          :precision="0"
          controls-position="right"
          style="width: 100%"
          placeholder="请输入二级引文年份"
        />
      </el-form-item>

      <!-- 三级引文年份 -->
      <el-form-item label="三级引文" prop="level3Year">
        <el-input-number
          v-model="formData.level3Year"
          :min="1900"
          :max="2100"
          :precision="0"
          controls-position="right"
          style="width: 100%"
          placeholder="请输入三级引文年份"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import {reactive, ref, computed, getCurrentInstance, nextTick} from "vue";
import {testConnectionApi} from "@/api/dataSource/dataSource";

const {proxy} = getCurrentInstance();

const props = defineProps({
  modal: {
    type: Object,
  },
  type:    { type: String, required: true }
});
/* 搜索表单数据 */
const searchForm = reactive({
  keyword: ''
})
/**字典*/
// const {sysDict, bizDict} = props.modal.dict
// const form = computed({
//   get() {
//     return props.modal.form;
//   },
//   set() {
//   }
// });

// 需要进行数组转换的字段
const arrayField = ref(['processFlow', 'analysisRuleId']);

const ruleFormRef = ref();

const testConnectionFlag = ref(null)

// 编辑表单数据
const formData = reactive({
  /* 单篇文献 */
  articleId:       '',
  articleType:     '',
  fpage:           '',
  lpage:           '',
  doi:             '',
  articleTitle:    '',
  subject:         '',
  pubDate:         '',
  history:         '',

  /* 来源信息 */
  sourceId:        '',
  issn:            '',
  sourcePubDate:   '',
  sourceTitle:     '',
  publisherName:   '',
  frequence:       '',
  regions:         '',
  notes:           '',
  abstract:        '',
  edition:         '',
  language:        ''
})
//
const rules = {
  /* ---------- 单篇文献元素集 ---------- */
  articleId: [
    { required: true, message: '请输入 article-id', trigger: 'blur' }
  ],
  articleType: [
    { required: true, message: '请选择文章类型', trigger: 'change' }
  ],
  fpage: [
    { required: true, message: '请输入起始页码', trigger: 'blur' },
    { pattern: /^\d+$/, message: '页码必须是数字', trigger: 'blur' }
  ],
  lpage: [
    { required: true, message: '请输入结束页码', trigger: 'blur' },
    { pattern: /^\d+$/, message: '页码必须是数字', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        const start = Number(formData.fpage)
        const end   = Number(value)
        if (start && end && end < start) {
          callback(new Error('结束页码不能小于起始页码'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  doi: [
    { required: true, message: '请输入 DOI', trigger: 'blur' },
    {
      pattern: /^10\.\d{4,}\/.+$/,
      message: 'DOI 格式应为 10.xxxx/...',
      trigger: 'blur'
    }
  ],
  articleTitle: [
    { required: true, message: '请输入文章标题', trigger: 'blur' },
    { min: 2, max: 500, message: '长度 2-500 字符', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请输入主题关键词', trigger: 'blur' }
  ],
  pubDate: [
    { required: true, message: '请选择出版日期', trigger: 'change' }
  ],
  history: [
    { required: true, message: '请选择记录日期', trigger: 'change' }
  ],

  /* ---------- 来源元素集 ---------- */
  sourceId: [
    { required: true, message: '请输入来源唯一标识', trigger: 'blur' }
  ],
  issn: [
    { required: true, message: '请输入 ISSN', trigger: 'blur' },
    {
      pattern: /^\d{4}-\d{3}[\dX]$/,
      message: 'ISSN 格式应为 1234-567X',
      trigger: 'blur'
    }
  ],
  sourcePubDate: [
    { required: true, message: '请选择来源出版年', trigger: 'change' }
  ],
  sourceTitle: [
    { required: true, message: '请输入来源标题', trigger: 'blur' },
    { min: 2, max: 200, message: '长度 2-200 字符', trigger: 'blur' }
  ],
  publisherName: [
    { required: true, message: '请输入出版机构', trigger: 'blur' }
  ],
  frequence: [
    { required: true, message: '请选择出版频率', trigger: 'change' }
  ],
  regions: [
    { required: true, message: '请输入所属国家/地区', trigger: 'blur' }
  ],
  edition: [
    { max: 50, message: '版本信息最长 50 字符', trigger: 'blur' }
  ],
  language: [
    { required: true, message: '请选择语言', trigger: 'change' }
  ],
}

// 处理数组字段转换为字符串（保存时使用）
const processArrayToString = (data) => {
  const processedData = {...data};
  if (arrayField.value) {
    arrayField.value.forEach(field => {
      if (processedData[field] && Array.isArray(processedData[field])) {
        console.log(`转换数组字段 ${field}:`, processedData[field], '-> ', processedData[field].join(','));
        // 过滤空值并转换为字符串
        processedData[field] = processedData[field]
            .filter(item => item && item.trim() !== '')
            .join(',');
      }
    });
  }
  return processedData;
};

// 处理字符串转换为数组（编辑时使用）
const processStringToArray = (data, readonly = false) => {
  if (arrayField.value && !readonly) {
    arrayField.value.forEach(field => {
      if (data[field] && typeof data[field] === 'string') {
        console.log(`转换字符串字段 ${field}:`, data[field], '-> ', data[field].split(','));
        // 处理空字符串的情况
        if (data[field].trim() === '') {
          data[field] = [];
        } else {
          data[field] = data[field].split(',').filter(item => item.trim() !== '');
        }
      }
    });
  }
};

// 获取表单数据
const getFormData = () => {
  // 返回处理后的数据（数组转为字符串）
  return processArrayToString(formData);
};

// 重置表单
const resetForm = () => {
  console.log('=== 重置表单开始 ===');
  console.log('重置前 ftpUser:', formData.ftpUser, 'ftpPwd:', formData.ftpPwd);

  // 先清除表单验证状态
  ruleFormRef.value?.clearValidate();

  // 重置表单数据为初始状态
  Object.assign(formData, {
    name: '',
    dataId: '',
    pubType: '',
    dataLevel: '',
    dataType: '',
    processFlow: [],
    harvestType: '',
    ftpUrl: '',
    ftpPort: '',
    ftpUser: '',
    ftpPwd: '',
    fileType: '',
    ifaceUrl: '',
    analysisRuleId: '',
    sourceType: '',
    describes: ''
  });

  console.log('重置后 ftpUser:', formData.ftpUser, 'ftpPwd:', formData.ftpPwd);

  // 强制清空表单字段值，确保输入框显示为空
  setTimeout(() => {
    ruleFormRef.value?.resetFields();

    // 额外确保用户名和密码字段为空
    formData.ftpUser = '';
    formData.ftpPwd = '';

    console.log('最终检查 ftpUser:', formData.ftpUser, 'ftpPwd:', formData.ftpPwd);
    console.log('=== 重置表单完成 ===');
  }, 100);
};

// 设置表单数据
const setFormData = (data) => {
  console.log('=== 设置表单数据 ===');
  console.log('传入数据:', data);

  // 深拷贝数据，避免修改原始对象
  const processedData = JSON.parse(JSON.stringify(data || {}));

  // 处理字符串转数组
  processStringToArray(processedData);

  console.log('处理后数据:', processedData);
  console.log('设置前 ftpUser:', formData.ftpUser, 'ftpPwd:', formData.ftpPwd);

  // 设置到表单数据
  Object.assign(formData, processedData);

  console.log('设置后 ftpUser:', formData.ftpUser, 'ftpPwd:', formData.ftpPwd);
  console.log('=== 设置表单数据完成 ===');
};

// 表单验证
const validateForm = () => {
  return new Promise((resolve, reject) => {
    ruleFormRef.value.validate((valid) => {
      if (valid) {
        resolve(true);
      } else {
        reject(false);
      }
    });
  });
};

// 专门用于新增时的完全重置
const resetForAdd = () => {
  console.log('=== 新增专用重置开始 ===');

  // 先清除表单验证状态
  ruleFormRef.value?.clearValidate();

  // 完全重置表单数据
  Object.keys(formData).forEach(key => {
    if (key === 'processFlow') {
      formData[key] = [];
    } else {
      formData[key] = '';
    }
  });

  // 强制更新响应式数据
  nextTick(() => {
    ruleFormRef.value?.resetFields();

    // 再次确保关键字段为空
    formData.ftpUser = '';
    formData.ftpPwd = '';
    formData.ftpUrl = '';
    formData.ftpPort = '';

    console.log('新增重置完成，所有字段:', JSON.stringify(formData, null, 2));
    console.log('=== 新增专用重置完成 ===');
  });
};

// 暴露方法给父组件
defineExpose({
  getFormData,
  resetForm,
  resetForAdd,  // 新增专用重置方法
  setFormData,
  validateForm
});
</script>

<style lang="scss" scoped>
.form-container {
  border-radius: 2px;
  border: 1px solid rgb(221, 229, 235);
  background-color: rgb(255, 255, 255);
  overflow: hidden;

  :deep(.el-form-item--small) {
    margin-bottom: 0;
  }

  :deep(.el-input) {
    width: 335px;
    height: 34px;
  }
  :deep(.el-select) {
    width: 335px;
    height: 34px;
  }
  :deep(.el-input__wrapper) {
    height: 32px;
  }

  :deep(.el-select--small .el-select__wrapper) {
    height: 32px;
  }

  :deep(.el-form-item__content) {
    padding-top: 6px !important;
    padding-left: 9px !important;
    padding-right: 6px !important;
    background-color: transparent;
  }

  // 确保标签区域背景色连续
  :deep(.el-form-item) {
    display: flex;

    .el-form-item__label {
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 0, 0);
      font-weight: bold;
      padding-left: 15px !important;
      padding-top: 6px !important;
      padding-right: 0 !important;
      height: 40px;
      line-height: 40px;
      background-color: #f7f7f7;
      width: 110px;
      flex-shrink: 0;

      em {
        font-style: normal;
        color: #eb3037;
      }
    }
  }

  // 确保middle-box不受表单样式影响
  .middle-box {
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
  }

  :deep(.el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label-wrap>.el-form-item__label:before),
  :deep(.el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label:before) {
    display: none;
  }

  // textarea项的特殊样式
  :deep(.textarea-item) {
    .el-form-item__label {
      height: 196px !important;
      align-items: flex-start;
      padding-top: 6px !important;
    }
  }
}

.middle-box {
  display: flex;
  flex-direction: row;

  .middle-box-left {
    width: 110px;
    background-color: #f7f7f7 !important;
    flex-shrink: 0;
  }

  .middle-box-none {
    height: 20px;
    flex: 1;
  }

  .middle-box-right {
    height: 80px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      border: 1px solid rgb(219, 219, 219);
      border-radius: 2px;
      background-color: rgb(255, 255, 255);
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 120, 212);
      padding: 3px 11px;
      cursor: pointer;
    }
  }
}

// 映射规则标签样式
.rule-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
  min-height: 32px;
  padding: 2px 0;

  .rule-tag {
    margin: 0;
    font-size: 12px;
    border-radius: 3px;
    background-color: #e8f4fd;
    border: 1px solid #b3d9f7;
    color: #1890ff;
    padding: 2px 8px;
    height: auto;
    line-height: 1.4;

    :deep(.el-tag__content) {
      line-height: 1.4;
    }

    &:hover {
      background-color: #d6f0ff;
    }
  }

  .no-data-text {
    color: #999;
    font-size: 13px;
    font-style: italic;
    padding: 6px 0;
  }
}
</style>
