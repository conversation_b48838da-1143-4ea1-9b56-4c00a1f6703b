<template>
  <el-dialog :title="modal.title" v-model="modal.open" width="500px" append-to-body destroy-on-close>
    <el-form v-if="modal.type === '0'" ref="parsingMappingTaskRef" :model="form" label-width="120px">
      <el-form-item label="批次号" prop="batchId">
        <el-input v-model="form.batchId" disabled placeholder="批次号"/>
      </el-form-item>
      <el-form-item label="映射数量" prop="analysisTotalNum">
        <el-input v-model="form.analysisTotalNum" disabled placeholder=""/>
      </el-form-item>
      <el-form-item label="映射规则" prop="analysisRuleId">
        <el-input v-model="form.analysisRuleId" disabled placeholder=""/>
      </el-form-item>
      <el-form-item label="规则状态" prop="analysisRuleId">
        <el-input v-model="form.analysisRuleId" disabled placeholder=""/>
      </el-form-item>
    </el-form>
    <!--    // 失败原因-->
    <span v-if="modal.type === '1'">
      <el-text>
        批次<el-tag type="success">{{ form.batchId }}</el-tag> {{ failType }}，失败原因：{{ form.failMsg }}
      </el-text>
    </span>
    <template #footer>
      <div class="dialog-footer">
        <el-divider/>
        <el-button v-if="modal.type === '0'" type="primary" @click="handleRule">发送规则</el-button>
        <el-button v-if="modal.type === '1'" type="primary" @click="handleFailMsg">
          {{ form.taskStatus === '数据解析失败' ? '查看详情' : '测试连接' }}
        </el-button>
        <el-button type="info" @click="cancel">{{ modal.type === '0' ? '关闭提醒' : '关闭' }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {getCurrentInstance} from "vue";

const {proxy} = getCurrentInstance();

const props = defineProps({
  modal: {
    type: Object,
  }
});
const emit = defineEmits(["ok"]);

const form = computed({
  get() {
    return props.modal.form;
  },
  set() {
  }
});

// 计算属性，根据 modal.type 动态返回按钮文本
const failType = computed(() => {
  switch (form.value.taskStatus) {
    case '数据接收失败':
      return '接收失败';
    case '数据解析失败':
      return '解析失败';
    default:
      return '';
  }
});

function handleFailMsg() {
  // todo 发送失败原因
  props.modal.open = false
  // 根据失败原因不同跳转不同的处理页面
  if (form.value.taskStatus === '数据接收失败') {
    alert('跳转接口管理页面')
    // 跳转数据格式错误页面
    // proxy.$router.push({path: '/数据接收失败'});
  }
  if (form.value.taskStatus === '数据解析失败') {
    alert('跳转任务采集列表页面')
    // 跳转数据格式错误页面
    // proxy.$router.push({path: '/数据解析失败'});
  }
}

function handleRule() {
  props.modal.open = false
}

function cancel() {
  props.modal.open = false
  emit("ok");
}

</script>

<style scoped>

</style>
