<template>
  <div class="app-container">
    <!-- 背景框框 -->
    <viewBody title="解析映射任务" :isShowTopCaption="true">
      <template #content>
        <!-- 使用FormSearch组件替换搜索菜单 -->
        <FormSearch
          ref="formSearchRef"
          v-model="queryParams"
          :formItems="formItems"
          @search="handleQuery"
          @reset="resetQuery"
        >
          <template #btn>
            <el-button class="my_primary_btn" type="primary" icon="Connection" @click="handleDeduplicate"
              >归一合并</el-button
            >
          </template>
        </FormSearch>

        <!-- 表格部分 -->
        <TableForm
          ref="tableFormRef"
          :columns="columns"
          :tableData="dataSourceList"
          :total="total"
          v-loading="loading"
          :showIndex="true"
          :isShowSelection="true"
          :showOtherColumn="true"
          tableotherColumnLabel="操作"
          :isShowCount="true"
          :isShowSearchQuery="hasSearchConditions"
          :columnsRightWidth="'180'"
          @selectionChange="handleSelectionChange"
        >

          <!-- 操作按钮插槽 -->
          <template #otherOperation="{ row }">
            <span
              class="operation-btn"
              style="color: #0076d0 !important"
              @click="handleDetail(row)"
              v-if="row.taskStatus === '结果已返回'"
            >
              详情
            </span>
            <span
              class="operation-btn"
              style="color: #e6a23c !important"
              @click="handleReprocess(row)"
              v-if="row.reprocessFlag === 1"
            >
              重新处理
            </span>
            <span
              class="operation-btn"
              style="color: #f56c6c !important"
              @click="handleFailMsg(row)"
              v-if="row.taskStatus === '数据接收失败' || row.taskStatus === '数据解析失败'"
            >
              失败原因
            </span>
          </template>

          <!-- 分页插槽 -->
          <template #pagination>
            <MyPagination
              v-if="total > 0"
              :total="total"
              :page="queryParams.pageNum"
              :limit="queryParams.pageSize"
              @pagination="getList"
            />
          </template>
        </TableForm>
      </template>
    </viewBody>

    <modal-form :modal="modal" @ok="getList" />
  </div>
</template>

<script setup name="ParsingMapping">
import { useRouter } from "vue-router";
import { ref, reactive, toRefs, computed, getCurrentInstance } from "vue";
import { queryDataOriginSelect } from "@/api/searchbase/dataOrigin.js";
import { queryRuleBaseSelect, STORAGE_RULE_BASE_URL } from "@/api/processingtool/ruleBase.js";
import modalForm from "./modal";
import { listBatchAnalysisMapping } from "@/api/obtainConvert/parsingMappingTask.js";
import { parseDeduplicateBatch } from "@/api/task/task.js";

// 引入组件
import viewBody from "@/components/view/viewBody.vue";
import TableForm from "@/views/source/components/TableForm.vue";
import MyPagination from "@/components/Pagination/new.vue";
import FormSearch from "@/views/source/components/FormSearch.vue";

const { proxy } = getCurrentInstance();
const router = useRouter();
/**数据字典*/
const sysDict = reactive({
  ...proxy.useDict("storage_data_type", "storage_task_status", "storage_audit_status"),
});
/**业务字典*/
const bizDict = reactive({ dataOriginOptions: [], ruleBaseSelectOptions: [] });

const dataSourceList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const title = ref("");
const multiple = ref(true);
const ids = ref([]);
const selectRows = ref([]);
const dateRange = ref([]);

// 表单搜索引用
const formSearchRef = ref(null);
const tableFormRef = ref(null);

// 搜索表单配置
const formItems = ref([
  {
    label: "批次号",
    prop: "batchId",
    component: "el-input",
    props: {
      placeholder: "请输入批次号",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    label: "数据源",
    prop: "sourceId",
    component: "el-select",
    props: {
      placeholder: "请选择数据源",
      clearable: true,
      style: { width: "200px" },
    },
    children: () =>
      bizDict.dataOriginOptions.map((item) => ({
        value: item.value,
        label: item.text,
      })),
  },
  {
    label: "数据类型",
    prop: "dataType",
    component: "el-select",
    props: {
      placeholder: "请选择数据类型",
      clearable: true,
      style: { width: "200px" },
    },
    children: () =>
      sysDict.storage_data_type.map((item) => ({
        value: item.value,
        label: item.label,
      })),
  },
  {
    label: "任务状态",
    prop: "taskStatus",
    component: "el-select",
    props: {
      placeholder: "请选择任务状态",
      clearable: true,
      style: { width: "200px" },
    },
    children: () =>
      sysDict.storage_task_status.map((item) => ({
        value: item.value,
        label: item.label,
      })),
  },
  {
    label: "审核状态",
    prop: "auditStatus",
    component: "el-select",
    props: {
      placeholder: "请选择审核状态",
      clearable: true,
      style: { width: "200px" },
    },
    children: () =>
      sysDict.storage_audit_status.map((item) => ({
        value: item.value,
        label: item.label,
      })),
  },
  {
    label: "任务开始时间",
    prop: "dateRange",
    component: "el-date-picker",
    props: {
      type: "daterange",
      valueFormat: "YYYY-MM-DD",
      rangeSeparator: "-",
      startPlaceholder: "开始日期",
      endPlaceholder: "结束日期",
      style: { width: "308px" },
    },
    events: {
      change: handleDateChange,
    },
  },
]);

// 表格列配置
const columns = ref([
  { prop: "batchId", label: "批次号", width: "120", showOverflowTooltip: true },
  { prop: "source", label: "数据源" },
  {
    prop: "dataType",
    label: "数据类型",
    formatter: (row) => {
      const dict = sysDict.storage_data_type.find((item) => item.value === row.dataType);
      return dict ? dict.label : row.dataType;
    },
  },
  {
    prop: "analysisRuleId",
    label: "映射规则",
    width: "150",
    formatter: (row) => {
      return getRuleNameText(row.analysisRuleId);
    },
  },
  { prop: "fileName", label: "文件名称", showOverflowTooltip: true },
  {
    prop: "taskStatus",
    label: "任务状态",
    width: "120",
    formatter: (row) => {
      const dict = sysDict.storage_task_status.find((item) => item.value === row.taskStatus);
      return dict ? dict.label : row.taskStatus;
    },
  },
  { prop: "originalNum", label: "原篇级数量", width: "120" },
  { prop: "analysisTotalNum", label: "映射篇级数量", width: "120" },
  { prop: "analysisSuccessNum", label: "映射成功数量", width: "120" },
  { prop: "analysisFailNum", label: "映射失败数量", width: "120" },
  {
    prop: "auditStatus",
    label: "审核状态",
    formatter: (row) => {
      const dict = sysDict.storage_audit_status.find((item) => item.value === row.auditStatus);
      return dict ? dict.label : row.auditStatus;
    },
  },
  { prop: "taskStartTime", label: "任务开始时间", width: "140" },
  { prop: "taskEndTime", label: "任务结束时间", width: "140" },
]);

// 计算是否有搜索条件
const hasSearchConditions = computed(() => {
  return (
    queryParams.value.batchId ||
    queryParams.value.sourceId ||
    queryParams.value.dataType ||
    queryParams.value.taskStatus ||
    queryParams.value.auditStatus ||
    (dateRange.value && dateRange.value.length === 2)
  );
});

// 获取数据源标签
function getDataSourceLabel(value) {
  const option = bizDict.dataOriginOptions.find((item) => item.value === value);
  return option ? option.text : value;
}

// 获取数据类型标签
function getDataTypeLabel(value) {
  const option = sysDict.storage_data_type.find((item) => item.value === value);
  return option ? option.label : value;
}

// 获取任务状态标签
function getTaskStatusLabel(value) {
  const option = sysDict.storage_task_status.find((item) => item.value === value);
  return option ? option.label : value;
}

// 获取审核状态标签
function getAuditStatusLabel(value) {
  const option = sysDict.storage_audit_status.find((item) => item.value === value);
  return option ? option.label : value;
}

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    batchId: "",
    dataType: "",
    sourceId: "",
    taskStatus: "",
    auditStatus: "",
    searchTaskStartTime: "",
    orderAsc: false,
    orderByCol: "taskStartTime",
  },
  arrayField: [],
  rules: {},
});

const { queryParams, form, rules } = toRefs(data);

// 弹窗数据
const modal = reactive({
  isEdit: false,
  title: "",
  // 弹窗类型 0-重新处理 1-失败原因
  type: "0",
  open: false,
  form: form,
  dict: {
    sysDict,
    bizDict,
  },
});

/** 查询接口列表 */
async function getList() {
  loading.value = true;
  const { data } = await listBatchAnalysisMapping(queryParams.value);
  dataSourceList.value = data.records;
  total.value = data.total;
  loading.value = false;
}

/** 搜索按钮操作 */
function handleQuery(formData) {
  if (formData) {
    // 如果是FormSearch组件传来的表单数据
    Object.assign(queryParams.value, formData, { pageNum: 1 });
  } else {
    queryParams.value.pageNum = 1;
  }
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    batchId: "",
    dataType: "",
    sourceId: "",
    taskStatus: "",
    auditStatus: "",
    searchTaskStartTime: "",
    orderAsc: false,
    orderByCol: "taskStartTime",
  };
  getList();
}

// 清除单个搜索条件
function clearSearchItem(prop) {
  if (prop === "dateRange") {
    dateRange.value = [];
    queryParams.value.searchTaskStartTime = "";
  } else {
    queryParams.value[prop] = "";
  }
  handleQuery();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    STORAGE_RULE_BASE_URL + "/export",
    {
      ...queryParams.value,
    },
    `解析映射列表数据_${new Date().getTime()}.xlsx`,
  );
}

function handleDateChange(range) {
  let searchTime = "";
  if (range && range.length === 2) {
    searchTime = range.join(",");
  }
  // 可以在这里将 dateRange 用于查询参数
  console.log("日期范围改变:", searchTime);
  queryParams.value.searchTaskStartTime = searchTime;
}

// ===================== 业务操作逻辑 =======================

/** 查看详情按钮操作 */
function handleDetail(row) {
  const batchId = row.batchId;
  router.push({
    path: "/obtainConvert/batch-detail/index/" + batchId,
    query: {
      id: row.id,
      analysisRuleId: row.analysisRuleId,
      analysisTotalNum: row.analysisTotalNum,
    },
  });
}

// 重新处理 ==> 暂时通过手动触发的方式实现初次进行数据解析映射的出库逻辑（模拟调用工具数据接口）
async function handleReprocess(row) {
  console.log("重新处理 ==>", row);
  modal.title = "重新处理数据";
  modal.type = "0";
  modal.open = true;
  modal.form = { ...row };
  // const confirmRes = await proxy.$modal.confirm('确认重新处理批次号为' + row.batchId + '"的数据项？').catch(() => {
  // });
  // if (!confirmRes) return;
  // await reprocessApi(row);
  // proxy.$modal.msgSuccess("重新处理成功！")
  // await getList();
}

function handleFailMsg(row) {
  modal.title = "失败理由";
  modal.type = "1";
  modal.open = true;
  modal.form = { ...row };
}

/**获取对应的数据源字典信息*/
async function getDataOriginSelect() {
  const { data } = await queryDataOriginSelect();
  console.log("获取数据源字典信息", data);
  bizDict.dataOriginOptions = data;
}

/**获取解析规则字典信息*/
async function getRuleBaseSelect() {
  // , ruleStatus: 'COMPLETED'
  const { data } = await queryRuleBaseSelect({ ruleType: "ANALYSIS" });
  console.log("获取数据源字典信息", data);
  bizDict.ruleBaseSelectOptions = data;
}

/** 根据数据源ID获取规则名称 */
function getRuleNameText(ruleId) {
  if (!ruleId || !bizDict.ruleBaseSelectOptions) return ruleId;
  const option = bizDict.ruleBaseSelectOptions.find((item) => item.value === ruleId);
  return option ? option.text : ruleId;
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.batchId);
  selectRows.value = selection;
  console.log("多选框选中数据", selectRows.value);
  multiple.value = !selection.length;
}

function getRowClassName({ row, rowIndex }) {
  let className = "";
  // 斑马纹效果
  if (rowIndex % 2 === 0) {
    className += "even-row ";
  } else {
    className += "odd-row ";
  }
  return className.trim();
}

/** 处理解析映射按钮点击 */
function handleDeduplicate() {
  // 检查是否有选中的批次
  if (!ids.value || ids.value.length === 0) {
    proxy.$modal.msgWarning("请先选择要执行归一合并的批次");
    return;
  }


  // 显示确认对话框
  proxy.$modal.confirm(
      `确定要对选中的 ${ids.value.length} 个批次执行归一合并操作吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
  ).then(() => {
    // 用户确认，调用解析映射接口
    proxy.$modal.loading("正在执行归一合并操作，请稍候...");

    parseDeduplicateBatch(ids.value).then(response => {
      proxy.$modal.closeLoading();
      if (response.code === 200) {
        proxy.$modal.msgSuccess("归一合并操作成功");
        // 刷新批次数据
        getList();
      } else {
        proxy.$modal.msgError(`归一合并操作失败: ${response.msg}`);
      }
    }).catch(error => {
      proxy.$modal.closeLoading();
      console.error("归一合并操作失败:", error);
      proxy.$modal.msgError("归一合并操作失败，请重试");
    });
  }).catch(() => {
    // 用户取消操作
  });
}

getList();
getDataOriginSelect();
getRuleBaseSelect();
</script>

<style lang="scss" scoped>
// 引入 base.scss 样式文件
@import "../../source/components/base.scss";

.my_drawer_title {
  display: flex;
  justify-content: space-between;
}
.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
  }

  .btn_add {
    background-color: rgb(0, 120, 212);
  }
}

// 单独设置右侧抽屉的样式，防止搜索部分被遮挡。与设计图不同～
:deep(.drawer-content-new) {
  margin-top: 0;
  border-top: 1px solid #bccde0;
  max-height: calc(100vh - 85px);
}

.operation-btn {
  display: inline-block;
  margin: 0 5px;
  color: #0076d0 !important;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
}
</style>
