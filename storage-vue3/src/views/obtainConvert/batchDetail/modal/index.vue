<template>
  <el-dialog :title="modal.title" v-model="modal.open" width="500px" append-to-body destroy-on-close>
    <el-form ref="parsingMappingTaskRef" :model="form" label-width="120px">
      <el-form-item label="批次号" prop="batchId">
        <el-input v-model="form.batchId" disabled placeholder="批次号"/>
      </el-form-item>
      <el-form-item label="映射数量" prop="analysisTotalNum">
        <el-input v-model="form.analysisTotalNum" disabled placeholder=""/>
      </el-form-item>
      <el-form-item label="拒绝理由" prop="rejectedReason">
        <el-input v-model="form.rejectedReason" type="textarea" placeholder="拒绝理由"></el-input>
      </el-form-item>
      <el-form-item label="失败数据重新处理" prop="reprocessFlag">
        <el-radio-group v-model="form.reprocessFlag">
          <el-radio value="1">是（数据发送至解析工具，进行再次处理）</el-radio>
          <el-radio value="0">否（数据无需再次处理）</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-text class="mx-1" type="danger">是否需要人工修改规则后再次发送数据，或工具根据失败数据自动更新规则</el-text>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-divider />
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  updateAuditStatusApi
} from "@/api/obtainConvert/parsingMappingTask.js"
import {getCurrentInstance} from "vue";

const {proxy} = getCurrentInstance();

const props = defineProps({
  modal: {
    type: Object,
  }
});
const emit = defineEmits(["ok"]);

const form = computed({
  get() {
    return props.modal.form;
  },
  set() {
  }
});

/** 提交按钮 */
async function submitForm() {
  console.log("提交表单", form.value)
  await updateAuditStatusApi(form.value);
  proxy.$modal.msgError("审核拒绝成功！")
  props.modal.open = false
  emit("ok");
}

</script>

<style scoped>

</style>
