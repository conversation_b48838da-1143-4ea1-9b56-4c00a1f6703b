<template>
  <div class="app-container">
    <!-- 搜索菜单 -->
    <FormSearch
        v-model="queryParams"
        :formItems="formItems"
        :sys_normal_disable="[]"
        @search="handleQuery"
        @reset="resetQuery"
    >
      <template #btn>
        <el-button
            type="success"
            @click="handleApproved"
            v-hasPermi="['system:source:add']"
        >审核通过
        </el-button>
        <el-button
            type="danger"
            @click="handleRejected"
            v-hasPermi="['system:source:add']"
        >审核拒绝
        </el-button>
        <el-button
            color="#6300BF"
            @click="handleDelete"
            v-hasPermi="['system:source:add']"
        >删除标记
        </el-button>
      </template>
    </FormSearch>

    <!-- 表格部分 -->
    <TableForm
        ref="tableFormRef"
        :columns="columns"
        :tableData="dataSourceList"
        :formMaxWidth="formMaxWidth"
        :showFields="showFields"
        :form-config="formConfig"
        v-loading="loading"
        :showIndex="true"
        :showEditBtn="false"
        :total="total"
    >
      <!-- 自定义操作按钮 -->
      <template #operation="{ row }">
        <el-button
            @click="handleDetail(row)"
            type="text"
            size="small"
        >详情
        </el-button>
      </template>

      <template #pagination>
        <pagination
            :small="small"
            layout="prev, pager, next, sizes"
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="getList"
        />
      </template>
    </TableForm>

    <modal-form :modal="modal" @ok="getList"/>
  </div>
</template>

<script setup>
import {ref, reactive, computed, getCurrentInstance} from 'vue'
import {listBatchDetail, updateAuditStatusApi} from "@/api/obtainConvert/parsingMappingTask.js"
import FormSearch from '../components/FormSearch.vue'
import TableForm from '../components/TableForm.vue'
import Pagination from '@/components/Pagination/index.vue'
import modalForm from './modal';

const {proxy} = getCurrentInstance();
import {useRouter, useRoute} from 'vue-router'

const router = useRouter();
const route = useRoute();
// 数据和状态
const dataSourceList = ref([])
const loading = ref(true)
const small = ref(true)
const total = ref(0)
const formMaxWidth = ref('50%')
const showFields = ref(['name'])
const tableFormRef = ref(null)
// 弹窗数据
const modal = reactive({
  title: '',
  open: false,
  form: {}
});
// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  batchId: route.params.batchId,
  articleId: '',
  originalArticleId: '',
  articleTitle: '',
  journalTitle: ''
})

// 表单配置示例
const formConfig = [];

// 搜索表单配置
const formItems = computed(() => [
  {
    label: '篇级ID',
    prop: 'originalArticleId',
    component: 'el-input',
    props: {
      placeholder: '篇级ID',
      clearable: true,
      style: {width: '200px'},
    }
  },
  {
    label: '唯一标识',
    prop: 'articleId',
    component: 'el-input',
    props: {
      placeholder: '唯一标识',
      clearable: true,
      style: {width: '200px'},
    }
  },
  {
    label: '题名',
    prop: 'articleTitle',
    component: 'el-input',
    props: {
      placeholder: '题名',
      clearable: true,
      style: {width: '200px'},
    }
  },
  {
    label: '期刊题名',
    prop: 'journalTitle',
    component: 'el-input',
    props: {
      placeholder: '期刊题名',
      clearable: true,
      style: {width: '200px'},
    }
  }
])

// 表格列配置
const columns = ref([
  {prop: 'originalArticleId', label: '篇级ID', width: '180'},
  {prop: 'identifierId', label: '唯一标识', width: '140'},
  {prop: 'title', label: '题名', width: '150'},
  {prop: 'journal-title', label: '期刊题名', width: '120'},
  {prop: 'issn', label: 'ISSN', width: '140'},
  {prop: 'year', label: '年', width: '100'},
  {prop: 'issue', label: '卷', width: '100'},
  {prop: 'volume', label: '期'}
])

// 查询数据源列表
async function getList() {
  loading.value = true;
  try {
    const {data} = await listBatchDetail(queryParams);
    dataSourceList.value = data.contentData;
    total.value = data.totalSize;
  } catch (error) {
    console.error('获取批次数据详情失败:', error);
    proxy.$modal.msgError("获取批次数据详情失败");
  } finally {
    loading.value = false;
  }
}

// 重置查询条件
const resetQuery = () => {
  const params = {
    articleId: '',
    originalArticleId: '',
    articleTitle: '',
    journalTitle: ''
  }
  handleQuery(params)
}

/** 搜索按钮操作 */
function handleQuery(params) {
  console.log('搜索条件', queryParams);
  queryParams.pageNum = 1;
  Object.assign(queryParams, params)
  getList();
}

// ===================== 业务操作逻辑 =======================

/** 审核通过 */
async function handleApproved() {
  const confirmRes = await proxy.$modal.confirm('确认审核通过批次为' + route.params.batchId + '"的数据项？').catch(() => {
  });
  if (!confirmRes) return;
  const params = {
    id: route.query.id,
    batchId: route.params.batchId,
    auditStatus: '通过',
  }
  await updateAuditStatusApi(params);
  proxy.$modal.msgSuccess("审核通过成功！")
  await getList();
}

/** 审核拒绝 */
async function handleRejected() {
  console.log('审核拒绝', route.query)
  const params = {
    id: route.query.id,
    batchId: route.params.batchId,
    analysisTotalNum: route.query.analysisTotalNum,
    reprocessFlag: "1",
    rejectedReason: "",
    auditStatus: '拒绝',
  }
  modal.title = '拒绝理由';
  modal.open = true;
  modal.form = {...params};
}

/** 删除标记 */
async function handleDelete() {
  const confirmRes = await proxy.$modal.confirm('确认删除标记批次为' + route.params.batchId + '"的数据项？').catch(() => {
  });
  if (!confirmRes) return;
  const params = {
    id: route.query.id,
    batchId: route.params.batchId,
    auditStatus: '删除',
  }
  await updateAuditStatusApi(params);
  proxy.$modal.msgSuccess("删除标记成功！")
  await getList();
}

// 详情页面
function handleDetail(row) {
  console.log('查看详情', row)
  const currentIndex = dataSourceList.value.findIndex(item => item.id === row.id) + queryParams.pageSize * (queryParams.pageNum - 1)
  router.push({
    path: '/obtainConvert/article-detail/index/' + row.originalArticleId,
    query: {
      batchId: route.params.batchId,
      analysisRuleId: route.query.analysisRuleId,
      originalArticleId: row.originalArticleId,
      total: total.value,
      currentPage: currentIndex + 1,
    }
  });
}

/** 查询批次数据详细 */
getList();

</script>

<style lang="scss" scoped>
:deep(.pagination-container) {
  justify-content: flex-start !important;
  padding: 10px 0;
}

:deep(.custom-form-item) {
  margin-bottom: 18px;

  .el-form-item__label {
    font-weight: normal;
    color: var(--el-text-color-regular);
    padding: 0 12px 0 0;
  }

  .custom-select {
    width: 100%;

    .el-input__wrapper {
      padding: 1px 11px;
    }

    .el-input__inner {
      height: 32px;
      line-height: 32px;
    }
  }

  .custom-textarea {
    .el-textarea__inner {
      padding: 5px 11px;
      min-height: 80px;
      font-size: 14px;
    }
  }
}
</style>
