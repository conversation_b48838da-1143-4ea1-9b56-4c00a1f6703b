<template>
  <div class="tree-view-item" :style="itemStyle">
    <div class="tree-view-item-line">
      <span class="tree-view-item-indent" :style="indentStyle"></span>

      <!-- 展开/折叠按钮 -->
      <span
        v-if="hasChildren"
        class="tree-view-item-toggle"
        @click="toggleExpand"
      >
        {{ isExpanded ? '▼' : '▶' }}
      </span>
      <span v-else class="tree-view-item-toggle-placeholder"></span>

      <!-- XML 节点内容 -->
      <span class="tree-view-item-content">
        <!-- 开始标签 -->
        <span class="xml-tag" v-if="!isRoot">
          <span class="xml-bracket">&lt;</span>
          <span class="xml-tag-name">{{ displayKey }}</span>

          <!-- 属性 -->
          <template v-if="hasAttributes">
            <span class="xml-attributes-container" :class="{'multi-attr': Object.keys(attributes).length > 2}">
              <span v-for="(attrValue, attrName) in attributes" :key="attrName" class="xml-attribute">
                <span class="xml-attribute-name">{{ attrName }}</span>
                <span class="xml-equal">=</span>
                <span class="xml-attribute-value">"{{ attrValue }}"</span>
              </span>
            </span>
          </template>

          <!-- 如果没有子节点和文本，则显示自闭合标签 -->
          <span class="xml-bracket" v-if="!hasChildren && !hasText">/&gt;</span>
          <span class="xml-bracket" v-else>&gt;</span>
        </span>

        <!-- 文本内容 -->
        <template v-if="!hasChildren && hasText">
          <span class="xml-text">{{ textContent }}</span>
          <!-- 结束标签 -->
          <span class="xml-tag" v-if="!isRoot">
            <span class="xml-bracket">&lt;/</span>
            <span class="xml-tag-name">{{ displayKey }}</span>
            <span class="xml-bracket">&gt;</span>
          </span>
        </template>
      </span>
    </div>

    <!-- 子节点 -->
    <div v-if="hasChildren && isExpanded" class="tree-view-item-children">
      <tree-view-item
        v-for="child in children"
        :key="child.key"
        :key-name="child.key"
        :value="child.value"
        :depth="depth + 1"
        :options="options"
      ></tree-view-item>

      <!-- 父节点的结束标签 -->
      <div class="tree-view-item closing-tag">
        <div class="tree-view-item-line">
          <span class="tree-view-item-indent" :style="indentStyle"></span>
          <span class="tree-view-item-toggle-placeholder"></span>
          <span class="xml-tag">
            <span class="xml-bracket">&lt;/</span>
            <span class="xml-tag-name">{{ displayKey }}</span>
            <span class="xml-bracket">&gt;</span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  keyName: {
    type: String,
    required: true
  },
  value: {
    type: [Object, Array, String, Number, Boolean],
    required: true
  },
  depth: {
    type: Number,
    default: 0
  },
  options: {
    type: Object,
    required: true
  },
  isRoot: {
    type: Boolean,
    default: false
  }
})

// 状态
const isExpanded = ref(true) // 默认展开所有节点

// 计算属性
const hasChildren = computed(() => {
  if (!props.value || typeof props.value !== 'object') return false

  // 如果是数组，并且有内容，则认为有子节点
  if (Array.isArray(props.value)) {
    return props.value.length > 0
  }

  const keys = Object.keys(props.value)
  return keys.some(key => !['#text', '@_Version', '@_IssnType', '@_CitedMedium', '@_EIdType', '@_ValidYN', '@_PubStatus', '@_Status', '@_Owner', '@_IndexingMethod', '@_PubModel', '@_CompleteYN', '@_DateType', '@_IdType'].includes(key))
})

const children = computed(() => {
  if (!hasChildren.value) return []

  // 如果是数组，则将每个数组项作为同名的子元素
  if (Array.isArray(props.value)) {
    return props.value.map((item, index) => ({
      key: props.keyName, // 使用相同的标签名
      value: item
    }))
  }

  return Object.entries(props.value)
    .filter(([key]) => !['#text', '@_Version', '@_IssnType', '@_CitedMedium', '@_EIdType', '@_ValidYN', '@_PubStatus', '@_Status', '@_Owner', '@_IndexingMethod', '@_PubModel', '@_CompleteYN', '@_DateType', '@_IdType'].includes(key))
    .map(([key, value]) => ({ key, value }))
})

const hasAttributes = computed(() => {
  if (!props.value || typeof props.value !== 'object') return false

  // 检查是否有以@_开头的属性
  if (Array.isArray(props.value)) {
    return false // 数组没有直接属性
  }

  return Object.keys(props.value).some(key => key.startsWith('@_'))
})

const attributes = computed(() => {
  if (!hasAttributes.value) return {}

  const attrs = {}
  Object.entries(props.value).forEach(([key, value]) => {
    if (key.startsWith('@_')) {
      // 移除@_前缀
      const attrName = key.substring(2)
      attrs[attrName] = value
    }
  })
  return attrs
})

const hasText = computed(() => {
  if (!props.value) return false

  if (typeof props.value === 'string' || typeof props.value === 'number') {
    return true
  }

  if (typeof props.value === 'object' && !Array.isArray(props.value)) {
    return props.value['#text'] !== undefined &&
           props.value['#text'] !== null &&
           props.value['#text'].toString().trim() !== ''
  }

  return false
})

const textContent = computed(() => {
  if (typeof props.value === 'string' || typeof props.value === 'number') {
    return props.value.toString()
  }

  if (hasText.value && props.value['#text'] !== undefined) {
    return props.value['#text'].toString()
  }

  return ''
})

const displayKey = computed(() => {
  if (props.isRoot) {
    // 如果是根节点，优先使用options中的rootObjectKey
    // 如果没有设置，则使用传入的keyName
    return props.options.rootObjectKey || props.keyName || 'root'
  }
  return props.keyName
})

// 样式
const itemStyle = computed(() => ({
  fontSize: props.options.fontSize || '13px'
}))

const indentStyle = computed(() => ({
  paddingLeft: `${props.depth * (parseInt(props.options.indentSize || '12') || 12)}px`
}))

const childIndentStyle = computed(() => ({
  paddingLeft: `${props.depth * (parseInt(props.options.indentSize || '12') || 12)}px`,
  fontSize: props.options.fontSize || '13px'
}))

// 方法
const toggleExpand = () => {
  if (hasChildren.value) {
    isExpanded.value = !isExpanded.value
  }
}
</script>

<style scoped>
.tree-view-item {
  position: relative;
  width: max-content;
  min-width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.tree-view-item-line {
  white-space: nowrap;
  padding: 0;
  font-family: Monaco, Menlo, Consolas, monospace;
  line-height: 1.4;
  display: flex;
  flex-wrap: nowrap;
  align-items: flex-start;
  width: max-content;
  min-width: 100%;
}

.tree-view-item:hover > .tree-view-item-line {
  background-color: rgba(245, 247, 250, 0.5);
}

.tree-view-item-indent {
  display: inline-block;
  flex-shrink: 0;
}

.tree-view-item-toggle {
  display: inline-block;
  width: 12px;
  text-align: center;
  cursor: pointer;
  color: #666;
  user-select: none;
  flex-shrink: 0;
  font-size: 10px;
  vertical-align: top;
}

.tree-view-item-toggle:hover {
  color: #000;
}

.tree-view-item-toggle-placeholder {
  display: inline-block;
  width: 12px;
  flex-shrink: 0;
}

.tree-view-item-children {
  padding-left: 12px;
  border-left: 1px dotted #ddd;
  margin-left: 0;
  width: max-content;
  min-width: calc(100% - 12px);
  display: flex;
  flex-direction: column;
}

.tree-view-item-children > .tree-view-item {
  margin-top: 1px;
  margin-bottom: 1px;
  width: 100%;
}

/* XML 样式 */
.tree-view-item-content {
  display: inline-flex;
  white-space: nowrap;
  overflow: visible;
  align-items: center;
  flex-wrap: nowrap;
  flex: 0 0 auto;
}

.xml-tag {
  color: #800080;
  display: inline-flex;
  flex-wrap: nowrap;
  align-items: center;
  white-space: nowrap;
}

.xml-bracket {
  color: #800080;
}

.xml-tag-name {
  color: #800080;
  font-weight: normal;
}

.xml-attributes-container {
  display: inline-flex;
  flex-wrap: nowrap;
  align-items: center;
  gap: 4px; /* 使用gap属性确保属性之间有一致的间距 */
  padding-left: 2px; /* 在标签名和第一个属性之间添加一些额外空间 */
}

/* 当有多个属性时特殊处理 */
.multi-attr {
  margin-left: 4px;
  padding-left: 0;
  display: inline-flex;
  flex-wrap: nowrap;
  gap: 6px; /* 增加多属性情况下的间距 */
}

.xml-attribute {
  margin: 0; /* 移除margin，使用上面的gap替代 */
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  background-color: rgba(245, 247, 250, 0.3); /* 轻微的背景色以区分不同属性 */
  border-radius: 2px;
  padding: 0 2px;
}

.xml-attribute-name {
  color: #ff0000;
  padding: 0 1px;
}

.xml-equal {
  color: #000000;
  margin: 0 1px;
}

.xml-attribute-value {
  color: #0000ff;
  max-width: 300px; /* 防止非常长的属性值导致布局问题 */
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 1px;
}

/* 给长属性值添加特殊处理 */
.xml-text {
  color: #000000;
  margin: 0 1px;
  white-space: nowrap;
  display: inline;
}

/* 专门设置关闭标签的样式 */
.closing-tag {
  margin-top: 2px;
  margin-bottom: 1px;
  width: 100%;
}

/* 滚动条样式优化 */
.tree-view-item::-webkit-scrollbar {
  height: 6px;
}

.tree-view-item::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.tree-view-item::-webkit-scrollbar-track {
  background: #f5f7fa;
}

/* 针对多个属性的标签，优化显示 */
.xml-tag-name + .xml-attributes-container {
  margin-left: 2px; /* 确保标签名和属性容器之间有空间 */
}
</style>
