<template>
  <div class="tree-view">
    <div v-if="!parsedData" class="tree-view-empty">No data</div>
<!--    <div v-if="parsedData">-->
<!--      <pre>{{ parsedData }}</pre>-->
<!--    </div>-->
        <tree-view-item
            v-else
            :key-name="mergedOptions.rootObjectKey || 'root'"
            :value="parsedData"
            :depth="0"
            :options="mergedOptions"
            :is-root="true"
        ></tree-view-item>
  </div>
</template>

<script setup>
import {ref, watch, watchEffect} from 'vue'
import TreeViewItem from './TreeViewItem.vue'
import {XMLParser} from 'fast-xml-parser'

const props = defineProps({
  xmlData: {
    type: String,
    default: ''
  },
  options: {
    type: Object,
    default: () => ({})
  }
})

// 默认配置选项
const defaultOptions = {
  maxDepth: -1,         // 展开所有层级
  rootObjectKey: '',    // 根节点名称不预设，使用实际XML的根节点
  modifiable: false,    // 禁止修改
  link: false,          // 禁用链接
  colorize: true,       // 启用颜色
  fontSize: '12px',     // 字体大小
  indentSize: '10px',   // 缩进大小
  indentChar: '  '      // 缩进字符 - 两个空格
}

// 解析XML数据
const parseXmlData = (xmlString) => {
  if (!xmlString) {
    parsedData.value = null
    return
  }
  try {
    // 使用标准的XML解析配置
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: "@_",
      allowBooleanAttributes: true,
      format: true,
      indentBy: "  ",
      isArray: (name, jPath, isLeafNode, isAttribute) => {
        // 常见的应该作为数组处理的元素
        const arrayElements = [
          'PubMedPubDate', 'Author', 'ArticleIdList', 'Keyword',
          'ELocationID', 'PublicationType', 'History', 'PubDate'
        ];
        return arrayElements.includes(name);
      },
      processEntities: false,
      stopNodes: ['#text']
    });

    // 解析XML
    const jsonObj = parser.parse(xmlString);

    parsedData.value = jsonObj;
    console.log('Parsed XML:', jsonObj);
  } catch (e) {
    console.error('XML 解析错误:', e);
    parsedData.value = null;
  }
}

// 合并默认选项和传入的选项
const mergedOptions = ref({...defaultOptions, ...props.options})

// 解析后的数据
const parsedData = ref(null)

watch(() => props.xmlData, (newValue) => {
  if (newValue) {
    parseXmlData(newValue);
  }
}, { immediate: true });

// 监听options变化
watchEffect(() => {
  // 当props.options变化时，更新合并的选项
  mergedOptions.value = {...defaultOptions, ...props.options};
});
</script>

<style scoped>
.tree-view {
  position: relative;
  cursor: default;
  user-select: text;
  white-space: nowrap;
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  width: max-content;
  min-width: 100%;
  box-sizing: border-box;
  padding: 4px;
  background-color: #ffffff;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.4;
  will-change: transform; /* 提高滚动性能 */
}

.tree-view-empty {
  color: #666;
  font-style: italic;
  padding: 8px;
  text-align: center;
}

/* 滚动条样式 */
.tree-view::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.tree-view::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.tree-view::-webkit-scrollbar-track {
  background: #f5f7fa;
}

.tree-view::-webkit-scrollbar-corner {
  background: #f5f7fa;
}

/* XML高亮样式 */
:deep(.tree-view-item) {
  line-height: 1.4;
  padding: 0 0 0 0; /* 移除左右padding */
  white-space: nowrap; /* 确保不换行 */
}

:deep(.tree-view-item:hover) {
  background-color: rgba(245, 247, 250, 0.3);
}

:deep(.tree-view-item-indent) {
  padding-left: 12px !important; /* 固定缩进大小 */
}

:deep(.tree-view-item-toggle) {
  width: 12px !important; /* 减小展开/折叠按钮宽度 */
  color: #666;
  font-size: 10px;
}

:deep(.tree-view-item-children) {
  padding-left: 12px !important; /* 固定子节点缩进 */
  margin-left: 0 !important;
}

:deep(.xml-tag) {
  color: #800080; /* 标签颜色 - 紫色 */
}

:deep(.xml-attribute-name) {
  color: #ff0000; /* 属性名颜色 - 红色 */
}

:deep(.xml-attribute-value) {
  color: #0000ff; /* 属性值颜色 - 蓝色 */
}

:deep(.xml-text) {
  color: #000000; /* 文本颜色 - 黑色 */
}

/* 改进的XML属性容器样式 */
:deep(.xml-attributes-container) {
  display: inline-flex;
  flex-wrap: nowrap;
  align-items: center;
  gap: 4px;
}

/* 多属性显示 */
:deep(.multi-attr) {
  display: inline-flex;
  flex-wrap: nowrap;
  gap: 6px;
  margin-left: 4px;
}

/* XML属性样式 */
:deep(.xml-attribute) {
  background-color: rgba(245, 247, 250, 0.3);
  border-radius: 2px;
  padding: 0 2px;
  margin: 1px 0;
  white-space: nowrap;
  display: inline-flex;
}
</style>
