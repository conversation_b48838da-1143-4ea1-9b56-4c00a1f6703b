<template>
  <div class="app-container">
    <!-- 顶部搜索+翻页 -->
    <FormSearch
      v-model="queryParams"
      :formItems="formItems"
      searchBtnName="查询"
      @search="handleQuery"
      @reset="resetQuery"
    >
      <template #btn>
        <div class="page-btn-container">
          <el-button :disabled="isFirstPage" @click="prevPage" class="page-btn"
            >上一篇</el-button
          >
          <span>{{ `${currentPage} / ${total}` }}</span>
          <el-button :disabled="isLastPage" @click="nextPage" class="page-btn"
            >下一篇</el-button
          >
        </div>
      </template>
    </FormSearch>
    <!-- 内容区域 -->
    <div class="compare-content">
      <!-- IMI格式数据 -->
      <div class="compare-table-section">
        <div class="table-title">
          <span>IMI格式数据</span>
        </div>
        <div class="compare-table-scroll" ref="imiTableRef">
          <el-table
            ref="imiTable"
            :data="filteredData"
            style="width: 100%"
            :row-class-name="getRowClassName"
            size="small"
            border
            height="100%"
            :scrollbar-always-on="true"
          >
            <el-table-column
              prop="imiField"
              label="元数据字段项"
              width="110"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="scope.row.imiPath"
                  placement="left"
                >
                  {{ scope.row.imiField }}
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              prop="imiContent"
              label="元数据内容"
              :show-overflow-tooltip="true"
            />
          </el-table>
        </div>
      </div>
      <!-- 原始格式数据 -->
      <div class="compare-table-section">
        <div class="table-title">
          <span>原始格式数据</span>
        </div>
        <div class="compare-table-scroll" ref="originTableRef">
          <el-table
            ref="originTable"
            :data="filteredData"
            style="width: 100%"
            :row-class-name="getRowClassName"
            size="small"
            border
            height="100%"
            :scrollbar-always-on="true"
          >
            <el-table-column
              prop="originField"
              label="元数据字段项"
              width="110"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="scope.row.originPath"
                  placement="left"
                >
                  {{ scope.row.originField }}
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              prop="originContent"
              label="元数据内容"
              :show-overflow-tooltip="true"
            />
          </el-table>
        </div>
      </div>
      <!-- 原始XML数据 -->
      <div class="xml-section">
        <div class="table-title">
          <span>原始XML数据</span>
        </div>
        <div class="xml-content">
          <pre v-if="!xmlData">暂无数据</pre>
          <tree-view
            v-else
            :xml-data="xmlData"
            :options="treeViewOptions"
          ></tree-view>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  computed,
  getCurrentInstance,
  nextTick,
  onMounted,
  onUnmounted,
  ref,
} from "vue";
import { useRoute } from "vue-router";
import { articleDetailApi } from "@/api/obtainConvert/parsingMappingTask.js";
import TreeView from "./components/TreeView/index.vue";
import { FormSearch } from "@/components/Business/index.js";
const { proxy } = getCurrentInstance();
const route = useRoute();
// 基础数据
const loading = ref(false);
const currentPage = ref(0);
const total = ref(0);
const batchId = ref("1");
const originalArticleId = ref("1");
const queryParams = {
  batchId: batchId.value,
  originalArticleId: originalArticleId.value,
  searchText: "",
};
const formItems = ref([
  {
    label: "内容检索",
    prop: "searchText",
    component: "el-input",
    props: {
      placeholder: "内容检索",
      clearable: true,
      style: { width: "206px" },
    },
  },
]);
// 表格数据结构
const tableData = ref([
  {
    imiField: "publisher id",
    imiContent: "AAN19981611-4036",
    imiPath: "publisher_id",
    originField: "publisher id",
    originContent: "AAN19981611-4036",
    originPath: "publisher_id",
    diffFlag: false,
  },
  {
    imiField: "doi",
    imiContent: "10.1159/000046450",
    imiPath: "doi",
    originField: "doi",
    originContent: "10.1159/000046450",
    originPath: "doi",
    diffFlag: true,
  },
  {
    imiField: "article_title",
    imiContent: "Glycoproteins and Their Relationship to Human Disease",
    imiPath: "article_title",
    originField: "article_title",
    originContent: "Glycoproteins and Their Relationship to Human Disease",
    originPath: "article_title",
    diffFlag: false,
  },
  {
    imiField: "author",
    imiContent: "Brockhausen",
    imiPath: "author",
    originField: "author",
    originContent: "Brockhausen",
    originPath: "author",
    diffFlag: false,
  },
  {
    imiField: "institution",
    imiContent: "Marine Biological Laboratory, Voods Hole, Mass., USA",
    imiPath: "institution",
    originField: "aff",
    originContent: "Marine Biological Laboratory, Voods Hole, Mass., USA",
    originPath: "aff",
    diffFlag: true,
  },
  {
    imiField: "ppub-year",
    imiContent: "1998",
    imiPath: "ppub-year",
    originField: "ppub-year",
    originContent: "1998",
    originPath: "ppub-year",
    diffFlag: false,
  },
  {
    imiField: "ppub-month",
    imiContent: "10",
    imiPath: "ppub-month",
    originField: "ppub-month",
    originContent: "10",
    originPath: "ppub-month",
    diffFlag: false,
  },
  {
    imiField: "ppub-day",
    imiContent: "12",
    imiPath: "ppub-day",
    originField: "ppub-day",
    originContent: "12",
    originPath: "ppub-day",
    diffFlag: false,
  },
  {
    imiField: "volume",
    imiContent: "161",
    imiPath: "volume",
    originField: "volume",
    originContent: "161",
    originPath: "volume",
    diffFlag: false,
  },
  {
    imiField: "issue",
    imiContent: "1-4",
    imiPath: "issue",
    originField: "issue",
    originContent: "1-4",
    originPath: "issue",
    diffFlag: false,
  },
  {
    imiField: "fpage",
    imiContent: "36",
    imiPath: "fpage",
    originField: "fpage",
    originContent: "36",
    originPath: "fpage",
    diffFlag: false,
  },
  {
    imiField: "lpage",
    imiContent: "78",
    imiPath: "lpage",
    originField: "lpage",
    originContent: "78",
    originPath: "lpage",
    diffFlag: false,
  },
  {
    imiField: "publisher id",
    imiContent: "AAN19981611-4036",
    imiPath: "publisher_id",
    originField: "publisher id",
    originContent: "AAN19981611-4036",
    originPath: "publisher_id",
    diffFlag: false,
  },
  {
    imiField: "doi",
    imiContent: "10.1159/000046450",
    imiPath: "doi",
    originField: "doi",
    originContent: "10.1159/000046450",
    originPath: "doi",
    diffFlag: true,
  },
  {
    imiField: "article_title",
    imiContent: "Glycoproteins and Their Relationship to Human Disease",
    imiPath: "article_title",
    originField: "article_title",
    originContent: "Glycoproteins and Their Relationship to Human Disease",
    originPath: "article_title",
    diffFlag: false,
  },
  {
    imiField: "author",
    imiContent: "Brockhausen",
    imiPath: "author",
    originField: "author",
    originContent: "Brockhausen",
    originPath: "author",
    diffFlag: false,
  },
  {
    imiField: "institution",
    imiContent: "Marine Biological Laboratory, Voods Hole, Mass., USA",
    imiPath: "institution",
    originField: "aff",
    originContent: "Marine Biological Laboratory, Voods Hole, Mass., USA",
    originPath: "aff",
    diffFlag: true,
  },
  {
    imiField: "ppub-year",
    imiContent: "1998",
    imiPath: "ppub-year",
    originField: "ppub-year",
    originContent: "1998",
    originPath: "ppub-year",
    diffFlag: false,
  },
  {
    imiField: "ppub-month",
    imiContent: "10",
    imiPath: "ppub-month",
    originField: "ppub-month",
    originContent: "10",
    originPath: "ppub-month",
    diffFlag: false,
  },
  {
    imiField: "ppub-day",
    imiContent: "12",
    imiPath: "ppub-day",
    originField: "ppub-day",
    originContent: "12",
    originPath: "ppub-day",
    diffFlag: false,
  },
  {
    imiField: "volume",
    imiContent: "161",
    imiPath: "volume",
    originField: "volume",
    originContent: "161",
    originPath: "volume",
    diffFlag: false,
  },
  {
    imiField: "issue",
    imiContent: "1-4",
    imiPath: "issue",
    originField: "issue",
    originContent: "1-4",
    originPath: "issue",
    diffFlag: false,
  },
  {
    imiField: "fpage",
    imiContent: "36",
    imiPath: "fpage",
    originField: "fpage",
    originContent: "36",
    originPath: "fpage",
    diffFlag: false,
  },
  {
    imiField: "lpage",
    imiContent: "78",
    imiPath: "lpage",
    originField: "lpage",
    originContent: "78",
    originPath: "lpage",
    diffFlag: false,
  },
  {
    imiField: "publisher id",
    imiContent: "AAN19981611-4036",
    imiPath: "publisher_id",
    originField: "publisher id",
    originContent: "AAN19981611-4036",
    originPath: "publisher_id",
    diffFlag: false,
  },
  {
    imiField: "doi",
    imiContent: "10.1159/000046450",
    imiPath: "doi",
    originField: "doi",
    originContent: "10.1159/000046450",
    originPath: "doi",
    diffFlag: true,
  },
  {
    imiField: "article_title",
    imiContent: "Glycoproteins and Their Relationship to Human Disease",
    imiPath: "article_title",
    originField: "article_title",
    originContent: "Glycoproteins and Their Relationship to Human Disease",
    originPath: "article_title",
    diffFlag: false,
  },
  {
    imiField: "author",
    imiContent: "Brockhausen",
    imiPath: "author",
    originField: "author",
    originContent: "Brockhausen",
    originPath: "author",
    diffFlag: false,
  },
  {
    imiField: "institution",
    imiContent: "Marine Biological Laboratory, Voods Hole, Mass., USA",
    imiPath: "institution",
    originField: "aff",
    originContent: "Marine Biological Laboratory, Voods Hole, Mass., USA",
    originPath: "aff",
    diffFlag: true,
  },
  {
    imiField: "ppub-year",
    imiContent: "1998",
    imiPath: "ppub-year",
    originField: "ppub-year",
    originContent: "1998",
    originPath: "ppub-year",
    diffFlag: false,
  },
  {
    imiField: "ppub-month",
    imiContent: "10",
    imiPath: "ppub-month",
    originField: "ppub-month",
    originContent: "10",
    originPath: "ppub-month",
    diffFlag: false,
  },
  {
    imiField: "ppub-day",
    imiContent: "12",
    imiPath: "ppub-day",
    originField: "ppub-day",
    originContent: "12",
    originPath: "ppub-day",
    diffFlag: false,
  },
  {
    imiField: "volume",
    imiContent: "161",
    imiPath: "volume",
    originField: "volume",
    originContent: "161",
    originPath: "volume",
    diffFlag: false,
  },
  {
    imiField: "issue",
    imiContent: "1-4",
    imiPath: "issue",
    originField: "issue",
    originContent: "1-4",
    originPath: "issue",
    diffFlag: false,
  },
  {
    imiField: "fpage",
    imiContent: "36",
    imiPath: "fpage",
    originField: "fpage",
    originContent: "36",
    originPath: "fpage",
    diffFlag: false,
  },
  {
    imiField: "lpage",
    imiContent: "78",
    imiPath: "lpage",
    originField: "lpage",
    originContent: "78",
    originPath: "lpage",
    diffFlag: false,
  },
]);
const searchText = ref("");
const xmlData = ref(`<?xml version="1.0" encoding="UTF-8"?>
<article>
  <front>
    <journal-meta>
      <journal-id journal-id-type="publisher-id">AAN19981611-4036</journal-id>
      <journal-title-group>
        <journal-title>Journal of Glycobiology</journal-title>
      </journal-title-group>
      <issn pub-type="ppub">1234-5678</issn>
      <publisher>
        <publisher-name>Example Publisher</publisher-name>
      </publisher>
    </journal-meta>
    <article-meta>
      <article-id pub-id-type="doi">10.1159/000046450</article-id>
      <title-group>
        <article-title>Glycoproteins and Their Relationship to Human Disease</article-title>
      </title-group>
      <contrib-group>
        <contrib contrib-type="author">
          <string-name>Brockhausen</string-name>
          <aff>Marine Biological Laboratory, Voods Hole, Mass., USA</aff>
        </contrib>
      </contrib-group>
      <pub-date pub-type="ppub">
        <year>1998</year>
        <month>10</month>
        <day>12</day>
      </pub-date>
      <volume>161</volume>
      <issue>1-4</issue>
      <fpage>36</fpage>
      <lpage>78</lpage>
    </article-meta>
  </front>
  <body>
    <sec>
      <title>Abstract</title>
      <p>This article discusses the importance of glycoproteins in human disease processes.</p>
    </sec>
    <sec>
      <title>Introduction</title>
      <p>Glycoproteins play crucial roles in many biological processes...</p>
    </sec>
  </body>
</article>`);

// TreeView 组件的配置选项 - 可选，组件内部已有默认配置
const treeViewOptions = {
  indentSize: "6px", // 控制节点缩进
  indentChar: "", // 使用两个空格作为缩进字符
};

// 计算属性
const isFirstPage = computed(
  () => currentPage.value === 1 || currentPage.value === 0
);
const isLastPage = computed(
  () => currentPage.value === total.value || currentPage.value === 0
);

// 导航方法
const prevPage = () => {
  if (isFirstPage.value) return;
  currentPage.value--;
  console.log("prevPage ==>", currentPage.value);
  fetchArticleDetail();
};
const nextPage = () => {
  if (isLastPage.value) return;
  currentPage.value++;
  // 获取数据信息
  console.log("nextPage ==>", currentPage.value);
  fetchArticleDetail();
};

// 过滤后的数据
const filteredData = computed(() => {
  if (!searchText.value) return tableData.value;
  const searchLower = searchText.value.toLowerCase();
  return tableData.value.filter(
    (item) =>
      item.originField.toLowerCase().includes(searchLower) ||
      item.originContent.toLowerCase().includes(searchLower) ||
      item.imiField.toLowerCase().includes(searchLower) ||
      item.imiContent.toLowerCase().includes(searchLower)
  );
});

// 获取行的类名
const getRowClassName = ({ row }) => {
  return row.diffFlag ? "diff" : "";
};

// 检索按钮数据处理逻辑
const handleSearch = () => {
  // 实现搜索逻辑
};

// 查询数据源列表
async function fetchArticleDetail(firstQuery = false) {
  if (loading.value) return;
  loading.value = true;
  // TODO: 接口错误，先打断点
  return;
  try {
    let queryParams = {
      batchId: batchId.value,
      originalArticleId: originalArticleId.value,
      analysisRuleId: route.query.analysisRuleId,
      currentPage: currentPage.value,
      firstQuery: firstQuery,
    };
    const { data } = await articleDetailApi(queryParams);
    console.log("篇级详情数据", data);
    // 设置XML数据，会触发预处理后由TreeView组件内部处理解析
    if (data?.basicXml) {
      xmlData.value = data.basicXml.content || "";
    }
    // 设置表格数据
    tableData.value = data?.metadataParamList || [];
  } catch (error) {
    console.error("获取篇级数据详情失败:", error);
  } finally {
    loading.value = false;
  }
}

// 简化的滚动同步逻辑
const imiTableRef = ref(null);
const originTableRef = ref(null);
const imiTable = ref(null);
const originTable = ref(null);
let isScrolling = false;

// 实现滚动同步
const syncScroll = () => {
  // 获取表格内部的滚动容器
  const imiBody = imiTableRef.value?.querySelector(".el-scrollbar__wrap");
  const originBody = originTableRef.value?.querySelector(".el-scrollbar__wrap");

  if (!imiBody || !originBody) return;
  console.log("得到滚动容器", imiBody, originBody);

  // 为两个滚动容器添加滚动事件
  const handleScroll = (e) => {
    console.log("滚动事件触发了", e.target);
    if (isScrolling) return;
    isScrolling = true;

    const source = e.target;
    const target = source === imiBody ? originBody : imiBody;

    // 同步垂直和水平滚动位置
    target.scrollTop = source.scrollTop;
    target.scrollLeft = source.scrollLeft;

    // 使用requestAnimationFrame优化性能
    window.requestAnimationFrame(() => {
      isScrolling = false;
    });
  };

  // 添加滚动事件监听
  imiBody.addEventListener("scroll", handleScroll, { passive: true });
  originBody.addEventListener("scroll", handleScroll, { passive: true });
  // 返回清理函数
  return () => {
    imiBody.removeEventListener("scroll", handleScroll);
    originBody.removeEventListener("scroll", handleScroll);
  };
};

onMounted(() => {
  console.log(route.query);
  console.log(route.params);
  currentPage.value = Number(route.query.currentPage) || 0;
  total.value = Number(route.query.total) || 0;
  batchId.value = route.query.batchId || "1";
  originalArticleId.value = route.query.originalArticleId || "1";
  fetchArticleDetail(true);

  // 数据加载完成后，设置滚动同步
  nextTick(() => {
    const cleanup = syncScroll();
    // 在组件卸载时清理
    onUnmounted(cleanup);
  });
});
</script>

<style scoped lang="scss">
@import "@/components/Business/style/base.scss";
.app-container {
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.page-btn-container {
  margin-left: 20px;
  flex: 1;
  display: flex;
  align-items: center;
  .page-btn {
    /* 正常状态 */
    color: #0078d4;
    border-color: #dbdbdb;
    font-size: 14px;
    font-weight: normal;
    height: 32px;
    border-radius: 0;
    &:hover {
      cursor: pointer;
    }

    /* 禁用状态 */
    &.is-disabled,
    &.is-disabled:hover,
    &.is-disabled:active {
      background-color: #e7ebf5;
      color: #999898;
      border-color: #dbdbdb;
      cursor: not-allowed;
    }
  }
  span {
    font-family: MicrosoftYaHei;
    font-size: 14px;
    color: #000000;
    line-height: 30px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin: 0 10px;
  }
}

.page-info {
  display: inline-block;
  margin: 0 10px;
  font-size: 14px;
  color: #606266;
  min-width: 60px;
  text-align: center;
}
.compare-content {
  height: calc(100vh - 37px - 44px);
  display: flex;
  background: #fff;
  overflow: hidden;
  border: 1px solid #bccde0;
  .compare-table-section,
  .xml-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .table-title {
      height: 40px;
      width: 100%;
      padding: 0 20px;
      user-select: none;
      span {
        display: inline-block;
        height: 100%;
        line-height: 40px;
        font-family: MicrosoftYaHei;
        font-size: 14px;
        color: #0078d4;
        text-align: left;
        font-style: normal;
        text-transform: none;
        border-bottom: 2px solid #0078d4;
      }
    }
    .compare-table-scroll {
      flex: 1;
      padding: 10px;
      background: #eff4fa;
      overflow: hidden;
      :deep(.el-table) {
        height: 100%;
        .el-table__body-wrapper {
          overflow-x: auto;
          overflow-y: auto;
          // el-table滚动条样式
          .el-scrollbar {
            // 纵向滚动条
            .el-scrollbar__bar.is-vertical {
              opacity: 1;
              width: 8px; // 纵向滑块的宽度
              border-radius: 2px;
              background: #ededed;
              .el-scrollbar__thumb {
                opacity: 1;
                background: #dcdcdc;
                border-radius: 4px;
              }
            }
          }
        }

        .el-table__header {
          th {
            height: 42px;
            background-color: #f7f7f7;
            font-family: MicrosoftYaHei;
            font-weight: bold;
            font-size: 14px;
            color: #000000;
            line-height: 26px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }

        .el-table__row {
          transition: background-color 0.2s ease;
          td {
            height: 42px;
            font-family: MicrosoftYaHei;
            font-weight: normal;
            font-size: 14px;
            color: #000000;
            line-height: 26px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
          &:hover > td {
            background-color: #fafeff;
          }

          &.diff {
            td {
              background-color: #fff3e0;
              color: #d4380d;

              &:hover {
                background-color: #ffe7cc;
              }
            }
          }
        }
      }
    }

    .xml-content {
      flex: 1;
      overflow: auto;
      position: relative;
      background-color: #eff4fa;
      pre {
        margin: 0;
        padding: 16px;
        background: #fff;
        font-family: monospace;
        font-size: 13px;
        line-height: 1.5;
        color: #333;
        white-space: pre;
        overflow-x: auto;
      }

      :deep(.tree-view) {
        width: max-content;
        min-width: 100%;
        white-space: nowrap;
        font-family: Monaco, Menlo, "Courier New", monospace;
        padding: 8px;

        .tree-view-item {
          font-size: 13px;
          line-height: 1.5;
        }
      }
    }
  }
  .compare-table-section {
    border-right: 1px solid #bccde0;
  }
}
/* 让TreeView组件内部的内容使用我们自定义的滚动行为 */
:deep(.tree-view) {
  overflow: visible !important;
  height: auto !important;
  max-height: none !important;
  background-color: #eff4fa;
}

/* XML内容区域的滚动条样式 */
.xml-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.xml-content::-webkit-scrollbar-thumb {
  background: #dcdcdc;
  border-radius: 4px;
}

.xml-content::-webkit-scrollbar-track {
  background: #ededed;
}

.xml-content::-webkit-scrollbar-corner {
  background: #f5f5f5;
}
</style>
