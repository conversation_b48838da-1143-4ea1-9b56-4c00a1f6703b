<template>
  <div class="container">
    <div
        class="table-container"
        :style="{
        width: showForm ? 'calc(100% - ' + formMaxWidth + ')' : '100%',
      }"
    >
      <span class="mx-1" style="margin-bottom: 6px; font-size: 12px; display: inline-block;">全部数据共计{{ total }}条</span>
      <el-table
          :data="tableData"
          border
          stripe
          size="small"
          style="width: 100%"
          highlight-current-row
          @current-change="handleCurrentChange"
      >
        <!-- 根据showIndex属性控制序号列的显示 -->
        <el-table-column
            v-if="showIndex"
            type="index"
            label="序号"
            width="50"
            align="center"
            fixed="left"
        />
        <el-table-column
            v-for="column in columns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :fixed="column.fixed"
            show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="150">
          <template #default="scope">
            <!-- <el-button @click="handleClick(scope.row, true)" type="text" size="small"
            >查看</el-button> -->
            <el-button v-if="showEditBtn" @click="handleClick(scope.row)" type="text" size="small">编辑</el-button>
            <slot name="operation" :row="scope.row"></slot>
          </template>
        </el-table-column>
      </el-table>
      <slot name="pagination"></slot>
    </div>
    <transition name="el-fade-in-linear" v-if="showForm">
      <div class="form-container" :style="{ width: formMaxWidth }">
        <el-form
            v-if="isShowRightForm"
            ref="form"
            :model="formData"
            label-width="120px"
            size="small"
            class="custom-form"
            style="max-width: 500px"
        >
          <div style="text-align: right; margin-bottom: 20px;">
            <el-button type="primary" :disabled="readonly" @click="handleConfirm">确认</el-button>
            <el-button @click="handleCancel">取消</el-button>
          </div>
          <el-form-item
              v-for="(value, key) in filteredFormData"
              :key="key"
              :label="getLabel(key)"
          >
            <el-input
                v-model="formData[key]"
                :placeholder="`请输入${getLabel(key)}`"
                :disabled="localReadonly"
            ></el-input>
          </el-form-item>

          <template v-for="field in formConfig" :key="field.prop">
            <el-form-item
                v-if="checkFieldVisible(field)"
                :label="field.label"
                :prop="field.prop"
                :rules="field.rules"
            >
              <!-- 输入框 -->
              <el-input
                  v-if="field.type === 'input'"
                  v-model="formData[field.prop]"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                  :disabled="readonly || field.disabled"
                  :type="field.inputType || 'text'"
                  :clearable="field.clearable !== false"
              />

              <!-- 选择框 -->
              <el-select
                  v-else-if="field.type === 'select'"
                  v-model="formData[field.prop]"
                  :placeholder="field.placeholder || `请选择${field.label}`"
                  :disabled="readonly || field.disabled"
                  :clearable="field.clearable !== false"
              >
                <el-option
                    v-for="item in field.children ? field.children() : []"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
              </el-select>

              <!-- 文本域 -->
              <el-input
                  v-else-if="field.type === 'textarea'"
                  v-model="formData[field.prop]"
                  type="textarea"
                  :rows="field.rows || 3"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                  :disabled="readonly || field.disabled"
              />
            </el-form-item>
          </template>
        </el-form>
        <slot name="form"></slot>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  props: {
    columns: {
      type: Array,
      required: true,
    },
    tableData: {
      type: Array,
      required: true,
    },
    total: {
      type: Number,
      default: 0
    },
    formMaxWidth: {
      type: String,
      default: "50%",
    },
    showFields: {
      type: Array,
      default: () => [],
    },
    isShowRightForm: {
      type: Boolean,
      default: true,
    },
    // 添加控制序号列显示的属性
    showIndex: {
      type: Boolean,
      default: true
    },
    showEditBtn: {
      type: Boolean,
      default: true
    },
    formConfig: {
      type: Array,
      required: true,
      default: () => []
    },
  },
  watch: {
    readonly: {
      handler(newValue) {
        this.localReadonly = newValue;
      },
      deep: true
    },
  },
  methods: {
    handleClick(row, readonly=false) {
      console.log(row)
      this.formData = { ...row };
      this.showForm = true;
      this.localReadonly = readonly;
    },
    async handleConfirm() {
      await this.$refs.form.validate();
      this.$emit('submitForm', this.formData);
    },
    handleCancel() {
      this.showForm = false;
    },
    getLabel(key) {
      const column = this.columns.find((col) => col.prop === key);
      return column ? column.label : key;
    },
    handleCurrentChange(val) {
      this.currentRow = val
    },
    // 检查字段是否应该显示
    checkFieldVisible(field) {
      if (!field.showCondition) return true;

      try {
        // 使用 Function 构造器创建一个函数，该函数可以访问 formData
        const condition = new Function('formData', `return ${field.showCondition}`);
        return condition(this.formData);
      } catch (e) {
        console.error('Error evaluating field visibility condition:', e);
        return true;
      }
    },
  },
  data() {
    return {
      currentRow: '',
      showForm: false,
      formData: {},
      localReadonly: false,
    };
  },
  computed: {
    filteredFormData() {
      if (this.showFields.length === 0) {
        return this.formData;
      }
      return Object.keys(this.formData)
          .filter(key => this.showFields.includes(key))
          .reduce((obj, key) => {
            obj[key] = this.formData[key];
            return obj;
          }, {});
    },
    // 自定义颜色
    tableRowClassName(row, rowIndex) {
      console.log(row, rowIndex)
      if (rowIndex === 1) {
        return 'warning-row'
      } else if (rowIndex === 3) {
        return 'success-row'
      }
      return ''
    },
    formRules() {
      const rules = {};
      this.formConfig.forEach(field => {
        if (field.rules) {
          rules[field.prop] = field.rules;
        }
      });
      return rules;
    }
  },
};
</script>

<style scoped lang="scss">
.container {
  display: flex;
  gap: 20px;

  :deep(.el-table .el-button--small) {
    padding: 0;
  }
  :deep(.el-table--small) {
    padding: 0;
  }
  :deep(.el-table--small .el-table__cell) {
    padding: 0;
  }
  :deep(.el-table__header-wrapper th,
        .el-table__fixed-header-wrapper th) {
    height: auto !important;
  }
}
.table-container {
  transition: width 0.3s ease;
}
.form-container {
  flex-shrink: 0;
}
.custom-form {
  margin-top: 20px;
}
//自定义颜色
.el-table .warning-row {
  --el-table-tr-bg-color: red;
}
.el-table .success-row {
  --el-table-tr-bg-color: green;
}
</style>
