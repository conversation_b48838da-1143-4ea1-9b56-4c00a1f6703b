<template>
  <el-form :model="localQueryParams" ref="queryRef" :inline="true" size="small">
    <template v-for="(item, index) in formItems" :key="index">
      <el-form-item :label="item.label" :prop="item.prop">
        <component
            :is="item.component"
            v-model="localQueryParams[item.prop]"
            v-bind="item.props"
        >
          <el-option
              v-if="item.children"
              v-for="(opt, optIndex) in item.children()"
              :key="optIndex"
              :label="opt.label"
              :value="opt.value"
          />
        </component>
      </el-form-item>
    </template>
    <el-form-item>
      <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      <slot name="btn"></slot>
    </el-form-item>
  </el-form>
</template>

<script>
import { ref, watchEffect } from 'vue';

export default {
  name: 'FormSearch',
  props: {
    value: {
      type: Object,
      required: true
    },
    formItems: {
      type: Array,
      required: true
    },
    sys_normal_disable: {
      type: Array,
      required: true
    }
  },
  setup(props, { emit }) {
    const localQueryParams = ref({ ...props.value });

    // 使用 watchEffect 来动态监听 props.value 的变化
    watchEffect(() => {
      localQueryParams.value = { ...props.value };
    });

    const handleQuery = () => {
      emit('input', localQueryParams.value);
      emit('search', localQueryParams.value);
    };

    const resetQuery = () => {
      localQueryParams.value = { ...props.value };
      emit('reset');
    };

    return {
      localQueryParams,
      handleQuery,
      resetQuery
    };
  }
};
</script>

<style scoped>
/* 你可以在这里添加你的样式 */
</style>
