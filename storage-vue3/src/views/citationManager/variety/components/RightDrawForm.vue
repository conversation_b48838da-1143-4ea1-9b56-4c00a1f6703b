<template>
  <div class="form-container">
    <!-- 编辑侧边栏 -->
    <el-form
      v-if="props.type==='edit'"
      ref="ruleFormRef"
      :model="editFormData"
      :rules="rules"
      label-width="140px"
      size="small"
      label-position="left"
    >
      <!-- 品种ID：只读输入框 -->
      <el-form-item label="品种ID" prop="journalId">
        <el-input v-model="editFormData.journalId" disabled />
      </el-form-item>

      <!-- 品种名称：普通输入框 -->
      <el-form-item label="品种名称" prop="journalTitle">
        <el-input v-model="editFormData.journalTitle" placeholder="请输入品种名称" />
      </el-form-item>

      <el-form-item label="出版机构" prop="publisher">
        <el-input v-model="editFormData.publisher" placeholder="请输入出版机构" />
      </el-form-item>

      <el-form-item label="文献类型" prop="articleType">
        <el-select v-model="editFormData.articleType" placeholder="请选择文献类型" clearable>
          <el-option label="期刊" value="journal" />
          <el-option label="会议" value="conference" />
          <el-option label="专利" value="patent" />
          <el-option label="学位论文" value="thesis" />
        </el-select>
      </el-form-item>

      <el-form-item label="引文级别" prop="citationLevel">
        <el-select v-model="editFormData.citationLevel" placeholder="请选择级别" clearable>
          <el-option label="一级" :value="1" />
          <el-option label="二级" :value="2" />
          <el-option label="三级" :value="3" />
          <el-option label="无" :value="0" />
        </el-select>
      </el-form-item>

      <el-form-item label="已有引文最早年" prop="earliestYear">
        <el-date-picker
          v-model="editFormData.earliestYear"
          type="year"
          placeholder="选择最早年份"
          style="width: 100%"
          format="YYYY"
          value-format="YYYY"
        />
      </el-form-item>

      <el-form-item label="已有引文最新年" prop="latestYear">
        <el-date-picker
          v-model="editFormData.latestYear"
          type="year"
          placeholder="选择最新年份"
          style="width: 100%"
          format="YYYY"
          value-format="YYYY"
        />
      </el-form-item>
    </el-form>
    <!-- 级别管理侧边栏 -->
    <el-form
      v-if="props.type==='levelManage'"
      ref="ruleFormRef"
      :model="levelManageFormData"
      :rules="rules"
      label-width="140px"
      size="small"
      label-position="left"
    >
      <!-- 一级引文年份 -->
      <el-form-item label="一级引文" prop="level1Year">
        <el-input-number
          v-model="levelManageFormData.level1Year"
          :min="1900"
          :max="2100"
          :precision="0"
          controls-position="right"
          style="width: 100%"
          placeholder="请输入一级引文年份"
        />
      </el-form-item>

      <!-- 二级引文年份 -->
      <el-form-item label="二级引文" prop="level2Year">
        <el-input-number
          v-model="levelManageFormData.level2Year"
          :min="1900"
          :max="2100"
          :precision="0"
          controls-position="right"
          style="width: 100%"
          placeholder="请输入二级引文年份"
        />
      </el-form-item>

      <!-- 三级引文年份 -->
      <el-form-item label="三级引文" prop="level3Year">
        <el-input-number
          v-model="levelManageFormData.level3Year"
          :min="1900"
          :max="2100"
          :precision="0"
          controls-position="right"
          style="width: 100%"
          placeholder="请输入三级引文年份"
        />
      </el-form-item>
    </el-form>
    <!-- 上传文件 -->
    <el-form
      v-if="props.type==='upload'"
      ref="formRef"
      :model="uploadFormData"
      :rules="rules"
      label-width="140px"
      size="small"
      label-position="left"
    >
      <!-- 数据文件 -->
      <el-form-item label="数据文件" prop="file" class="el-form-item__label-126">
        <el-upload
          ref="uploadRef"
          v-model:file-list="fileList"
          :auto-upload="false"
          :limit="1"
          :on-change="handleChange"
          :on-exceed="() => ElMessage.warning('只能上传一个文件')"
          accept=".xlsx,.xls"
          style="width: 100%"
        >
          <el-button type="primary">选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">请上传 ≤ 20MB 的 xls / xlsx 文件</div>
          </template>
        </el-upload>
        <el-button type="success" :loading="uploading" @click="submitUpload">
          开始上传
        </el-button>
        <el-button @click="resetForUpload">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import {reactive, ref, computed, getCurrentInstance, nextTick} from "vue";
import {testConnectionApi} from "@/api/dataSource/dataSource";
import {
  importCitationJournal,
  updateCitationJournal
} from "@/api/citationManager/citationJournal";
import { ElMessage } from 'element-plus'
const {proxy} = getCurrentInstance();
//编辑
const emit = defineEmits(['submit', 'close'])
const props = defineProps({
  modal: {
    type: Object,
  },
  type:    { type: String, required: true }
});
/**字典*/
// const {sysDict, bizDict} = props.modal.dict
// const form = computed({
//   get() {
//     return props.modal.form;
//   },
//   set() {
//   }
// });

// 需要进行数组转换的字段
const arrayField = ref(['processFlow', 'analysisRuleId']);

const ruleFormRef = ref();
//文件上传相关
const formRef   = ref()
const uploadRef = ref()
const fileList  = ref([])          // el-upload 的 v-model
const uploading = ref(false)

const testConnectionFlag = ref(null)


// 编辑表单数据
const editFormData = reactive({
  journalId: '',          // 品种ID
  journalTitle: '',        // 品种名称
  citationLevel: 0,      // 引文级别
  earliestYear: '',  // 已有引文最早年
  latestYear: '',    // 已有引文最新年
  publisher: '',          // 出版机构
  articleType: '' ,           // 文献类型
})
const uploadFormData = reactive({
  //文件上传
  file: null,
})
const levelManageFormData = reactive({
  level1Year: 2000,
  level2Year: 2010,
  level3Year: 2020,
})
//
const rules = reactive({
  journalTitle: [
    { required: true, message: '品种名称不能为空', trigger: 'blur' }
  ],
  citationLevel: [
    { required: true, message: '请选择引文级别', trigger: 'change' }
  ],
  earliestYear: [
    { required: true, message: '请选择最早年份', trigger: 'change' }
  ],
  latestYear: [
    { required: true, message: '请选择最新年份', trigger: 'change' }
  ],
  publisher: [
    { required: true, message: '出版机构不能为空', trigger: 'blur' }
  ],
  articleType: [
    { required: true, message: '请选择文献类型', trigger: 'change' }
  ],

  level1Year: [{ required: true, message: '请输入一级引文年份', trigger: 'blur' }],
  level2Year: [{ required: true, message: '请输入二级引文年份', trigger: 'blur' }],
  level3Year: [{ required: true, message: '请输入三级引文年份', trigger: 'blur' }],
  //文件上传
  file: [{ required: true, message: '请选择数据文件', trigger: 'change' }],

})

// 处理数组字段转换为字符串（保存时使用）
const processArrayToString = (data) => {
  const processedData = {...data};
  if (arrayField.value) {
    arrayField.value.forEach(field => {
      if (processedData[field] && Array.isArray(processedData[field])) {
        console.log(`转换数组字段 ${field}:`, processedData[field], '-> ', processedData[field].join(','));
        // 过滤空值并转换为字符串
        processedData[field] = processedData[field]
            .filter(item => item && item.trim() !== '')
            .join(',');
      }
    });
  }
  return processedData;
};

// 处理字符串转换为数组（编辑时使用）
const processStringToArray = (data, readonly = false) => {
  if (arrayField.value && !readonly) {
    arrayField.value.forEach(field => {
      if (data[field] && typeof data[field] === 'string') {
        console.log(`转换字符串字段 ${field}:`, data[field], '-> ', data[field].split(','));
        // 处理空字符串的情况
        if (data[field].trim() === '') {
          data[field] = [];
        } else {
          data[field] = data[field].split(',').filter(item => item.trim() !== '');
        }
      }
    });
  }
};

// 获取表单数据
const getFormData = () => {
  // 返回处理后的数据（数组转为字符串）
  if(props.type=="edit"){
    return processArrayToString(editFormData);
  }else if(props.type=="upload"){
     return processArrayToString(uploadFormData);
  }
 
};

// 重置表单
const resetForm = () => {
  console.log('=== 重置表单开始 ===');
  console.log('重置前 ftpUser:', formData.ftpUser, 'ftpPwd:', formData.ftpPwd);

  // 先清除表单验证状态
  ruleFormRef.value?.clearValidate();

  // 重置表单数据为初始状态
  Object.assign(formData, {
    name: '',
    dataId: '',
    pubType: '',
    dataLevel: '',
    dataType: '',
    processFlow: [],
    harvestType: '',
    ftpUrl: '',
    ftpPort: '',
    ftpUser: '',
    ftpPwd: '',
    fileType: '',
    ifaceUrl: '',
    analysisRuleId: '',
    sourceType: '',
    describes: ''
  });

  console.log('重置后 ftpUser:', formData.ftpUser, 'ftpPwd:', formData.ftpPwd);

  // 强制清空表单字段值，确保输入框显示为空
  setTimeout(() => {
    ruleFormRef.value?.resetFields();

    // 额外确保用户名和密码字段为空
    formData.ftpUser = '';
    formData.ftpPwd = '';

    console.log('最终检查 ftpUser:', formData.ftpUser, 'ftpPwd:', formData.ftpPwd);
    console.log('=== 重置表单完成 ===');
  }, 100);
};

// 设置表单数据
const setFormData = (data) => {

  console.log('=== 设置表单数据 ===');
  console.log('传入数据:', data);

  // 深拷贝数据，避免修改原始对象
  const processedData = JSON.parse(JSON.stringify(data || {}));

  // 处理字符串转数组
  // processStringToArray(processedData);

  console.log('深拷贝:', processedData);

  // 设置到表单数据
  if(props.type=="edit"){
    if (processedData.earliestYear !== undefined) {
      processedData.earliestYear = String(processedData.earliestYear);
    }
    if (processedData.latestYear !== undefined) {
      processedData.latestYear = String(processedData.latestYear);
    }
    Object.assign(editFormData, processedData);
    // editFormData = { ...editFormData, ...processedData };
    console.log('=== 设置表单数据完成 ===',editFormData);
  }
  
  // console.log('=== 设置表单数据完成 ===');
};

// 表单验证
const validateForm = () => ruleFormRef.value.validate();
// const validateForm = () => {
//   return new Promise((resolve, reject) => {
//     ruleFormRef.value.validate((valid) => {
//       if (valid) {
//         resolve(true);
//       } else {
//         reject(false);
//       }
//     });
//   });
// };

// 专门用于新增时的完全重置
const resetForAdd = () => {
  console.log('=== 新增专用重置开始 ===');

  // 先清除表单验证状态
  ruleFormRef.value?.clearValidate();

  // 完全重置表单数据
  Object.keys(formData).forEach(key => {
    if (key === 'processFlow') {
      formData[key] = [];
    } else {
      formData[key] = '';
    }
  });

  // 强制更新响应式数据
  nextTick(() => {
    ruleFormRef.value?.resetFields();

    // 再次确保关键字段为空
    formData.ftpUser = '';
    formData.ftpPwd = '';
    formData.ftpUrl = '';
    formData.ftpPort = '';

    console.log('新增重置完成，所有字段:', JSON.stringify(formData, null, 2));
    console.log('=== 新增专用重置完成 ===');
  });
};
/* 文件变化时把文件塞进 form 并手动校验 */
function handleChange(uploadFile, uploadFiles) {
  // 1. 先清空旧数据
  fileList.value = []
  uploadFormData.file = null

  // 2. 没选文件直接返回
  if (!uploadFile?.raw) return

  const f = uploadFile.raw
  const fileSuffix = f.name.split('.')[1];
  const okExt = ['xls', 'xlsx', 'doc', 'docx', 'pdf', 'ppt', 'pptx', 'txt', 'rar', 'zip', 'png', 'jpg', 'jpeg', 'gif'].includes(fileSuffix)
  const okType = ['xls', 'xlsx'].includes(fileSuffix)
  const okSize = f.size / 1024 / 1024 < 20

  // 3. 格式或大小不合法
  if (!okType || !okSize) {
    ElMessage.warning(okType ? '文件 ≤ 20MB' : '仅支持 xls / xlsx')
    return
  }

  // 4. 同步到两边
  // fileList.value = [uploadFile]  // el-upload 展示
  fileList.value = uploadFiles
  uploadFormData.file = f                  // form.file 校验
  nextTick(() => formRef.value?.validateField('file'))
}

/* 真正上传 */
async function submitUpload() {
  await formRef.value.validate()
  uploading.value = true
  try {
    /* const fd = new FormData()
    if (uploadFormData.file) {
      fd.append('file', uploadFormData.file, uploadFormData.file.name)
    } */
    // fd.append('file', uploadFormData.file)
    await importCitationJournal(uploadFormData.file)
    ElMessage.success('上传成功')
    resetForUpload()
  } catch (e) {
    ElMessage.error(e?.response?.data?.msg || '上传失败')
  } finally {
    uploading.value = false
  }
}

/* 重置 */
function resetForUpload() {
  formRef.value.resetFields()
  fileList.value = []
  uploadFormData.file = null
}
// 暴露方法给父组件
defineExpose({
  getFormData,
  resetForm,
  resetForAdd,  // 新增专用重置方法
  setFormData,
  validateForm,
  levelManageFormData
});
watch(
  () => editFormData.earliestYear,
  (newVal) => {
    console.log(newVal);
    // if (!newVal) return;
    if (newVal) {
      const year = new  Date(newVal).getFullYear();
      if (year < 2000) {
        editFormData.citationLevel = 0;
      } else if (year >= 2000 && year < 2020) {
        editFormData.citationLevel = 1;
      } else if (year >= 2020 && year < 2025) {
        editFormData.citationLevel = 2;
      } else if (year >= 2025) {
        editFormData.citationLevel = 3;
      }
    }
  }
);
</script>

<style lang="scss" scoped>
.form-container {
  border-radius: 2px;
  border: 1px solid rgb(221, 229, 235);
  background-color: rgb(255, 255, 255);
  overflow: hidden;

  :deep(.el-form-item--small) {
    margin-bottom: 0;
  }

  :deep(.el-input) {
    width: 335px;
    height: 34px;
  }
  :deep(.el-select) {
    width: 335px;
    height: 34px;
  }
  :deep(.el-input__wrapper) {
    height: 32px;
  }

  :deep(.el-select--small .el-select__wrapper) {
    height: 32px;
  }

  :deep(.el-form-item__content) {
    padding-top: 6px !important;
    padding-left: 9px !important;
    padding-right: 6px !important;
    background-color: transparent;
  }

  // 确保标签区域背景色连续
  :deep(.el-form-item) {
    display: flex;

    .el-form-item__label {
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 0, 0);
      font-weight: bold;
      padding-left: 15px !important;
      padding-top: 6px !important;
      padding-right: 0 !important;
      height: 40px;
      line-height: 40px;
      background-color: #f7f7f7;
      width: 110px;
      flex-shrink: 0;

      em {
        font-style: normal;
        color: #eb3037;
      }
    }
  }
  :deep(.el-form-item__label-126 .el-form-item__label) {
    height: 126px !important;
    line-height: 126px !important;
  }
  // 确保middle-box不受表单样式影响
  .middle-box {
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
  }

  :deep(.el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label-wrap>.el-form-item__label:before),
  :deep(.el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label:before) {
    display: none;
  }

  // textarea项的特殊样式
  :deep(.textarea-item) {
    .el-form-item__label {
      height: 196px !important;
      align-items: flex-start;
      padding-top: 6px !important;
    }
  }
}

.middle-box {
  display: flex;
  flex-direction: row;

  .middle-box-left {
    width: 110px;
    background-color: #f7f7f7 !important;
    flex-shrink: 0;
  }

  .middle-box-none {
    height: 20px;
    flex: 1;
  }

  .middle-box-right {
    height: 80px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      border: 1px solid rgb(219, 219, 219);
      border-radius: 2px;
      background-color: rgb(255, 255, 255);
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 120, 212);
      padding: 3px 11px;
      cursor: pointer;
    }
  }
}

// 映射规则标签样式
.rule-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
  min-height: 32px;
  padding: 2px 0;

  .rule-tag {
    margin: 0;
    font-size: 12px;
    border-radius: 3px;
    background-color: #e8f4fd;
    border: 1px solid #b3d9f7;
    color: #1890ff;
    padding: 2px 8px;
    height: auto;
    line-height: 1.4;

    :deep(.el-tag__content) {
      line-height: 1.4;
    }

    &:hover {
      background-color: #d6f0ff;
    }
  }

  .no-data-text {
    color: #999;
    font-size: 13px;
    font-style: italic;
    padding: 6px 0;
  }
}
</style>
