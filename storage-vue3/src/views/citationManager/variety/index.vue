<template>
  <div class="app-container">
    <!-- 背景框框 -->
    <viewBody title="引文品种管理">
      <template #content>
        <!-- 使用FormSearch组件替换搜索菜单 -->
        <FormSearch
          ref="formSearchRef"
          v-model="queryParams"
          :formItems="formItems"
          @search="handleQuery"
          @reset="resetQuery"
        >
          <template #btn>
            <el-button
              type="primary"
              class="my_primary_btn"
              icon="Plus"
              @click="handleUpload()"
            >
              批量导入</el-button
            >
            <el-button
              type="primary"
              class="my_primary_btn"
              icon="Download"
              @click="handleExport()"
            >
              批量导出</el-button
            >
            <el-button
              type="success"
              class="my_primary_btn"
              @click="levelManage()"
            >
              级别管理</el-button
            >
          </template>
        </FormSearch>

        <!-- 使用TableForm组件替换原有表格 -->
        <TableForm
          ref="tableFormRef"
          :columns="columns"
          :tableData="dataSourceList"
          :total="total"
          :showIndex="true"
          :showOtherColumn="true"
          tableotherColumnLabel="操作"
          :isShowCount="true"
          :isShowSearchQuery="false"
        >
          <!-- 操作按钮插槽 -->
          <template #otherOperation="{ row }">
            <span class="operation-btn" @click.stop="handleUpdate(row)">
              编辑
            </span>
            <span class="operation-btn" @click.stop="handleDetail(row)">
              日志
            </span>
          </template>
          <!-- 分页插槽 -->
          <template #pagination>
            <MyPagination
              v-if="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </template>

          <!-- 抽屉插槽 -->
          <template #drawer>
            <MyDrawer ref="drawerRef">
              <template #header="{ titleId, titleClass }">
                <div :id="titleId" :class="titleClass" class="my_drawer_title">
                  <span class="active">{{ drawer.title }}</span>
                  <div class="my_drawer_title_right">
                    <span class="btn_add" @click="submitForm">保存</span>
                    <span class="btn_cancel" @click="cancel">取消</span>
                  </div>
                </div>
              </template>
              <template #form>
                <RightDrawForm ref="rightFormRef" :modal="form":type=drawer.type></RightDrawForm>
              </template>
            </MyDrawer>
          </template>
        </TableForm>
      </template>
    </viewBody>
  </div>
</template>

<script setup name="dataSource">
import {
  listCitationJournal,
  exportCitationJournal,
  importCitationJournal,
  updateCitationJournal,
  saveCitationLevel
} from "@/api/citationManager/citationJournal";
import { queryRuleBaseSelect } from "@/api/processingtool/ruleBase";
import { useRouter } from "vue-router";
import { nextTick } from "vue";
import dayjs from "dayjs";
import MyPagination from "@/components/Pagination/new.vue";
import { Pagination } from "@/components/Business";
import FormSearch from "@/views/citationManager/components/FormSearch.vue";
import TableForm from "@/views/citationManager/components/TableForm.vue";
import MyDrawer from "@/views/citationManager/components/MyDrawer.vue";
import RightDrawForm from "./components/RightDrawForm.vue";
import viewBody from "@/views/citationManager/components/viewBody.vue";
import { ElMessage } from "element-plus";
import { VARIETY_DATA,TOTAL } from "@/views/citationManager/components/citationData";

const { proxy } = getCurrentInstance();
const router = useRouter();

// 存储业务字典数据
const bizDict = reactive({
  ruleBaseSelectOptions: [],
});

const drawer = reactive({
  visible: false,
  title: '',
  type: ''
});
const drawerOpen = ref(false);
const drawerRef = ref(null)
const rightFormRef = ref(null)
const currentEditRow = ref(null)

const dataSourceList = ref(VARIETY_DATA);
const loading = ref(true);
const total = ref(0);
const tableFormRef = ref(null);
const formSearchRef = ref(null);

// 在 script setup 中添加表单引用
const datasourceform = ref(null);
//引文级别字典
const citationLevel = reactive([
  { label: "一级", value: 1 },
  { label: "二级", value: 2 },
  { label: "三级", value: 3 },
  { label: "无", value: 0 },
]);

// 搜索表单配置
const formItems = ref([
  {
    prop: "journalTitle",
    component: "el-input",
    props: {
      placeholder: "品种名称",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    prop: "publisher",
    component: "el-input",
    props: {
      placeholder: "出版机构",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    prop: "citationLevel",
    component: "el-select",
    props: {
      placeholder: "引文级别",
      clearable: true,
      style: { width: "200px" },
    },
    children: () =>
      citationLevel.map((item) => ({
        value: item.value,
        label: item.label,
      })),
  },
  {
    label: '创建时间',
    prop: 'createTimeRange', // 表单数据中临时存储日期数组的字段（对应 el-date-picker 的 v-model）
    component: 'el-date-picker', // 关键：指定为 el-date-picker 组件
    props: {
      type: 'daterange', // 指定为日期范围选择器
      placeholder: '请选择创建时间范围',
      clearable: true, // 可清空
      style: { width: '280px' }, // 宽度（根据你的布局调整）
      format: 'YYYY-MM-DD', // 显示格式（可选）
      valueFormat: 'YYYY-MM-DD', // 传递给后端的格式（可选，若不需要可省略）
    },
    // 日期变化时拆分日期到 start/end 字段
    onChange: (val) => {
      if (val) {
        // val 是 [开始日期, 结束日期] 数组（Date 对象或字符串，取决于 valueFormat）
        queryParams.value.startCreateTime = val[0]; 
        queryParams.value.endCreateTime = val[1];
      } else {
        // 清空时重置两个时间字段
        queryParams.value.startCreateTime = null;
        queryParams.value.endCreateTime = null;
      }
    }
  }
]);

// 表格列配置
const columns = ref([
  { prop: "journalId", label: "品种ID", width: "160" },
  { prop: "journalTitle", label: "品种名称", width: "200" },
  { prop: "publisher", label: "出版机构", width: "200" },
  { prop: "articleType", label: "文献类型", width: "120" },
  { prop: "citationLevel", label: "引文级别", width: "100" },
  { prop: "earliestYear", label: "已有引文最早年", width: "120" },
  { prop: "latestYear", label: "已有引文最新年", width: "120" },
  { prop: "updateTime", label: "更新时间", width: "180" },
]);
// 新增批次数据相关变量
const selectedTask = ref(null);
const batchQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  sourceId: null,
});

const data = reactive({
  form: {
    dataSource: {},
    cycleType: "daily", // 默认设置为每日收割
    cycleDate: null,
    cycleWeek: [1], // 默认设置为周一
    nextCycleStartDate: null, // 默认设置为明天
    nextCycleEndDate: null,
    nextCycleDate: null,
    cycleCron: null,
    ifaceUrl: null,
    ifaceParams: null,
    ftpUrl: null,
    ftpPort: null,
    ftpUser: null,
    ftpPwd: null,
    state: 1,
  },
  queryParams: {
    journalTitle:'', 
    publisher:'', 
    journalId:'', 
    citationLevel:'', 
    startCreateTime:'', 
    endCreateTime:'', 
    pageNum:1, 
    pageSize:10
  },
  rules: {
    cycleType: [
      { required: true, message: "收割频率不能为空", trigger: "change" },
    ],
    cycleDate: [
      { required: true, message: "收割时间不能为空", trigger: "change" },
    ],
    cycleWeek: [
      { required: true, message: "请选择执行星期", trigger: "change" },
    ],
    nextCycleStartDate: [
      { required: true, message: "收割起始时间不能为空", trigger: "change" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

async function getRuleBaseSelect() {
  // , ruleStatus: 'COMPLETED'
  const { data } = await queryRuleBaseSelect({ ruleType: "ANALYSIS" });
  console.log("获取解析规则字典信息", data);
  bizDict.ruleBaseSelectOptions = data;
}

/** 主表格序号方法 */
function indexMethod(index) {
  // 计算序号，考虑分页
  const pageSize = queryParams.value.pageSize;
  const pageNum = queryParams.value.pageNum;
  return (pageNum - 1) * pageSize + index + 1;
}

/** 批次表格序号方法 */
function batchIndexMethod(index) {
  // 计算序号，考虑分页
  const pageSize = batchQueryParams.value.pageSize;
  const pageNum = batchQueryParams.value.pageNum;
  return (pageNum - 1) * pageSize + index + 1;
}
// 处理分页事件
// const handlePagination = (params) => {
//   queryParams.value.pageNum = params.page;
//   queryParams.value.pageSize = params.limit;
//   getList();
// };
/** 查询岗位列表 */
function getList() {
  loading.value = true;
  if (queryParams.value.searchCreatTime) {
    queryParams.value.startCreateTime = queryParams.value.searchCreatTime[0] + " 00:00:00";
    queryParams.value.endCreateTime = queryParams.value.searchCreatTime[1] + " 23:59:59";
  }
  // total.value=TOTAL;
  // 调用后端接口，传递查询参数（包含分页和条件）
   listCitationJournal(queryParams.value)
     .then(res => {
       // 假设接口返回结构为 { code: 200, data: { list: [], total: 100 } }
       if (res.code === 200) {
        dataSourceList.value = res.data.records;
        dataSourceList.value.forEach(item => {
          item.citationLevel = item.citationLevel == 1 ? '一级' : item.citationLevel == 2 ? '二级' : item.citationLevel == 3 ? '三级' : '无';
        }); 
        total.value = res.data.total; 
       } else {
         ElMessage.error(res.message || '获取数据失败');
       }
     })
     .catch(err => {
       console.error('请求出错：', err);
       ElMessage.error('网络异常，请稍后重试');
     })
     .finally(() => {
       loading.value = false; // 关闭加载状态
     });
}

// 格式化日期时间
function formatDateTime(dateTime) {
  if (!dateTime) return "";

  // 处理字符串类型的日期时间
  if (typeof dateTime === "string") {
    // 替换T为空格，移除毫秒部分和时区信息
    return dateTime.replace("T", " ").replace(/\.\d+(\+|\-).*$/, "");
  }

  return dateTime;
}

// 获取字典标签
function getDictLabel(options, value) {
  if (!value || !options?.length) return "";
  return options.find((opt) => opt.value === value)?.label ?? value;
}
//---------------------------右侧抽屉信息-------------------

//批量导入
async function handleUpload() {
  console.log('=== 批量导入按钮点击 ===');
  // reset();
  drawer.title = '批量导入';
  drawer.type='upload'
  currentEditRow.value = null;

  // 打开抽屉
  drawerRef.value.openDrawer();
}
//批量导出
function handleExport() {
  console.log('=== 批量导出按钮点击 ===');
  const rawParams = queryParams.value;

 // 3. 过滤空值 + 排除分页字段（pageNum、pageSize）
  const validParams = Object.fromEntries(
    Object.entries(rawParams).filter(([key, value]) => {
      // 排除分页字段（无论是否有值）
      if (key === 'pageNum' || key === 'pageSize') return false;
      // 保留其他非空字段
      return value !== null && value !== undefined && value !== '';
    })
  );
  exportCitationJournal(validParams || {})
    .then(blob => {
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = '引文品种导出.xlsx'; // 或根据后端返回的文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    })
    .catch(err => {
      console.error('导出失败', err);
    });
}
//级别管理按钮
function levelManage() {
  console.log('=== 级别管理按钮点击 ===');
  // reset();
  drawer.title = '级别管理';
  drawer.type='levelManage'
  currentEditRow.value = null;

  // 打开抽屉
  drawerRef.value.openDrawer();

  // 使用新增专用的重置方法
  // setTimeout(() => {
  //   rightFormRef.value?.resetForAdd();
  //   console.log('=== 级别管理完成 ===');
  // }, 100);
}
/** 编辑按钮-打开抽屉 */
const handleUpdate = (row) => {
  // reset();
  drawer.type = 'edit';
  drawer.title = '编辑引文品种';
  // 深拷贝行数据，避免直接修改原始数据
  currentEditRow.value = JSON.parse(JSON.stringify(row));
  drawerRef.value.openDrawer();
  // drawerOpen.value = true;
 // 设置表单数据
  setTimeout(() => {
    rightFormRef.value?.setFormData(currentEditRow.value);
  }, 100);
};


/** 取消按钮 */
const cancel = () => {
  if (!drawerRef.value) return;

  try {
    // 先重置表单
    resetForm();
    // 关闭抽屉
    drawerRef.value.closeDrawer();
  } catch (error) {
    console.error("关闭抽屉失败:", error);
  }
};
/* 保存按钮点击 */
async function submitForm() {
  if(drawer.type==='edit'){
    console.log("编辑保存")
    try {
      console.log("1",rightFormRef.value)
      // 2. 子组件校验
      await rightFormRef.value.validateForm()
      console.log("2")
      // 3. 拿表单数据
      const data = rightFormRef.value.getFormData()
      console.log("获取的表单数据",data);

      // 4. 调接口
      await updateCitationJournal(data)
      getList();
      ElMessage.success('修改成功')
    } catch (e) {
      console.log(e)
      // 校验失败或接口报错
      ElMessage.error(e?.response?.data?.msg || '操作失败')
    }
  }else if(drawer.type=='levelManage'){
    console.log("级别管理保存",rightFormRef.value.levelManageFormData)
    // 取出子组件表单数据
    const { level1Year, level2Year, level3Year } = rightFormRef.value.levelManageFormData
    try {
      await saveCitationLevel({
        firstLevelYear: String(level1Year),
        secondLevelYear: String(level2Year),
        thirdLevelYear: String(level3Year)
      })
      ElMessage.success('保存成功')
    } catch (e) {
      ElMessage.error('保存失败：' + (e?.message || '未知错误'))
    }
  } 
}

/** 行点击事件 */
const handleRowClick = async (row) => {
  try {
    // 先设置选中任务
    selectedTask.value = row;

    // 重置批次查询参数
    batchQueryParams.value = {
      pageNum: 1,
      pageSize: 10,
      sourceId: row.dataId,
    };

    // 等待DOM更新完成
    await nextTick();

    // 获取批次数据
    await getBatchListById();
  } catch (error) {
    console.error("处理行点击事件失败:", error);
  }
};

/** 验证开始和结束时间 */
function validateDateRange() {
  if (form.value.nextCycleStartDate && form.value.nextCycleEndDate) {
    const startDate = new Date(form.value.nextCycleStartDate);
    const endDate = new Date(form.value.nextCycleEndDate);

    if (endDate <= startDate) {
      proxy.$modal.msgError("收割截止时间必须大于收割起始时间");
      form.value.nextCycleEndDate = null;
    }
  }
}


/** 重置表单 */
const resetForm = () => {
  if (datasourceform.value) {
    datasourceform.value.resetFields();
  }
  form.value = {
    dataSource: {
      name: "",
      dataId: "",
      dataType: undefined,
      harvestType: undefined,
      ifaceUrl: "",
      ifaceParams: "",
      ftpUrl: "",
      ftpPort: "",
      ftpUser: "",
      ftpPwd: "",
      sourceType: "",
      describes: "",
      analysisRuleId: undefined,
    },
    cycleType: "immediate",
    cycleDate: "",
    cycleWeek: [],
    nextCycleStartDate: "",
    nextCycleEndDate: "",
    nextCycleDate: "",
    cycleCron: "",
    state: 1,
  };
};

/** 搜索按钮操作 */
const handleQuery = (params) => {
  queryParams.value.pageNum = 1;
  Object.assign(queryParams.value, params)
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
   const initialParams = {
    journalTitle: '', 
    publisher: '', 
    journalId: '', 
    citationLevel: '', 
    startCreateTime: '', 
    endCreateTime: '', 
    pageNum: 1, 
    pageSize: 10
  };
  // 更新queryParams的值
  Object.assign(queryParams.value, initialParams);
  if (formSearchRef.value?.resetFields) {
    formSearchRef.value.resetFields()
  }
  getList();
};

/** 初始加载 */
getList();
// getRuleBaseSelect();
</script>
<style lang="scss" scoped>
// 引入 base.scss 样式文件
@import "../../source/components/base.scss";

.my_drawer_title {
  display: flex;
  justify-content: space-between;
}

.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
  }

  .btn_add {
    background-color: rgb(0, 120, 212);
  }
}

// 单独设置右侧抽屉的样式，防止搜索部分被遮挡。与设计图不同～
:deep(.drawer-content-new) {
  margin-top: 0;
  border-top: 1px solid #bccde0;
  max-height: calc(100vh - 85px);
}

.operation-btn {
  display: inline-block;
  margin: 0 5px;
  color: #0076d0 !important;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
}

.form-container {
  border-radius: 2px;
  border: 1px solid rgb(221, 229, 235);
  background-color: rgb(255, 255, 255);
  overflow: hidden;

  :deep(.el-form-item--small) {
    margin-bottom: 0;
  }

  :deep(.el-input) {
    width: 335px;
    height: 34px;
  }

  :deep(.el-select) {
    width: 335px;
    height: 34px;
  }

  :deep(.el-input__wrapper) {
    height: 32px;
  }

  :deep(.el-select--small .el-select__wrapper) {
    height: 32px;
  }

  :deep(.el-form-item__content) {
    padding-top: 6px !important;
    padding-left: 9px !important;
    padding-right: 6px !important;
    background-color: transparent;
  }

  :deep(.form-item-52 .el-form-item__label) {
    height: 52px !important;
    line-height: 52px !important;
  }

  :deep(.form-item-54 .el-form-item__label) {
    height: 54px !important;
    line-height: 54px !important;
  }

  :deep(.form-item-64 .el-form-item__label) {
    height: 64px !important;
    line-height: 64px !important;
  }

  // 确保标签区域背景色连续
  :deep(.el-form-item) {
    display: flex;

    .el-form-item__label {
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 0, 0);
      font-weight: bold;
      padding-left: 15px !important;
      padding-top: 6px !important;
      padding-right: 0 !important;
      height: 40px;
      line-height: 40px;
      background-color: #f7f7f7;
      width: 110px;
      flex-shrink: 0;

      em {
        font-style: normal;
        color: #eb3037;
      }
    }
  }

  // 确保middle-box不受表单样式影响
  .middle-box {
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
  }

  :deep(
      .el-form-item.is-required:not(.is-no-asterisk).asterisk-left
        > .el-form-item__label-wrap
        > .el-form-item__label:before
    ),
  :deep(
      .el-form-item.is-required:not(.is-no-asterisk).asterisk-left
        > .el-form-item__label:before
    ) {
    display: none;
  }

  // textarea项的特殊样式
  :deep(.textarea-item) {
    .el-form-item__label {
      height: 196px !important;
      align-items: flex-start;
      padding-top: 6px !important;
    }
  }
}

.middle-box {
  display: flex;
  flex-direction: row;

  .middle-box-left {
    width: 110px;
    background-color: #f7f7f7 !important;
    flex-shrink: 0;
  }

  .middle-box-none {
    height: 20px;
    flex: 1;
  }

  .middle-box-right {
    height: 80px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      border: 1px solid rgb(219, 219, 219);
      border-radius: 2px;
      background-color: rgb(255, 255, 255);
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 120, 212);
      padding: 3px 11px;
      cursor: pointer;
    }
  }
}
</style>
