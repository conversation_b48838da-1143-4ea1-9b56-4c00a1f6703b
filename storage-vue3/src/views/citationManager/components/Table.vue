<template>
  <el-table
    :data="tableData"
    border
    stripe
    size="small"
    style="width: 100%;flex: 1;"
    highlight-current-row
    @cell-click="handleCellClick"
    @current-change="handleCurrentChange"
    @selection-change="handleSelectionChange"
  >
    <el-table-column v-if="isShowSelection" type="selection" align="center" width="70" label="全选" />
    <el-table-column
      v-if="showIndex"
      type="index"
      label="序号"
      :width="sortableItems.includes('index') ? 90 : 50"
      align="center"
      fixed="left"
      :sortable="sortableItems.includes('index')"
      prop="index"
    />
    <template v-for="column in columns" :key="column.prop">
      <template v-if="column.type">
        <el-table-column 
          :prop="column.prop" 
          :label="column.label" 
          :width="column.width" 
          :minWidth="column.minWidth"
          :sortable="sortableItems.includes(column.prop)">
          <template #default="scope">
            <template v-if="column.type === 'switch'">
              <!-- <el-switch
                size="default"
                v-model="scope.row[column.prop]"
                inline-prompt
                :active-text="column.activeText"
                :inactive-text="column.inactiveText"
              /> -->
              <el-switch v-model="scope.row[column.prop]" class="my_switch">
                <template #active-action>
                  <span class="custom-active-action">{{column.activeText}}</span>
                </template>
                <template #inactive-action>
                  <span class="custom-inactive-action">{{column.inactiveText}}</span>
                </template>
              </el-switch>
            </template>
            <template v-else-if="column.type === 'textCircle'">
              <template v-if="column['textCircle']">
                <span class="textCircle" v-if="scope.row[column.prop]" :class="{ 'green-circle': scope.row[column.prop] == column['textCircle']['success'], 
                                                   'red-circle': scope.row[column.prop] == column['textCircle']['failed'],
                                                   'yellow-circle': scope.row[column.prop] == column['textCircle']['warning'] }"><em></em>{{ scope.row[column.prop] }}</span>
              </template>
              <template v-else>
                <span class="textCircle" v-if="scope.row[column.prop]" :class="{ 'red-circle': scope.row[column.prop] == scope.row['textCircleFailedVal'] }"><em></em>{{ scope.row[column.prop] }}</span>
              </template>
            </template>
            <template v-else>
              <span :style="column.style">{{ scope.row[column.prop] }}</span>
            </template>
          </template>
        </el-table-column>
      </template>
      <template v-else-if="column.style">
        <el-table-column 
          :prop="column.prop" 
          :label="column.label" 
          :width="column.width" 
          :minWidth="column.minWidth"
          :sortable="sortableItems.includes(column.prop)">
          <template #default="scope">
            <span :style="column.style">{{ scope.row[column.prop] }}</span>
          </template>
        </el-table-column>
      </template>
      <template v-else>
        <el-table-column
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :minWidth="column.minWidth"
          :fixed="column.fixed"
          show-overflow-tooltip
          :sortable="sortableItems.includes(column.prop)"
        >
        </el-table-column>
      </template>
    </template>

    <el-table-column 
      v-if="showEditBtn"
      fixed="right" label="操作" 
      :width="columnsRightWidth" 
      header-align="center" 
      align="left"
    >
      <template #default="scope">
        <el-button
          v-if="showEditBtn"
          @click="handleClick(scope.row, '编辑')"
          type="primary"
          link
          size="small"
          >编辑</el-button
        >
        <slot name="operation" :row="scope.row"></slot>
      </template>
    </el-table-column>
    <el-table-column
      v-if="showNoteColumn"
      fixed="right" label="备注" 
      :width="columnsRightWidth" 
      header-align="center" >
      <template #default="scope">
        <slot name="operation" :row="scope.row"></slot>
      </template>
    </el-table-column>
    <el-table-column
      v-if="showOtherColumn"
      fixed="right" :label="tableotherColumnLabel" 
      :width="columnsRightWidth" 
      header-align="center"
    >
      <template #header>
        <slot name="header"></slot>
      </template>
      <template #default="scope">
        <slot name="otherOperation" :row="scope.row"></slot>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  props: {
    columns: {
      type: Array,
      required: true,
    },
    tableData: {
      type: Array,
      required: true,
    },
    showIndex: {
      type: Boolean,
      default: true,
    },
    showEditBtn: {
      type: Boolean,
      default: false,
    },
    columnsRightWidth: {
      type: String,
      default: "150",
    },
    sortableItems: {
      type: Array,
      default: () => [],
    },
    showNoteColumn: {
      type: Boolean,
      default: false,
    },
    isShowSelection: {
      type: Boolean,
      default: false,
    },
    showOtherColumn: {
      type: Boolean,
      default: false,
    },
    tableotherColumnLabel: {
      type: String,
      default: "其他",
    }
  },
  methods: {
    handleCellClick(row, column, cellValue, event) {
      this.$emit("cellClick", row, column, cellValue, event);
    },
    handleSelectionChange(val) {
      this.$emit("selectionChange", val);
    },
    handleCurrentChange(val) {
      this.$emit("currentChange", val);
    },
    handleClick (row, event, column) {
      this.$emit("click", row, event, column);
    },
  }
};
</script>

<style scoped lang="scss">
:deep(.el-table--small .cell) {
  font-size: 14px;
  font-family: "MicrosoftYaHei";
  color: rgb(32, 33, 33);
  // text-align: center;
}
:deep(.el-table--small th .cell) {
  font-size: 14px;
  font-family: "MicrosoftYaHei";
  color: rgb(0, 0, 0);
  font-weight: bold;
}

:deep(.el-table .el-button--small) {
  padding: 0;
  font-size: 14px;
  font-family: "MicrosoftYaHei";
  color: rgb(58, 141, 255);
  line-height: 2.143;
  text-align: center;
}
:deep(.el-table--small) {
  padding: 0;
}
:deep(.el-table--small .el-table__cell) {
  padding: 0;
}
:deep(.el-table .el-table__cell.is-right .cell) {
  text-align: right;
}
:deep(.el-table .el-table__cell.is-left .cell) {
  text-align: left;
}
:deep(
    .el-table--small .el-table__header-wrapper th,
    .el-table__fixed-header-wrapper th
  ) {
  height: auto !important;
  padding: 7px 0;
  background-color: #99ceff !important;
}
:deep(
    .el-table--striped
      .el-table__body
      tr.el-table__row--striped.current-row
      td.el-table__cell
  ) {
  background-color: #d7edff !important;
}
:deep(.el-table__body tr.current-row > td.el-table__cell) {
  background-color: #d7edff !important;
}
:deep(
    .el-table--striped
      .el-table__body
      tr.el-table__row--striped
      td.el-table__cell
  ) {
  background-color: #f7f7f7 !important;
}
:deep(
    .el-table.is-scrolling-middle
      .el-table-fixed-column--right.is-first-column:before
  ),
:deep(
    .el-table.is-scrolling-left
      .el-table-fixed-column--right.is-first-column:before
  ) {
  box-shadow: inset -10px 0 10px -10px rgba(0, 0, 0, 0.5);
}
:deep(
    .el-table.is-scrolling-right
      .el-table-fixed-column--left.is-last-column:before
  ),
:deep(
    .el-table.is-scrolling-middle
      .el-table-fixed-column--left.is-last-column:before
  ) {
  box-shadow: inset 10px 0 10px -10px rgba(0, 0, 0, 0.5);
}
:deep(.el-table__header .el-table-column--selection .cell .el-checkbox:after) {
  content: "全选";
  margin-left: 3px;
  font-size: 14px;
  font-family: "MicrosoftYaHei";
  color: #000;
  font-weight: bold;
}

// 修改switch 的样式。 start
:deep(.my_switch.el-switch--small .el-switch__core) {
  width: 50px;
  margin-left: 10px;
}
:deep(.my_switch.el-switch--small .el-switch__core .el-switch__action) {
  width: 42px;
  height: 20px;
  border-radius: 20px;
  border: 1px solid #d3d3d3;
}
:deep(.my_switch.el-switch--small.is-checked .el-switch__core .el-switch__action) {
  left: 13px;
}
:deep(.my_switch.el-switch--small .el-switch__core .el-switch__action) {
  left: -10px;
}
:deep(.my_switch.el-switch--small .el-switch__core .el-switch__action .custom-active-action) {
  font-size: 12px;
}
:deep(.my_switch.el-switch--small .el-switch__core .el-switch__action .custom-inactive-action) {
  color: #515151;
}
// 修改switch 的样式。 end
</style>
