<template>
  <div class="container">
    <div class="table-container">
      <span class="mx-1 table-container-count" v-show="isShowCount"><em></em>全部数据共计 <b>{{ total }}</b> 条</span>
      <!-- 检索条件 start -->
      <div class="table-container-search" v-show="isShowSearchQuery">
        检索条件：<span>篇级ID：FJ<em><svg 
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        width="9px" height="9px">
        <path fill-rule="evenodd"  fill="rgb(0, 120, 212)"
        d="M8.515,7.446 L5.560,4.405 L8.515,1.459 C8.801,1.173 8.801,0.698 8.515,0.413 C8.229,0.128 7.752,0.128 7.466,0.413 L4.607,3.455 L1.557,0.508 C1.271,0.223 0.795,0.223 0.414,0.508 C0.128,0.793 0.128,1.268 0.414,1.649 L3.368,4.595 L0.414,7.541 C0.128,7.827 0.128,8.302 0.414,8.587 C0.700,8.872 1.176,8.872 1.462,8.587 L4.417,5.640 L7.371,8.587 C7.657,8.872 8.134,8.872 8.515,8.587 C8.896,8.207 8.896,7.731 8.515,7.446 Z"/>
        </svg></em></span>
        <span>服务风险：高<em><svg 
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        width="9px" height="9px">
        <path fill-rule="evenodd"  fill="rgb(0, 120, 212)"
        d="M8.515,7.446 L5.560,4.405 L8.515,1.459 C8.801,1.173 8.801,0.698 8.515,0.413 C8.229,0.128 7.752,0.128 7.466,0.413 L4.607,3.455 L1.557,0.508 C1.271,0.223 0.795,0.223 0.414,0.508 C0.128,0.793 0.128,1.268 0.414,1.649 L3.368,4.595 L0.414,7.541 C0.128,7.827 0.128,8.302 0.414,8.587 C0.700,8.872 1.176,8.872 1.462,8.587 L4.417,5.640 L7.371,8.587 C7.657,8.872 8.134,8.872 8.515,8.587 C8.896,8.207 8.896,7.731 8.515,7.446 Z"/>
        </svg></em></span>
      </div>
      <!-- 检索条件 end -->
      <div class="table-inner-container" ref="tableInnerContainer">
        <Table
          :columns="columns"
          :table-data="tableData"
          :show-index="showIndex"
          :show-edit-btn="showEditBtn"
          :columns-right-width="columnsRightWidth"
          :sortable-items="sortableItems"
          :show-note-column="showNoteColumn"
          :is-show-selection="isShowSelection"
          :show-other-column="showOtherColumn"
          :tableother-column-label="tableotherColumnLabel"
          @cell-click="handleCellClick"
          @selection-change="handleSelectionChange"
          @current-change="handleCurrentChange"
          @handleClick="handleClick"
        >
          <template #operation="scope">
            <slot name="operation" :row="scope.row"></slot>
          </template>
          <template #header>
            <slot name="header"></slot>
          </template>
          <template #otherOperation="scope">
            <slot name="otherOperation" :row="scope.row"></slot>
          </template>
        </Table>
      </div>
      <div class="pagination-container-box">
        <slot name="pagination"></slot>
        <slot name="paginationRight"></slot>
      </div>
      <slot name="footerTable"></slot>
    </div>
    <slot name="drawer"></slot>
  </div>
</template>

<script>
import MyDrawer from './MyDrawer.vue';
import Table from './Table.vue'; 
export default {
  name: 'TableForm',
  components: {
    MyDrawer,
    Table
  },
  props: {
    columns: {
      type: Array,
      required: true,
    },
    tableData: {
      type: Array,
      required: true,
    },
    total: {
      type: Number,
      default: 0,
    },
    formMaxWidth: {
      type: String,
      default: "50%",
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    showFields: {
      type: Array,
      default: () => [],
    },
    isShowRightForm: {
      type: Boolean,
      default: true,
    },
    // 添加控制序号列显示的属性
    showIndex: {
      type: Boolean,
      default: true,
    },
    showEditBtn: {
      type: Boolean,
      default: false,
    },
    formConfig: {
      type: Array,
      required: false,
      default: () => [],
    },
    arrayField: {
      type: Array,
      required: false,
      default: () => [],
    },
    columnsRightWidth: {
      type: String,
      default: "150",
    },
    isShowCount: {
      type: Boolean,
      default: true,
    },
    isShowSearchQuery: {
      type: Boolean,
      default: true,
    },
    sortableItems: {
      type: Array,
      default: () => [],
    },
    showNoteColumn: {
      type: Boolean,
      default: false,
    },
    isShowSelection: {
      type: Boolean,
      default: false,
    },
    showOtherColumn: {
      type: Boolean,
      default: false,
    },
    tableotherColumnLabel: {
      type: String,
      default: "其他",
    },
    renderHeader: {
      type: Function,
      default: null,
    }
  },
  watch: {
    readonly: {
      handler(newValue) {
        this.localReadonly = newValue;
      },
      deep: true,
    },
  },
  methods: {
    // 单元格点击事件
    handleCellClick(row, column, cellValue, event) {
      this.$emit("cellClick", row, column, cellValue, event);
    },
    // 多选框选中事件
    handleSelectionChange(val) {
      this.$emit("selectionChange", val);
    },
    // 编辑事件
    handleClick(row) {
      this.$emit("handleClick", row);
    },
    // 处理表单提交
    handleSubmitForm(formData) {
      this.$emit("submitForm", formData);
    },
    // 获取字段的label
    getLabel(key) {
      const column = this.columns.find((col) => col.prop === key);
      return column ? column.label + "：" : key + "：";
    },
    handleCurrentChange(val) {
      this.currentRow = val;
    },
    // 检查字段是否应该显示
    checkFieldVisible(field) {
      if (!field.showCondition) return true;

      try {
        // 使用 Function 构造器创建一个函数，该函数可以访问 formData
        const condition = new Function(
          "formData",
          `return ${field.showCondition}`
        );
        return condition(this.formData);
      } catch (e) {
        console.error("Error evaluating field visibility condition:", e);
        return true;
      }
    },
  },
  data() {
    return {
      currentRow: "",
      localReadonly: false,
    };
  },
  beforeUnmount() {
    // 清理资源，避免在组件卸载时出现错误
    this.currentRow = null;
  },
  computed: {
    // 自定义颜色
    tableRowClassName(row, rowIndex) {
      console.log(row, rowIndex);
      if (rowIndex === 1) {
        return "warning-row";
      } else if (rowIndex === 3) {
        return "success-row";
      }
      return "";
    },
    formRules() {
      const rules = {};
      this.formConfig.forEach((field) => {
        if (field.rules) {
          rules[field.prop] = field.rules;
        }
      });
      return rules;
    },
  },
};
</script>

<style scoped lang="scss">
.container {
  display: flex;
  flex-direction: row;
  transition: width 0.3s;

  :deep(.drawer-content .el-drawer__title) {
    font-size: 16px;
    font-family: "MicrosoftYaHei";
    color: rgb(0, 120, 212);
  }

  :deep(.el-table--small .cell) {
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(32, 33, 33);
    // text-align: center;
  }
  :deep(.el-table--small th .cell) {
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(0, 0, 0);
    font-weight: bold;
  }

  :deep(.el-table .el-button--small) {
    padding: 0;
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(58, 141, 255);
    line-height: 2.143;
    text-align: center;
  }
  :deep(.el-table--small) {
    padding: 0;
  }
  :deep(.el-table--small .el-table__cell) {
    padding: 0;
  }
  :deep(.el-table .el-table__cell.is-right .cell) {
    text-align: right;
  }
  :deep(.el-table .el-table__cell.is-left .cell) {
    text-align: left;
  }
  :deep(
      .el-table--small .el-table__header-wrapper th,
      .el-table__fixed-header-wrapper th
    ) {
    height: auto !important;
    padding: 7px 0;
    background-color: #99ceff !important;
  }
  :deep(
      .el-table--striped
        .el-table__body
        tr.el-table__row--striped.current-row
        td.el-table__cell
    ) {
    background-color: #d7edff !important;
  }
  :deep(.el-table__body tr.current-row > td.el-table__cell) {
    background-color: #d7edff !important;
  }
  :deep(
      .el-table--striped
        .el-table__body
        tr.el-table__row--striped
        td.el-table__cell
    ) {
    background-color: #f7f7f7 !important;
  }
  :deep(
      .el-table.is-scrolling-middle
        .el-table-fixed-column--right.is-first-column:before
    ),
  :deep(
      .el-table.is-scrolling-left
        .el-table-fixed-column--right.is-first-column:before
    ) {
    box-shadow: inset -10px 0 10px -10px rgba(0, 0, 0, 0.5);
  }
  :deep(
      .el-table.is-scrolling-right
        .el-table-fixed-column--left.is-last-column:before
    ),
  :deep(
      .el-table.is-scrolling-middle
        .el-table-fixed-column--left.is-last-column:before
    ) {
    box-shadow: inset 10px 0 10px -10px rgba(0, 0, 0, 0.5);
  }
  :deep(.el-drawer__header) {
    padding: 14px 16px 10px 16px;
    margin: 0;
    border-bottom: 1px solid rgb(232, 232, 232);
  }
  :deep(.el-drawer__body) {
    padding: 0px;
  }
  :deep(.el-switch.is-checked .el-switch__core){
    background-color: #0078d4;
    border-color: #0078d4;
  }
  :deep(.el-switch__core){
    background-color: rgb(189, 197, 211);
    border-color: rgb(189, 197, 211);
    padding: 0 4px;
  }
  :deep(.el-switch.is-disabled) {
    opacity: 0.8;
    height: 100%;
    line-height: 100%;
    vertical-align: baseline;
  }
  :deep(.el-table__header .el-table-column--selection .cell .el-checkbox:after) {
    content: "全选";
    margin-left: 3px;
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: #000;
    font-weight: bold;
  }

  .table-container-search {
    margin-bottom: 5px;
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(0, 0, 0);
    padding: 0 10px;
    
    & > span {
      display: inline-block;
      height: 24px;
      line-height: 24px;
      border-radius: 4px;
      background-color: rgb(231, 235, 245);
      margin: 0 4px;
      padding: 0 8px;
    }

    & > span > em {
      cursor: pointer;
      margin-left: 6px;
    }
  }

}
.table-container {
  flex: 1;
  box-sizing: border-box;
  overflow: hidden;
  transition: width 0.3s ease;
  padding: 5px 0;
  // min-height: calc(100vh - 44px - 37px);
  height: calc(100vh - 44px - 37px);
  border-top: 1px solid #bccde0;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
}
.form-container {
  flex-shrink: 0;
}
//自定义颜色
.el-table .warning-row {
  --el-table-tr-bg-color: red;
}
.el-table .success-row {
  --el-table-tr-bg-color: green;
}

.table-container-count {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 6px;
  font-size: 14px;
  font-family: "MicrosoftYaHei";
  color: rgb(56, 56, 56);
  margin-bottom: 6px;
  margin-top: 3px;

  em {
    display: inline-block;
    width: 4px;
    height: 11px;
    background-color: rgb(53, 79, 156);
    border-radius: 4px;
    margin: 0 3px;
  }
  b {
    margin: 0 3px;
    font-size: 15px;
  }
}

:deep(.textCircle) {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;

  em {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 6px;
    background-color: rgb(63, 191, 0);
  }
  &.green-circle em {
    background-color: rgb(63, 191, 0);
  }
  &.red-circle em {
    background-color: rgb(224, 0, 0);
  }
  &.yellow-circle em {
    background-color: #f38200;
  }
}
.el-form-item {
  align-items: center;
}

.pagination-container-box {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

:deep(.footer-table) {
    border-top: 1px solid #bccde0;
    padding: 10px;
}
:deep(.pagination-right .white_btn) {
  border: 1px solid rgb(219, 219, 219);
  background-color: #fff;
  font-size: 14px;
  font-family: "MicrosoftYaHei";
  color: rgb(33, 146, 233);
}
:deep(.pagination-right) {
    margin-right: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
}

.table-inner-container {
  padding: 0 10px;
  flex: 1;
  overflow: auto;

  .el-table--small {
    height: 100%;
  }
}
</style>
