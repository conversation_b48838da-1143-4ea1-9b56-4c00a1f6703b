<template>
    <div class="view-body-encompass">
        <div class="view-body-tit" v-if="isShowTopCaption">
            <div>{{ title }}</div>
            <div><slot name="right"></slot></div>
        </div>
        <div class="view-body-content">
            <slot name="content"></slot>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    title: {
        type: String,
        default: '管理'
    },
    isShowTopCaption: {
        type: Boolean,
        default: false
    }
})
</script>

<style lang="scss" scoped>
.view-body-encompass {
    // border-bottom: 1px solid rgb(206, 209, 210);
    // background-color: rgb(255, 255, 255);
    // box-shadow: 0px 0px 29px 0px rgba(220, 226, 239, 0.21);
    // // border-radius: 8px;
    // margin-bottom: 10px;
}

.view-body-tit {
    width: 100%;
    height: 38px;
    line-height: 38px;
    padding: 0px 16px;
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(53, 79, 156);
    font-weight: bold;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    background-color: rgb(238, 245, 255);
    border-top: 3px solid #0078d4;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.el-form-item__label {
    padding: 0;
}
</style>