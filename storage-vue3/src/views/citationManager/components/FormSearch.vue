<template>
  <el-form :model="localQueryParams" ref="queryRef" :inline="true" size="default" label-width="0" class="form-query-box">
    <template v-for="(item, index) in formItems" :key="index">
      <template v-if="item.children">
        <el-form-item :label="item.label" :prop="item.prop" :class="item.position">
          <component
              :is="item.component"
              v-model="localQueryParams[item.prop]"
              v-bind="item.props"
          >
            <el-option
                v-for="(opt, optIndex) in item.children()"
                :key="optIndex"
                :label="opt.label"
                :value="opt.value"
            />
          </component>
        </el-form-item>
      </template>
      <template v-else>
        <el-form-item :label="item.label" :prop="item.prop" :class="item.position">
          <component
              :is="item.component"
              v-model="localQueryParams[item.prop]"
              v-bind="item.props"
          />
        </el-form-item>
      </template>
    </template>
    <slot name="innerform"></slot>
    <el-form-item>
      <el-button v-if="showSearchBtn" type="primary" icon="Search" class="my_primary_btn" @click="handleQuery">{{ searchBtnName }}</el-button>
      <el-button v-if="showResetBtn" @click="resetQuery" class="my_clean_btn">清空</el-button>
      <slot name="btn"></slot>
    </el-form-item>
  </el-form>
</template>

<script>
import { ref, watchEffect } from 'vue';

export default {
  name: 'FormSearch',
  emits: ['search', 'reset', 'reset-other-forms', 'update:modelValue'],
  props: {
    modelValue: {
      type: Object,
      required: true
    },
    formItems: {
      type: Array,
      required: false
    },
    sys_normal_disable: {
      type: Array,
      required: false
    },
    showSearchBtn: {
      type: Boolean,
      required: false,
      default: true
    },
    showResetBtn: {
      type: Boolean,
      required: false,
      default: true
    },
    searchBtnName: {
      type: String,
      required: false,
      default: '搜索'
    }
  },
  components: {
  },
  setup(props, { emit }) {
    const localQueryParams = ref({ ...props.modelValue });

    // 使用 watchEffect 来动态监听 props.modelValue 的变化
    watchEffect(() => {
      localQueryParams.value = { ...props.modelValue };
    });

    const handleQuery = () => {
      emit('update:modelValue', localQueryParams.value);
      emit('search', localQueryParams.value);
    };

    const resetQuery = () => {
      localQueryParams.value = { ...props.modelValue };
      emit('reset');
      emit('reset-other-forms');
    };
    const queryRef = ref();
    // 定义重置方法
    const resetFields = () => {
      queryRef.value?.resetFields()
    }

    return {
      localQueryParams,
      handleQuery,
      resetQuery,
      resetFields
    };
  }
};
</script>

<style scoped lang="scss">
/* 你可以在这里添加你的样式 */
.form-query-box {
  padding-left: 11px;
}
.el-form--inline .el-form-item {
  margin-bottom: 4px;
  margin-top: 4px;
  margin-right: 6px;
}
.my_primary_btn {
  background-color: rgb(0, 120, 212);
  border-color: rgb(0, 120, 212);
  padding: 0px 19px;
  border-radius: 0;
}
.my_clean_btn {
  background-color: rgb(189, 189, 189);
  font-size: 14px;
  font-family: "MicrosoftYaHei";
  color: rgb(255, 255, 255);
  border-radius: 0;

  &:hover { 
    border-color: #fff;
  }
}

.el-form--inline :deep(.left_wrapper) {
  margin-right: 0;
}
.el-form--inline :deep(.right_wrapper) {
  margin-right: 11px;
}
:deep(.left_wrapper .el-select__wrapper),
:deep(.left_wrapper .el-input__wrapper) {
  border: 1px solid #dcdfe6;
  border-right: 0;
  box-shadow: none;
  border-radius: 0;
  padding: 8px;
}

:deep(.right_wrapper .el-select__wrapper),
:deep(.right_wrapper .el-input__wrapper) {
  border: 1px solid #dcdfe6;
  box-shadow: none;
  border-radius: 0;
  padding-top: 0;
  padding-bottom: 0;
}

:deep(.left_wrapper .el-select__placeholder),
:deep(.right_wrapper .el-select__placeholder)
 {
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(1, 1, 1);
}



</style>
