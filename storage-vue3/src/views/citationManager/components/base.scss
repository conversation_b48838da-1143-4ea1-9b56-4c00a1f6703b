:deep(.el-form .el-form-item__label) {
  overflow: hidden;
  padding: 0;
}
:deep(.pagination-container) {
  justify-content: flex-start !important;
  padding: 10px 0;
  margin: 0;
}

:deep(.custom-form-item) {
  margin-bottom: 18px;

  .el-form-item__label {
    font-weight: normal;
    color: var(--el-text-color-regular);
    padding: 0 12px 0 0;
  }

  .custom-select {
    width: 100%;

    .el-input__wrapper {
      padding: 1px 11px;
    }

    .el-input__inner {
      height: 32px;
      line-height: 32px;
    }
  }

  .custom-textarea {
    .el-textarea__inner {
      padding: 5px 11px;
      min-height: 80px;
      font-size: 14px;
    }
  }
}
.my_primary_btn {
  background-color: #0078d4;
  border-color: #0078d4;
}

.view-body-top-right-btns {
    color: #0078d4;
    margin-left: 30px;
    border-bottom: 1px solid #0078d4;
}

:deep(.custom-form) {
  padding: 10px 4px;  

  .el-form-item__label {
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: #000;
    font-weight: bold;
  }
  .el-input--small .el-input__inner,
  .el-select--small .el-select__wrapper {
    height: 32px;
    line-height: 32px;
  }
  .el-button--small {
    padding: 14px 25px;
    border-radius: 4px;
  }
  .el-button--primary {
    background-color: #0078d4;
  }
  .el-input__wrapper {
    padding: 1px 15px;
  }
}
