/**
 * 引文管理数据配置
 * 包含表格列配置、字典数据和示例数据
 */
export const TOTAL=4;
// 1. 表格列配置
export const tableColumns = [
    { prop: 'name', label: '品种ID', width: '120' },
    { prop: 'dataType', label: '品种名称', width: '100' },
    { prop: 'state', label: '出版机构', width: '100' },
    { prop: 'harvestType', label: '文献类型', width: '120' },
    { prop: 'cycleType', label: '引文级别', width: '100' },
    { prop: 'lastState', label: '已有引文最早年', width: '140' },
    { prop: 'lastEndDate', label: '已有引文最新年', width: '180' },
    { prop: 'nextCycleDate', label: '更新时间', width: '180' }
  ];
  
  // 2. 字典数据
  export const DICTIONARIES = {
    // 文献类型
    DOCUMENT_TYPES: [
      { value: '1', label: '期刊论文' },
      { value: '2', label: '会议论文' },
      { value: '3', label: '学位论文' },
      { value: '4', label: '专利' },
      { value: '5', label: '标准' }
    ],
    
    // 引文级别
    CITATION_LEVELS: [
      { value: '1', label: '一级引文' },
      { value: '2', label: '二级引文' },
      { value: '3', label: '三级引文' },
      { value: '0', label: '无引文' }
    ],
    
    // 出版机构
    PUBLISHERS: [
      { value: '1', label: '科学出版社' },
      { value: '2', label: 'Elsevier' },
      { value: '3', label: 'Springer' },
      { value: '4', label: 'IEEE' }
    ]
  };
  
// 3. 示例数据（严格对齐调整后的 columns 字段）
export const SAMPLE_DATA = [
  {
    journalId: 'JS001',          // 品种ID（期刊唯一编码）
    journalTitle: '水稻遗传育种学报', // 品种名称（期刊全称）
    ISSN: '1001-8608',           // ISSN（国际标准连续出版物号）
    year: '2023',                // 年份（期刊出版年份）
    volume: '35',                // 卷号（期刊卷号）
    issue: '第6期',              // 期号（期刊期号）
    lastEndDate: '85',           // 待加工篇级数量（未处理的篇数）
    status: '待导入',            // 状态（任务状态）
    updateDate: '2024-03-15 14:30:00' // 更新时间（最近操作时间）
  },
  {
    journalId: 'JS002',
    journalTitle: '小麦研究学报',
    ISSN: '1003-1830',
    year: '2022',
    volume: '40',
    issue: '第4期',
    lastEndDate: '120',         // 待加工篇数较多
    status: '处理中',           // 正在处理中的任务
    updateDate: '2024-03-14 09:15:00'
  },
  {
    journalId: 'JS003',
    journalTitle: '玉米科学',
    ISSN: '1005-7208',
    year: '2024',
    volume: '38',
    issue: '第2期',
    lastEndDate: '0',           // 无待加工数据
    status: '已完成',           // 已完成的任务
    updateDate: '2024-03-13 16:45:00'
  },
  {
    journalId: 'JS004',
    journalTitle: '大豆科学',
    ISSN: '1000-9841',
    year: '2021',
    volume: '39',
    issue: '第5期',
    lastEndDate: '45',          // 部分待加工
    status: '异常',             // 任务异常（如数据冲突）
    updateDate: '2024-03-12 11:20:00'
  },
  {
    journalId: 'JS005',
    journalTitle: '棉花学报',
    ISSN: '1002-7807',
    year: '2023',
    volume: '36',
    issue: '第3期',
    lastEndDate: '60',          // 较多待加工
    status: '待导入',           // 等待导入的任务
    updateDate: '2024-03-11 08:50:00'
  },
  {
    journalId: 'JS006',
    journalTitle: '植物生理学报',
    ISSN: '0257-2829',
    year: '2024',
    volume: '42',
    issue: '第1期',
    lastEndDate: '10',          // 少量待加工
    status: '已完成',           // 已完成任务
    updateDate: '2024-03-10 17:00:00'
  }
];
  //参考数据
export const REFERENCE_DATA = [
  {
    title: 'Effect of Systemic Administration of Antioxidants on Obesity Markers',
    issn: '2769-819X',
    yearVolumeIssue: '2024/3/6',
    citedDocId: 20,
    journalTitle: 'Obesity Facts',
    citedCount: 307,
    pageRange: '26-34',
    updateTime: '2025-01.14 10:22:51'
  },
  {
    title: 'Identifying Risk Factors for Predominant Mental Health Disorders',
    issn: '2950-2853',
    yearVolumeIssue: '2024/3/6',
    citedDocId: 13,
    journalTitle: 'Spanish Journal of Psychiatry and Mental Health',
    citedCount: 235,
    pageRange: '42-49',
    updateTime: '2025-01.15 10:22:51'
  },
  {
    title: 'Metacognition in Psychosis: What and How',
    issn: '2950-2853',
    yearVolumeIssue: '2024/3/6',
    citedDocId: 10,
    journalTitle: 'Spanish Journal of Psychiatry and Mental Health',
    citedCount: 658,
    pageRange: '674-680',
    updateTime: '2025-02.03 10:22:51'
  },
  {
    title: 'Clonidine Reduces the Need for Antipsychotic Augmentation',
    issn: '1972-6007',
    yearVolumeIssue: '2024/3/6',
    citedDocId: 16,
    journalTitle: 'La Clinica Terapeutica',
    citedCount: 2126,
    pageRange: '32-38',
    updateTime: '2025-01.23 10:22:51'
  }
]
//回退数据
export const FALLBACK_DATA = ref([
  {
    articleId: "SJA01230718ajk9E0",
    articleTitle: "Effect of Systemic Administration of GLP-1 Receptor Agonist on Weight Loss in Obese Adults",
    journalTitle: "Obesity Facts",
    source: "成品库",
    status: "回退",
    rollbackNote: "数据重复上传，需回退后重新校验",
    rollbackTime: "2025-01-14 10:22:51",
    updateTime: "2025-01-14 10:22:51",
  },
  {
    articleId: "SJA01230718ajk9E1",
    articleTitle: "Identifying Risk Factors for Predominant Late-Onset Depression in Elderly Patients",
    journalTitle: "Spanish Journal of Psychiatry and Mental Health",
    source: "Kager",
    status: "删除",
    rollbackNote: "期刊信息填写错误，已删除",
    rollbackTime: "2025-01-15 10:22:51",
    updateTime: "2025-01-15 10:22:51",
  },
  {
    articleId: "SJA01230718ajk9E2",
    articleTitle: "Metacognition in Psychosis: What Antipsychotic Medications Can and Cannot Do",
    journalTitle: "Spanish Journal of Psychiatry",
    source: "welly",
    status: "回退",
    rollbackNote: "作者信息缺失，需回退补录",
    rollbackTime: "2025-02-03 10:22:51",
    updateTime: "2025-02-03 10:22:51",
  },
  {
    articleId: "FJA01230718ajk9E4",
    articleTitle: "Clonidine Reduces the Need for Antipsychotic Augmentation in Acute Mania: A Double-Blind RCT",
    journalTitle: "La Clinica Terapeutica",
    source: "成品库",
    status: "回退",
    rollbackNote: "试验注册号未提供，需回退补充",
    rollbackTime: "2025-01-23 10:22:51",
    updateTime: "2025-01-23 10:22:51",
  },
]);
export const SAMPLEDATA = ref([
  {
    serial: "01",
    taskName: "任务名称1",
    dbName: "成品库",
    docType: "期刊",
    docLevel: "篇级",
    checkObj: "全量数据",
    checkStatus: "处理中",
    dataCount: null,
    dealStatus: "待处理",
    operator: null,
    createTime: "2025-02-21 13:26:08",
    updateTime: "2025-02-21 13:26:08"
  },
  {
    serial: "02",
    taskName: "任务名称2",
    dbName: "单源库",
    docType: "期刊",
    docLevel: "品种",
    checkObj: "增量数据",
    checkStatus: "已完成",
    dataCount: 100,
    dealStatus: "待处理",
    operator: null,
    createTime: "2025-02-11 17:26:08",
    updateTime: "2025-02-21 13:26:08"
  },
  {
    serial: "03",
    taskName: "任务名称3",
    dbName: "原始库",
    docType: "期刊",
    docLevel: "篇级",
    checkObj: "增量数据",
    checkStatus: "已完成",
    dataCount: 50,
    dealStatus: "通过",
    operator: "加工员3",
    createTime: "2025-02-05 16:26:08",
    updateTime: "2025-02-21 13:26:08"
  },
  {
    serial: "04",
    taskName: "任务名称4",
    dbName: "全文库",
    docType: "期刊",
    docLevel: "篇级",
    checkObj: "全量数据",
    checkStatus: "已完成",
    dataCount: 200,
    dealStatus: "拒绝",
    operator: "加工员3",
    createTime: "2025-02-03 02:26:08",
    updateTime: "2025-02-21 13:26:08"
  },
  {
    serial: "05",
    taskName: "任务名称5",
    dbName: "引文库",
    docType: "期刊",
    docLevel: "篇级",
    checkObj: "增量数据",
    checkStatus: "已完成",
    dataCount: 600,
    dealStatus: "通过",
    operator: "加工员3",
    createTime: "2025-01-25 23:26:08",
    updateTime: "2025-02-21 13:26:08"
  }
]);
export const SAMPLEDETAIL = ref([
  {
    articleId: 'SJA01230718ajk9E0',
    articleTitle: 'Glycoproteins and Their Relationship with Renal Diseases',
    journalTitle: 'Cells Tissues Organs',
    issn: '1422-6405',
    yearVolIssue: '2023/3/8',
    attachStatus: 2,
    hasCitation: true,
    dataStatus: '正常',
    updateTime: '2025-02-20 13:26:08'
  },
  {
    articleId: 'SJA01230718ajk9E1',
    articleTitle: 'Identifying risk factors for predominant late-onset depression',
    journalTitle: 'JMIR Pediatrics and Parenting',
    issn: '1422-5909',
    yearVolIssue: '2024/3/4',
    attachStatus: 1,
    hasCitation: true,
    dataStatus: '正常',
    updateTime: '2025-02-10 17:26:08'
  },
  {
    articleId: 'SJA01230718ajk9E2',
    articleTitle: 'Metacognition in psychosis: What antipsychotic medications can and cannot do',
    journalTitle: 'JMIR Pediatrics and Parenting',
    issn: '1422-5909',
    yearVolIssue: '2024/3/4',
    attachStatus: 3,
    hasCitation: false,
    dataStatus: '通过',
    updateTime: '2025-02-05 10:26:08'
  },
  {
    articleId: 'SJA01230718ajk9E3',
    articleTitle: 'Clonidine reduces the need for antipsychotic augmentation in acute mania',
    journalTitle: 'JMIR Pediatrics and Parenting',
    issn: '1422-5909',
    yearVolIssue: '2024/3/4',
    attachStatus: 0,
    hasCitation: true,
    dataStatus: '拒绝',
    updateTime: '2025-02-01 02:26:08'
  },
  {
    articleId: 'SJA01230718ajk9E4',
    articleTitle: 'Effect of Systemic Administration of GLP-1 Receptor Agonist on Weight Loss',
    journalTitle: 'JMIR Pediatrics and Parenting',
    issn: '1422-5909',
    yearVolIssue: '2024/3/4',
    attachStatus: 7,
    hasCitation: true,
    dataStatus: '正常',
    updateTime: '2025-01-25 20:26:08'
  },
  // 其余 5 条数据同理，可继续复制或自动生成
]);
export const VARIETY_DATA = ref([
  {
    journalId: 1001, // 品种ID（数字）
    journalTitle: "人工智能与模式识别（期刊）", // 品种名称
    publisher: "清华大学出版社", // 出版机构
    articleType: "journal", // 文献类型（期刊）
    citationLevel: 1, // 引文级别（1→一级）
    earliestYear: 2020, // 已有引文最早年
    latestYear: 2023, // 已有引文最新年
    updateTime: "2024-03-15 14:30:22" // 更新时间
  },
  {
    journalId: 1002,
    journalTitle: "全球气候变化研讨会（会议）", // 会议类型
    publisher: "中国环境科学出版社",
    articleType: "conference", // 文献类型（会议）
    citationLevel: 2, // 引文级别（2→二级）
    earliestYear: 2019,
    latestYear: 2022,
    updateTime: "2024-02-28 09:15:47"
  },
  {
    journalId: 1003,
    journalTitle: "5G通信技术专利集（专利）", // 专利类型
    publisher: "国家知识产权局",
    articleType: "patent", // 文献类型（专利）
    citationLevel: 3, // 引文级别（3→三级）
    earliestYear: 2021,
    latestYear: 2024,
    updateTime: "2024-04-01 16:45:10"
  },
  {
    journalId: 1004,
    journalTitle: "高等教育心理学研究（学位论文）", // 学位论文
    publisher: "北京大学出版社",
    articleType: "thesis", // 文献类型（学位论文）
    citationLevel: 0, // 引文级别（0→无）
    earliestYear: 2018,
    latestYear: 2021,
    updateTime: "2023-12-10 11:20:33"
  },
  {
    journalId: 1005,
    journalTitle: "量子计算与物理进展（期刊）", // 期刊
    publisher: "物理学报",
    articleType: "journal", // 文献类型（期刊）
    citationLevel: 1, // 引文级别（一级）
    earliestYear: 2022,
    latestYear: 2024, // 最新年与更新时间同年（合理）
    updateTime: "2024-05-05 10:00:00"
  },
  {
    journalId: 1006,
    journalTitle: "生物医学工程前沿（会议）", // 会议
    publisher: "上海交通大学出版社",
    articleType: "conference", // 文献类型（会议）
    citationLevel: 2, // 引文级别（二级）
    earliestYear: 2020,
    latestYear: 2023,
    updateTime: "2024-01-18 17:30:55"
  },
  {
    journalId: 1007,
    journalTitle: "新能源材料专利汇编（专利）", // 专利
    publisher: "中国专利局",
    articleType: "patent", // 文献类型（专利）
    citationLevel: 3, // 引文级别（三级）
    earliestYear: 2019,
    latestYear: 2023,
    updateTime: "2024-03-22 13:10:15"
  },
  {
    journalId: 1008,
    journalTitle: "教育学博士论文选（学位论文）", // 学位论文
    publisher: "华东师范大学出版社",
    articleType: "thesis", // 文献类型（学位论文）
    citationLevel: 0, // 引文级别（无）
    earliestYear: 2017,
    latestYear: 2022,
    updateTime: "2023-11-05 08:45:28"
  },
  {
    journalId: 1009,
    journalTitle: "现代文学评论（期刊）", // 期刊
    publisher: "人民文学出版社",
    articleType: "journal", // 文献类型（期刊）
    citationLevel: 1, // 引文级别（一级）
    earliestYear: 2023,
    latestYear: 2024, // 最新年与更新时间同年
    updateTime: "2024-06-20 15:25:00"
  },
  {
    journalId: 1010,
    journalTitle: "机器人技术研讨会（会议）", // 会议
    publisher: "浙江大学出版社",
    articleType: "conference", // 文献类型（会议）
    citationLevel: 2, // 引文级别（二级）
    earliestYear: 2021,
    latestYear: 2023,
    updateTime: "2024-04-12 10:40:18"
  }
])

// 模拟表格数据（可直接用于 el-table 的 data 属性）
export const TASK_ARTICAL_DATA =ref(
  [
    {
      journalId:"JS001",
      articleId: "ART20240001", // 篇级ID（模拟唯一标识）
      articleTitle: "基于Transformer的多模态学术文献语义分析研究", // 篇级题名（注意字段名拼写）
      journalTitle: "计算机学报", // 期刊题名
      ISSN: "0254-4164", // ISSN（标准8位格式）
      "year/volumn/issue": "2024, Vol.47, No.3", // 年/卷/期（组合格式）
      number: 42, // 参考文献数（数值）
      updateTime: "2024-03-15 14:22:36" // 更新时间（标准时间戳格式）
    },
    {
      journalId:"JS001",
      articleId: "ART20240002",
      articleTitle: "深度学习在生物医学文献实体识别中的应用进展",
      journalTitle: "人工智能学报",
      ISSN: "2096-5036",
      "year/volumn/issue": "2024, Vol.36, No.2",
      number: 38,
      updateTime: "2024-04-02 09:17:12"
    },
    {
      journalId:"JS001",
      articleId: "ART20240003",
      articleTitle: "开放获取期刊的学术影响力评估指标体系构建",
      journalTitle: "出版发行研究",
      ISSN: "1001-9316",
      "year/volumn/issue": "2023, Vol.41, No.11",
      number: 56,
      updateTime: "2024-05-10 16:45:08"
    },
    {
      journalId:"JS001",
      articleId: "ART20240004",
      articleTitle: "预训练模型在科技论文摘要生成中的性能对比",
      journalTitle: "模式识别与人工智能",
      ISSN: "1003-6059",
      "year/volumn/issue": "2024, Vol.37, No.1",
      number: 61,
      updateTime: "2024-06-18 11:03:24"
    },
    {
      journalId:"JS001",
      articleId: "ART20240005",
      articleTitle: "学术文献知识图谱的构建方法与实证研究",
      journalTitle: "情报科学",
      ISSN: "1007-7634",
      "year/volumn/issue": "2023, Vol.41, No.9",
      number: 29,
      updateTime: "2024-07-01 18:20:51"
    }
  ]
) 
  // 模拟表格数据（用于测试或初始化）
export const FILE_UPLOAD_DATA = ref([
  {
    fusionIssueId:'0001',
    source: 'PubMed', // 来源库（常见数据库）
    rule: 'doi', // 关联规则（DOI 号）
    status: 2, // 任务状态（2: 已完成）
    fileName: 'PubMed_20250722_1430.zip', // 文件名称（含日期时间戳）
    fileSize: 2560, // 文件大小（kb，约 2.5MB）
    citationNoNum: 12, // 关联上无引文数量
    citationYesNum: 85, // 关联上有引文数量
    oneNum: 5, // 关联不上（无匹配）数量
    startTime: '2025-07-15 09:00:00', // 任务开始时间
    endTime: '2025-07-22 16:30:00' // 任务结束时间
  },
  {
    fusionIssueId:'0001',
    source: 'CNKI', // 来源库（中国知网）
    rule: 'articleId', // 关联规则（文章 ID）
    status: 1, // 任务状态（1: 进行中）
    fileName: 'CNKI_20250718_0925.xlsx', // 文件名称
    fileSize: 1890, // 文件大小（kb，约 1.9MB）
    citationNoNum: 8, // 关联上无引文数量
    citationYesNum: 120, // 关联上有引文数量
    oneNum: 3, // 关联不上数量
    startTime: '2025-07-18 10:15:00', // 任务开始时间
    endTime: '2025-07-25 14:45:00' // 任务结束时间（未完成）
  },
  {
    fusionIssueId:'0001',
    source: 'Web of Science', // 来源库（WoS）
    rule: 'journalId', // 关联规则（期刊 ID）
    status: 0, // 任务状态（0: 未开始）
    fileName: 'WoS_20250725_1612.zip', // 文件名称
    fileSize: 3200, // 文件大小（kb，约 3.2MB）
    citationNoNum: 5, // 关联上无引文数量
    citationYesNum: 65, // 关联上有引文数量
    oneNum: 10, // 关联不上数量
    startTime: '2025-07-25 14:00:00', // 任务开始时间（计划）
    endTime: '2025-08-05 18:00:00' // 任务结束时间（计划）
  },
  {
    fusionIssueId:'0001',
    source: 'Scopus', // 来源库（Scopus）
    rule: 'doi', // 关联规则（DOI 号）
    status: 1, // 任务状态（1: 进行中）
    fileName: 'Scopus_20250720_1105.xlsx', // 文件名称
    fileSize: 1540, // 文件大小（kb，约 1.5MB）
    citationNoNum: 15, // 关联上无引文数量
    citationYesNum: 90, // 关联上有引文数量
    oneNum: 7, // 关联不上数量
    startTime: '2025-07-20 08:30:00', // 任务开始时间
    endTime: '2025-07-28 12:15:00' // 任务结束时间
  },
  {
    fusionIssueId:'0001',
    source: '万方', // 来源库（万方数据）
    rule: 'articleId', // 关联规则（文章 ID）
    status: 2, // 任务状态（2: 已完成）
    fileName: '万方_20250728_1440.zip', // 文件名称
    fileSize: 2100, // 文件大小（kb，约 2.1MB）
    citationNoNum: 3, // 关联上无引文数量
    citationYesNum: 110, // 关联上有引文数量
    oneNum: 2, // 关联不上数量
    startTime: '2025-07-22 13:20:00', // 任务开始时间
    endTime: '2025-07-28 16:50:00' // 任务结束时间
  },
  {
    fusionIssueId:'0001',
    source: '维普', // 来源库（维普资讯）
    rule: 'journalId', // 关联规则（期刊 ID）
    status: 0, // 任务状态（0: 未开始）
    fileName: '维普_20250715_0830.xlsx', // 文件名称
    fileSize: 980, // 文件大小（kb，约 1.0MB）
    citationNoNum: 20, // 关联上无引文数量
    citationYesNum: 75, // 关联上有引文数量
    oneNum: 12, // 关联不上数量
    startTime: '2025-07-24 10:00:00', // 任务开始时间（计划）
    endTime: '2025-08-06 17:30:00' // 任务结束时间（计划）
  },
  {
    fusionIssueId:'0001',
    source: 'PubMed Central', // 来源库（PMC）
    rule: 'doi', // 关联规则（DOI 号）
    status: 1, // 任务状态（1: 进行中）
    fileName: 'PMC_20250723_1705.zip', // 文件名称
    fileSize: 4050, // 文件大小（kb，约 4.1MB）
    citationNoNum: 7, // 关联上无引文数量
    citationYesNum: 130, // 关联上有引文数量
    oneNum: 4, // 关联不上数量
    startTime: '2025-07-23 15:45:00', // 任务开始时间
    endTime: '2025-07-30 11:20:00' // 任务结束时间
  },
  {
    fusionIssueId:'0001',
    source: 'IEEE Xplore', // 来源库（IEEE）
    rule: 'articleId', // 关联规则（文章 ID）
    status: 2, // 任务状态（2: 已完成）
    fileName: 'IEEE_20250719_1015.xlsx', // 文件名称
    fileSize: 2800, // 文件大小（kb，约 2.8MB）
    citationNoNum: 10, // 关联上无引文数量
    citationYesNum: 105, // 关联上有引文数量
    oneNum: 6, // 关联不上数量
    startTime: '2025-07-19 09:30:00', // 任务开始时间
    endTime: '2025-07-26 14:55:00' // 任务结束时间
  },
  {
    fusionIssueId:'0001',
    source: 'ScienceDirect', // 来源库（ScienceDirect）
    rule: 'journalId', // 关联规则（期刊 ID）
    status: 0, // 任务状态（0: 未开始）
    fileName: 'SD_20250727_1320.zip', // 文件名称
    fileSize: 3500, // 文件大小（kb，约 3.5MB）
    citationNoNum: 4, // 关联上无引文数量
    citationYesNum: 80, // 关联上有引文数量
    oneNum: 15, // 关联不上数量
    startTime: '2025-07-29 11:00:00', // 任务开始时间（计划）
    endTime: '2025-08-12 15:00:00' // 任务结束时间（计划）
  },
  {
    fusionIssueId:'0001',
    source: 'Springer Link', // 来源库（Springer）
    rule: 'doi', // 关联规则（DOI 号）
    status: 1, // 任务状态（1: 进行中）
    fileName: 'SL_20250721_1545.xlsx', // 文件名称
    fileSize: 1950, // 文件大小（kb，约 2.0MB）
    citationNoNum: 18, // 关联上无引文数量
    citationYesNum: 180, // 关联上有引文数量
    oneNum: 1, // 关联不上数量（几乎全匹配）
    startTime: '2025-07-21 14:20:00', // 任务开始时间
    endTime: '2025-07-29 16:45:00' // 任务结束时间
  }
]);
  
  // 4. 默认导出
  export default {
    TOTAL,
    tableColumns,
    DICTIONARIES,
    SAMPLE_DATA
  };