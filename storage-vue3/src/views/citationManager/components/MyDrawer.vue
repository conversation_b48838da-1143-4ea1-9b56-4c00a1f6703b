<template>
  <!-- <el-drawer
    v-model="drawerVisible"
    :title="title"
    direction="rtl"
    :before-close="handleClose"
    :size="size"
    class="drawer-content"
  > -->
  <div v-show="drawerVisible" class="drawer-content-new">
      <!-- <template #header="{ close, titleId, titleClass }">
        <slot name="header" :close="close" :title-id="titleId" :title-class="titleClass"></slot>
      </template> -->
      <slot name="header"></slot>
      <div class="drawer-content-body">
        <el-scrollbar>
          <el-form
            v-if="isShowRightForm"
            ref="formRef"
            :model="formDataModel"
            :label-width="labelWidth"
            size="small"
            class="custom-form"
            :style="{ maxWidth: maxWidth }"
          >
            <div style="text-align: right; margin-bottom: 10px">
              <el-button type="primary" :disabled="readonly" @click="handleConfirm">确认</el-button>
              <el-button @click="handleCancel">取消</el-button>
            </div>
            <el-form-item
              v-for="(value, key) in filteredFormData"
              :key="key"
              :label="getLabel(key)"
            >
              <el-input
                v-model="formDataModel[key]"
                :placeholder="`请输入${getLabel(key)}`"
                :disabled="localReadonly"
              ></el-input>
            </el-form-item>

            <template v-for="field in formConfig" :key="field.prop">
              <el-form-item
                v-if="checkFieldVisible(field)"
                :label="field.label + '：'"
                :prop="field.prop"
                :rules="field.rules"
              >
                <!-- 输入框 -->
                <el-input
                  v-if="field.type === 'input'"
                  v-model="formDataModel[field.prop]"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                  :disabled="readonly || field.disabled"
                  :type="field.inputType || 'text'"
                  :clearable="field.clearable !== false"
                />

                <!-- 选择框 -->
                <el-select
                  v-else-if="field.type === 'select'"
                  v-model="formDataModel[field.prop]"
                  :multiple="field.multiple"
                  :placeholder="field.placeholder || `请选择${field.label}`"
                  :disabled="readonly || field.disabled"
                  :clearable="field.clearable !== false"
                >
                  <el-option
                    v-for="item in field.children ? field.children() : []"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>

                <!-- 文本域 -->
                <el-input
                  v-else-if="field.type === 'textarea'"
                  v-model="formDataModel[field.prop]"
                  type="textarea"
                  :rows="field.rows || 3"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                  :disabled="readonly || field.disabled"
                />
              </el-form-item>
            </template>        
          </el-form>
          <div class="drawer-form">
            <slot name="form"></slot>
          </div>
        </el-scrollbar>
        <span class="drawer-close" @click="handleCancel" v-show="showClose">
          <svg 
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
          width="14px" height="36px">
          <image  x="0px" y="0px" width="14px" height="36px"  xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAkCAMAAACOofuzAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAZlBMVEUAeNcTgtqZye8Sgdk3ld88l+C/3fVvs+gEetfT6PhXpuQchtvj8Pozk98sj93n8vski9xEm+Hb7PkUgtpgqubP5fcIfNh/u+u32PSXyO+byu+32fRDm+FfquY0k99YpuQ4ld////8FVq+6AAAAAWJLR0QhxGwNFgAAAAd0SU1FB+kGFBcQFgrbx3cAAABWSURBVCjPY2BAAoxMDCiAmWHIABYGBlYkLhs7BycXgsvNw8vHL4DgCwoJi4iKIfjiEgySUkhcaQYpBFdGSFgWoZibRw7ZKJBF8gidCmA0RAFqdDMyAQBIegM0UmhjDwAAAABJRU5ErkJggg==" />
          </svg>
        </span>
      </div>
      <slot name="footer"></slot>
  </div>
  <!-- </el-drawer> -->
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits, defineExpose } from 'vue';

const props = defineProps({
  // 表单标题
  title: {
    type: String,
    default: '编辑数据源'
  },
  // 表单宽度
  size: {
    type: [String, Number],
    default: '500'
  },
  // 表单最大宽度
  maxWidth: {
    type: String,
    default: '500px'
  },
  // 标签宽度
  labelWidth: {
    type: String,
    default: '120px'
  },
  // 是否只读
  readonly: {
    type: Boolean,
    default: false
  },
  // 显示的字段
  showFields: {
    type: Array,
    default: () => []
  },
  // 是否显示右侧表单
  isShowRightForm: {
    type: Boolean,
    default: false
  },
  // 表单配置
  formConfig: {
    type: Array,
    default: () => []
  },
  // 数组字段
  arrayField: {
    type: Array,
    default: () => []
  },
  // 列定义
  columns: {
    type: Array,
    default: () => []
  },
  // 显示侧边关闭按钮
  showClose: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:modelValue', 'submitForm', 'cancel']);

// 响应式数据
const drawerVisible = ref(false);
const formDataModel = ref({});
const localReadonly = ref(props.readonly);
const formRef = ref(null);

// 监听readonly属性变化
watch(() => props.readonly, (newValue) => {
  localReadonly.value = newValue;
}, { deep: true });

// 计算属性：过滤后的表单数据
const filteredFormData = computed(() => {
  if (props.showFields.length === 0) {
    return formDataModel.value;
  }
  return Object.keys(formDataModel.value)
    .filter((key) => props.showFields.includes(key))
    .reduce((obj, key) => {
      obj[key] = formDataModel.value[key];
      return obj;
    }, {});
});

// 方法：关闭抽屉
const handleClose = (done) => {
  done();
};

// 方法：取消
const handleCancel = () => {
  drawerVisible.value = false;
  emit('cancel');
};

// 方法：确认
const handleConfirm = async () => {
  try {
    if (formRef.value) {
      await formRef.value.validate();
    }
    
    // 处理数组字段
    const formDataToSubmit = { ...formDataModel.value };
    if (props.arrayField && props.arrayField.length > 0) {
      props.arrayField.forEach((field) => {
        if (formDataToSubmit[field] && Array.isArray(formDataToSubmit[field])) {
          formDataToSubmit[field] = formDataToSubmit[field].join(',');
        }
      });
    }
    
    emit('submitForm', formDataToSubmit);
    drawerVisible.value = false;
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 方法：获取字段的label
const getLabel = (key) => {
  const column = props.columns.find((col) => col.prop === key);
  return column ? column.label + '：' : key + '：';
};

// 方法：检查字段是否应该显示
const checkFieldVisible = (field) => {
  if (!field.showCondition) return true;

  try {
    // 使用 Function 构造器创建一个函数，该函数可以访问 formData
    const condition = new Function(
      'formData',
      `return ${field.showCondition}`
    );
    return condition(formDataModel.value);
  } catch (e) {
    console.error('Error evaluating field visibility condition:', e);
    return true;
  }
};

// 方法：打开抽屉并设置表单数据
const openDrawer = (rowData, isReadonly = false) => {
  // 复制行数据到表单数据
  formDataModel.value = { ...rowData };
  
  // 处理数组字段
  if (props.arrayField && props.arrayField.length > 0 && !isReadonly) {
    props.arrayField.forEach((field) => {
      if (formDataModel.value[field] && typeof formDataModel.value[field] === 'string') {
        formDataModel.value[field] = formDataModel.value[field].split(',');
      }
    });
  }
  
  // 设置只读状态
  localReadonly.value = isReadonly;
  
  // 显示抽屉
  drawerVisible.value = true;
};

// 方法：关闭抽屉
const closeDrawer = () => {
  drawerVisible.value = false;
};

// 暴露方法给父组件
defineExpose({
  openDrawer,
  closeDrawer,
  formData: formDataModel
});
</script>

<style scoped lang="scss">
:deep(.el-drawer__header) {
  padding: 14px 16px 10px 16px;
  margin: 0;
  border-bottom: 1px solid rgb(232, 232, 232);
}

:deep(.el-drawer__body) {
  padding: 0px;
}

:deep(.drawer-content .el-drawer__title) {
  font-size: 16px;
  font-family: "MicrosoftYaHei";
  color: rgb(0, 120, 212);
}

.drawer-content-body {
  height: calc(100% - 44px);
  background-color: #eff4fa;
  position: relative;
}

.drawer-close {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(20px);
  cursor: pointer;
  z-index: 99;
}

.drawer-content-new {
  width: 483px;
  max-height: calc(100vh - 37px);
  background: white;
  // box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1); 
  flex-shrink: 0;
  margin-top: -40px;
  border-left: 1px solid #bccde0;
}

.el-form-item {
  align-items: center;
}

.drawer-form {
  padding: 10px;
  position: relative;
}

:deep(.my_drawer_title) {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  font-family: "MicrosoftYaHei";
  color: rgb(102, 102, 102);
  display: flex;
  gap: 40px;
  padding-left: 20px;

  span {
    cursor: pointer;
  }

  .active {
    color: #0078d4;
    border-bottom: 2px solid #0078d4;
  }
}
</style>