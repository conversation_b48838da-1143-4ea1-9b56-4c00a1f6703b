<template>
  <div class="comparison-container">
    <!-- 左侧固定栏目 -->
    <div class="left-sidebar">
      <div class="field-header">请选择:</div>
      <div class="field-list">
        <div class="field-item" v-for="(item, index) in tableColumns" :key="item.name">
          <div 
            :class="`field-item-${index+1}`"
            >{{ item.name }}:</div>
        </div>
      </div>
    </div>

    <!-- 右侧滚动内容区域 -->
    <div class="right-content">
      <div class="content-wrapper">
        <!-- 数据列 -->
        <div class="data-columns">
          <div
            v-for="(item, index) in tableTextArr"
            :key="index"
            class="data-column"
            :class="{ selected: selectedItem === index }"
          >
            <!-- 选择按钮 -->
            <div class="selection-header">
              <el-radio
                v-model="selectedItem"
                :label="index"
                class="selection-radio"
              >
                第{{ index + 1 }}条
              </el-radio>
            </div>

            <!-- 数据内容 -->
            <div class="data-content">
              <template v-for="(itemCol, indexCol) in tableColumns" :key="itemCol.name">
                <div
                  class="data-item"
                  :class="`field-item-${indexCol+1}`"
                >
                  {{ item[itemCol.name] }}
                </div>
              </template>
            </div>
            
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from "vue";

const props = defineProps({
  tableColumns: {
    type: Object,
    default: () => [],
  },
  tableTextArr: {
    type: Object,
    default: () => [],
  },
});

const selectedItem = ref(1);

onMounted(() => {
  nextTick(() => {
    const _len = props.tableColumns.length;
    // 获取每一个 field-item-${index} 的内容高度，判断所有的 field-item-${index} 的最大值 ，将最大值赋值 给 field-item-${index}
    for (let i = 0; i < _len; i++) {
      const fieldItem = document.querySelectorAll(`.field-item-${i+1}`);
      let maxHeight = 0;
      fieldItem.forEach((item) => {
        const height = item.clientHeight;
        if (height > maxHeight) {
          maxHeight = height;
        }
      });
      console.log(maxHeight, i);
      if (fieldItem) {
        fieldItem.forEach((item) => {
          item.style.height = `${maxHeight}px`;
        });
      }
    }
  });
});

</script>

<style scoped>
:deep(.table-container) {
  display: none;
}
.comparison-container {
  display: flex;
  flex-direction: row;
  padding: 10px;
  background-color: #eff4fa;
}

/* 左侧固定栏目 */
.left-sidebar {
  width: 110px;
  background-color: #f7f7f7;
  border-right: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.field-header,
.field-item {
  font-size: 14px;
  background-color: #f7f7f7;
  font-family: "MicrosoftYaHei";
  color: rgb(0, 0, 0);
  font-weight: bold;
  box-sizing: border-box;
}
.field-header {
  border-bottom: 1px solid #dde4ec;
}
.field-item > div {
  padding: 10px 0 10px 14px;
  border-bottom: 1px solid #dde4ec;
}

.field-header {
  padding: 0;
  height: 40px;
  line-height: 40px;
  border-top: 1px solid #dde4ec;
  border-left: 1px solid #dde4ec;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 15px;
}

.field-list {
  border-left: 1px solid #dde4ec;
}

/* 右侧滚动内容区域 */
.right-content {
  flex: 1;
  overflow-x: auto;
}

.data-columns {
  display: flex;
}

.data-column {
  width: 340px;
  min-width: 340px;
  max-width: 340px;
  border-right: 1px solid #dde4ec;
}

.data-column,
.data-item {
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  word-break: break-word;
}

.data-item {
  padding: 10px 0 10px 14px;
  border-bottom: 1px solid #dde4ec;
}

.data-column.selected {
  border: 2px solid #0078d4;
}

.data-column.selected .selection-header,
.data-column.selected .data-item {
  background-color: #d7edff;
}

.selection-header {
  height: 40px;
  line-height: 40px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 15px;
  border-bottom: 1px solid #e4e7ed;
  border-top: 1px solid #dde4ec;
}

.selection-radio {
  font-weight: bold;
}

:deep(.el-radio__label) {
  color: #409eff;
  font-weight: bold;
  font-size: 14px !important;
  font-family: "MicrosoftYaHei";
}

:deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: #409eff;
  border-color: #409eff;
}

.data-content {
  flex: 1;
}
</style>
