<template>
  <div class="app-container">
    <div class="content-container">
      <!-- 内容区域 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="header-title">疑似数据处理</span>
            <el-button link type="primary" @click="toggleBasicInfo">
              {{ showBasicInfo ? '收起' : '展开' }}
              <el-icon class="el-icon--right">
                <component :is="showBasicInfo ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
            </el-button>
          </div>
        </template>
        <el-collapse-transition>
          <div class="top-row" v-show="showBasicInfo">
            <!-- 左侧 -->
            <div class="left-container">
                <div class="box-container-top-menu">
                    <div class="menu-container">
                        <span class="menu-title">待处理数据</span>
                        <span class="menu-title-btn menu-title-btn-gray">上一条</span>
                        <em class="menu-title-count">1/60</em>
                        <span class="menu-title-btn">下一条</span>
                    </div>
                    <div class="menu-container">
                        <div class="menu-container-icon">
                            <span class="active"><svg 
                                xmlns="http://www.w3.org/2000/svg"
                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                width="15px" height="16px">
                                <image  x="0px" y="0px" width="15px" height="16px"  xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAQCAMAAAD+iNU2AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAaVBMVEX///8AeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNQAeNT///8OUPGdAAAAIXRSTlMAi/DhFMD2sNMw4HAOEAVQgCh+kC3SS4ygMuQggrwsOwPdnBduAAAAAWJLR0QAiAUdSAAAAAd0SU1FB+kHARY0ArqMFoYAAABZSURBVAjXdc7XCoAwEETRtcSSWGLvJf//k7oiQwh63i7MwpLnW4KQRBRDYlISkkC9neWsQEvDSrTSrEK79+6+blj7u++e53r0MLLpez8vsN69GdtOh7ao8wKExwuaS1bqiwAAAABJRU5ErkJggg==" />
                                </svg>
                            </span>
                            <span>
                                <svg 
                                xmlns="http://www.w3.org/2000/svg"
                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                width="18px" height="14px">
                                <image  x="0px" y="0px" width="18px" height="14px"  xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAOCAMAAAAVBLyFAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAsVBMVEX///9obHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHhobHj///9Z97Z2AAAAOXRSTlMAw6o2aw8pk/sUAzXJ6AwF67GE+k79W01VFdPwB2z4muc5ZT14sgbB3wEJIIFvdnslLZ/axzdqHUBgxgkKAAAAAWJLR0QAiAUdSAAAAAd0SU1FB+kHARY0K/g+juoAAACHSURBVBjTXc9FEsMwDAXQn5QpBafglCllRt3/YpU9ajxjLaSvN1rYQFZBCK9ylHdLoWh6qVyp1v5Sb0RAs9VGR8Ui3R6PPg2gE2siCIcjiAU0treTqRl6puaIVaI5L2hpaEXrFGKb7S4Ta/sDHTmdRNjOuNCVw+2eutc+nv5nXvT26fON3PIDEGQLdepGWNgAAAAASUVORK5CYII=" />
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="box-container-content">
                    <LeftTable :tableTextArr="tableTextArr"></LeftTable>
                    <div class="box-container-content-footer">
                        <span class="btn">新增</span>
                        <span class="btn">挂接</span>
                        <span class="btn">删除</span>
                    </div>
                </div>
            </div>
            <!-- 右侧 -->
            <div class="right-container">
                <RightTable></RightTable>
            </div>
          </div>
        </el-collapse-transition>
      </el-card>
      <!-- 下侧 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="header-title">结构化数据</span>
            <el-button link type="primary" @click="toggleContentDetail">
              {{ showContentDetail ? '收起' : '展开' }}
              <el-icon class="el-icon--right">
                <component :is="showContentDetail ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
            </el-button>
          </div>
        </template>
        <el-collapse-transition>
          <viewBody title="疑似数据处理" v-show="showContentDetail">
            <template #content>
              <!-- 结构化数据 -->
              <TableForm
                ref="struTableFormRef"
                :columns="struColumns"
                :tableData="struDataSourceList"
                :total="total"
                :showIndex="false"
                :isShowCount="true"
                :isShowSearchQuery="false"
                :columnsRightWidth=120
              >
              </TableForm>
            </template>
          </viewBody>
        </el-collapse-transition>
      </el-card>
      
    </div>
    
  </div>
</template>

<script setup name="dataSource">
import {
  listSource,
  getSource,
  addSource,
  updateSource,
  updateState,
  getBatchList,
  downloadFromMinio,
  parseMappingBatch,
} from "@/api/task/task";
import { queryRuleBaseSelect } from "@/api/processingtool/ruleBase";
import { useRouter } from "vue-router";
import { nextTick } from "vue";
import dayjs from "dayjs";
import MyPagination from "@/components/Pagination/new.vue";
import FormSearch from "@/views/citationManager/components/FormSearch.vue";
import TableForm from "@/views/citationManager/components/TableForm.vue";
import MyDrawer from "@/views/citationManager/components/MyDrawer.vue";
import RightDrawForm from "./components/RightDrawForm.vue";
import viewBody from "@/views/citationManager/components/viewBody.vue";
import { ElMessage } from "element-plus";
import { DICTIONARIES, SAMPLE_DATA,TOTAL } from "@/views/citationManager/components/citationData";
import LeftTable from "./components/LeftTable.vue"
import RightTable from "./components/RightTable.vue"

const { proxy } = getCurrentInstance();
const router = useRouter();
// const { storage_data_type } = proxy.useDict("storage_data_type");
// const { storage_harvest_type } = proxy.useDict("storage_harvest_type");
// const { storage_harvest_cycle } = proxy.useDict("storage_harvest_cycle");
// const { storage_last_state } = proxy.useDict("storage_last_state");

// 存储业务字典数据
const bizDict = reactive({
  ruleBaseSelectOptions: [],
});
const selectData = ref({});
const susDataSourceList = ref(SAMPLE_DATA);
const struDataSourceList = ref(SAMPLE_DATA);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const susFormSearchRef = ref(null);
const susTableFormRef = ref(null);
const struTableFormRef = ref(null);
//抽屉
const drawerOpen = ref(false);
const drawerRef = ref(null)
const rightFormRef = ref(null)
const currentEditRow = ref(null)
const drawer = reactive({
  visible: false,
  title: '',
  type: ''
});
// 控制卡片展开收起状态
const showBasicInfo = ref(true);
const showContentDetail = ref(true);
// 在 script setup 中添加表单引用
const datasourceform = ref(null);
//引文级别字典
const AssociationStatus = reactive([
  { label: "全部", value: 1 },
  { label: "关联不上", value: 2 },
  { label: "正常", value: 3 },
  { label: "更新数据", value: 0 },
]);
const tableTextArr = ref([{
        leftText: 'Source ld：',
        righttext: 'AAN'
    }, {
        leftText: 'Source Title：',
        righttext: 'Cells Tissues Organs'
    }, {
        leftText: 'ISSN：',
        righttext: '1422-6405'
    }, {
        leftText: 'Publisher Name：',
        righttext: 'S. Karger AG',
        className: 'right-text-class' // 通过添加类名，改变样式
    }, {
        leftText: 'Pub Year：',
        righttext: '1998'
    }, {
        leftText: 'Volume：',
        righttext: '1'
    }, {
        leftText: 'Issue：',
        righttext: '1'
    }, {
        leftText: 'Start Page：',
        righttext: '1'
    }, {
        leftText: 'End Page：',
        righttext: '1'
    }, {
        leftText: 'DOI：',
        righttext: '10.1016/j.jbi.2019.01.004'
    }
])
// 疑似数据搜索表单配置
const susFormItems = ref([
  {
    prop: "name",
    component: "el-input",
    props: {
      placeholder: "篇级ID",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    prop: "name",
    component: "el-input",
    props: {
      placeholder: "篇级题名",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    prop: "name",
    component: "el-input",
    props: {
      placeholder: "ISSN",
      clearable: true,
      style: { width: "200px" },
    },
  },
]);

// 疑似数据表格列配置
const susColumns = ref([
  { prop: "name", label: "篇级ID", width: "180" },
  { prop: "dataType", label: "篇级题名", width: "200" },
  { prop: "state", label: "期刊题名", width: "200" },
  { prop: "harvestType", label: "ISSN", width: "120" },
  { prop: "cycleType", label: "年/卷/期", width: "100" },
  // { prop: "lastState", label: "参考文献数", width: "80" },
  // { prop: "lastEndDate", label: "更新时间", width: "200" },
]);
// 结构化搜索表单配置
const struFormItems = ref([
  {
    prop: "name",
    component: "el-input",
    props: {
      placeholder: "唯一标识",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    prop: "name",
    component: "el-input",
    props: {
      placeholder: "题名",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    prop: "name",
    component: "el-input",
    props: {
      placeholder: "期刊题名",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    prop: "dataType",
    component: "el-select",
    props: {
      placeholder: "关联状态",
      clearable: true,
      style: { width: "200px" },
    },
    children: () =>
      AssociationStatus.map((item) => ({
        value: item.value,
        label: item.label,
      })),
  },
]);

// 结构化表格列配置
const struColumns = ref([
  { prop: "name", label: "被引文献ID", width: "180" },
  { prop: "dataType", label: "作者", width: "150" },
  { prop: "state", label: "出版年", width: "120" },
  { prop: "harvestType", label: "文章标题", width: "200" },
  { prop: "harvestType", label: "期刊题名", width: "200" },
  { prop: "cycleType", label: "卷号/期号", width: "100" },
  { prop: "lastState", label: "页码范围", width: "120" },
  { prop: "lastEndDate", label: "更新时间", width: "200" },
]);
// 新增批次数据相关变量
const selectedTask = ref(null);
const batchList = ref([]);
const batchLoading = ref(false);
const batchTotal = ref(0);
const selectedBatchIds = ref([]); // 存储选中的批次ID
const batchQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  sourceId: null,
});

const data = reactive({
  form: {
    dataSource: {},
    cycleType: "daily", // 默认设置为每日收割
    cycleDate: null,
    cycleWeek: [1], // 默认设置为周一
    nextCycleStartDate: null, // 默认设置为明天
    nextCycleEndDate: null,
    nextCycleDate: null,
    cycleCron: null,
    ifaceUrl: null,
    ifaceParams: null,
    ftpUrl: null,
    ftpPort: null,
    ftpUser: null,
    ftpPwd: null,
    state: 1,
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    dataType: null,
    state: null,
    cycleType: null,
    harvestType: "1",
  },
  rules: {
    cycleType: [
      { required: true, message: "收割频率不能为空", trigger: "change" },
    ],
    cycleDate: [
      { required: true, message: "收割时间不能为空", trigger: "change" },
    ],
    cycleWeek: [
      { required: true, message: "请选择执行星期", trigger: "change" },
    ],
    nextCycleStartDate: [
      { required: true, message: "收割起始时间不能为空", trigger: "change" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);
// 修改打开抽屉的方法
function openDrawer(type, title) {
  drawer.type = type;
  drawer.title = title;
  drawerOpen.value = true;
  // 使用MyDrawer的openDrawer方法
  drawerRef.value.openDrawer();
}
/** 关闭抽屉 */
function closeDrawer() {
  drawer.visible = false;
  drawerOpen.value = false;
  reset();
}
// 切换展开/收起状态
const toggleBasicInfo = () => {
  showBasicInfo.value = !showBasicInfo.value;
};

const toggleContentDetail = () => {
  showContentDetail.value = !showContentDetail.value;
};
async function getRuleBaseSelect() {
  // , ruleStatus: 'COMPLETED'
  const { data } = await queryRuleBaseSelect({ ruleType: "ANALYSIS" });
  console.log("获取解析规则字典信息", data);
  bizDict.ruleBaseSelectOptions = data;
}
// 回显方法 - 关联状态
function getAssociationStatusLabel(value) {
  console.log(value);
  const statusMap = {
    0: "更新数据",
    1: "全部", 
    2: "关联不上",
    3: "正常"
  };
  
  // 如果有对应的映射值，则返回
  if (statusMap[value] !== undefined) {
    return statusMap[value];
  }
  
  // 也可以从你的 AssociationStatus 数组中查找
  const statusItem = AssociationStatus.find(item => item.value === value);
  if (statusItem) {
    return statusItem.label;
  }
  
  // 如果没有找到对应的值，则返回原值或默认值
  return value || '-';
}
/** 主表格序号方法 */
function indexMethod(index) {
  // 计算序号，考虑分页
  const pageSize = queryParams.value.pageSize;
  const pageNum = queryParams.value.pageNum;
  return (pageNum - 1) * pageSize + index + 1;
}

/** 批次表格序号方法 */
function batchIndexMethod(index) {
  // 计算序号，考虑分页
  const pageSize = batchQueryParams.value.pageSize;
  const pageNum = batchQueryParams.value.pageNum;
  return (pageNum - 1) * pageSize + index + 1;
}

/** 处理解析映射按钮点击 */
function handleAnalysisMapping() {
  // 检查是否有选中的行和批次数据
  if (!selectedTask.value) {
    proxy.$modal.msgWarning("请先选择一个数据源");
    return;
  }

  if (!batchList.value || batchList.value.length === 0) {
    proxy.$modal.msgWarning("没有可用的批次数据");
    return;
  }

  // 检查是否有选中的批次
  if (!selectedBatchIds.value || selectedBatchIds.value.length === 0) {
    proxy.$modal.msgWarning("请选择需要解析映射的批次");
    return;
  }

  // 显示确认对话框
  proxy.$modal
    .confirm(
      `确定要对选中的 ${selectedBatchIds.value.length} 个批次执行解析映射操作吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    )
    .then(() => {
      // 用户确认，调用解析映射接口
      proxy.$modal.loading("正在执行解析映射操作，请稍候...");

      parseMappingBatch(selectedBatchIds.value)
        .then((response) => {
          proxy.$modal.closeLoading();
          if (response.code === 200) {
            proxy.$modal.msgSuccess("解析映射操作成功");
            // 刷新批次数据
            getBatchListById();
          } else {
            proxy.$modal.msgError(`解析映射操作失败: ${response.msg}`);
          }
        })
        .catch((error) => {
          proxy.$modal.closeLoading();
          console.error("解析映射操作失败:", error);
          proxy.$modal.msgError("解析映射操作失败，请重试");
        });
    })
    .catch(() => {
      // 用户取消操作
    });
}
//详情按钮的回调
/** 查看按钮操作 */
function handleDetail(row) {
  // 跳转规则结果详情页
  console.log("row", row)
  router.push('/detail/${id}');
}
/** 跳转到收割任务列表页面 */
function goToTaskList() {
  router.push({ path: "/collection/batch" });
}

/** 跳转到解析映射列表页 */
function goToMappingList() {
  router.push({ path: "/obtainConvert/parsingMappingTask" });
}

/** 查询岗位列表 */
function getList() {
  loading.value = true;

  // 清空选中的任务和批次数据
  selectedTask.value = null;
  batchList.value = [];
  batchTotal.value = 0;
  total.value=TOTAL;

  // 使用 Promise 和 setTimeout 延迟请求，避免 DOM 操作冲突
  // new Promise((resolve) => {
  //   setTimeout(() => {
  //     resolve(listSource(queryParams.value));
  //   }, 100);
  // }).then(response => {
  //       // 处理数据，转换字典项和时间格式
  //       const formattedData = (response.rows || []).map(row => ({
  //         ...row,
  //         // 处理时间格式
  //         lastEndDate: formatDateTime(row.lastEndDate),
  //         nextCycleDate: formatDateTime(row.nextCycleDate),
  //         // 处理字典项
  //         dataType: getDictLabel(storage_data_type.value, row.dataType),
  //         harvestType: getDictLabel(storage_harvest_type.value, row.harvestType),
  //         cycleType: getDictLabel(storage_harvest_cycle.value, row.cycleType),
  //         lastState: getDictLabel(storage_last_state.value, row.lastState),
  //       }));

  //       // 使用 requestAnimationFrame 确保在下一帧渲染前更新数据
  //       requestAnimationFrame(() => {
  //         dataSourceList.value = formattedData;
  //         total.value = response.total || 0;
  //         loading.value = false;
  //       });
  //     }).catch(error => {
  //       console.error("获取数据失败:", error);
  //       loading.value = false;
  //       dataSourceList.value = [];
  //       total.value = 0;
  //     });
}

// 格式化日期时间
function formatDateTime(dateTime) {
  if (!dateTime) return "";

  // 处理字符串类型的日期时间
  if (typeof dateTime === "string") {
    // 替换T为空格，移除毫秒部分和时区信息
    return dateTime.replace("T", " ").replace(/\.\d+(\+|\-).*$/, "");
  }

  return dateTime;
}

// 获取字典标签
function getDictLabel(options, value) {
  if (!value || !options?.length) return "";
  return options.find((opt) => opt.value === value)?.label ?? value;
}

/** 打开抽屉 */
const handleUpdate = (row) => {
  if (!drawerRef.value) {
    ElMessage.error("抽屉组件未初始化");
    return;
  }

  title.value = row ? "修改收割任务" : "添加收割任务";
  resetForm();

  // 使用 nextTick 确保 DOM 已更新
  nextTick(() => {
    try {
      // 先打开抽屉
      drawerRef.value.openDrawer();

      // 打开后再获取数据
      setTimeout(() => {
        if (row) {
          const id = row.dataId;
          getTask(id);
        }
      }, 100);
    } catch (error) {
      console.error("打开抽屉失败:", error);
    }
  });
};

/** 取消按钮 */
const cancel = () => {
  if (!drawerRef.value) return;

  try {
    // 先重置表单
    resetForm();
    // 关闭抽屉
    drawerRef.value.closeDrawer();
  } catch (error) {
    console.error("关闭抽屉失败:", error);
  }
};

/** 表单提交 */
const submitForm = () => {
  proxy.$refs["datasourceform"].validate((valid) => {
    if (valid) {
      // 表单验证，确保每周收割时必须选择星期
      if (
        form.value.cycleType === "weekly" &&
        (!form.value.cycleWeek || form.value.cycleWeek.length === 0)
      ) {
        proxy.$modal.msgError("每周收割模式下必须选择星期");
        return;
      }

      // 表单验证，确保必须有cycleDate
      if (form.value.cycleType !== "immediate" && !form.value.cycleDate) {
        proxy.$modal.msgError("请设置收割时间");
        return;
      }

      // 强制重新生成cron表达式和计算下次执行时间
      generateCronExpression();
      calculateNextExecutionTime();

      // 处理周选择数据格式
      if (form.value.cycleWeek && Array.isArray(form.value.cycleWeek)) {
        form.value.cycleWeek = form.value.cycleWeek.join(";");
      }

      // 转换前端的cycleType格式为后端格式
      let backendCycleType;
      if (form.value.cycleType === "immediate") {
        backendCycleType = "0";
      } else if (form.value.cycleType === "daily") {
        backendCycleType = "1";
      } else if (form.value.cycleType === "weekly") {
        backendCycleType = "2";
      }

      // 创建提交数据对象
      const submitData = {
        ...form.value,
        cycleType: backendCycleType,
        cycleCron: form.value.cycleCron,
        // 处理特殊的下次执行时间显示文本
        nextCycleDate:
          form.value.nextCycleDate === "已超出收割截止时间" ||
          form.value.nextCycleDate === "任务已执行"
            ? null
            : form.value.nextCycleDate,
        // 确保dataId字段正确设置
        dataId: form.value.dataSource.dataId,
      };

      // 根据是否有id字段决定是新增还是修改
      if (form.value.id) {
        // 有id，执行修改操作
        updateSource(submitData)
          .then((response) => {
            proxy.$modal.msgSuccess("修改成功");
            cancel();
            getList();
          })
          .catch((error) => {
            console.error("修改失败:", error);
            proxy.$modal.msgError("修改失败，请重试");
          });
      } else {
        // 无id，执行新增操作
        addSource(submitData)
          .then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            cancel();
            getList();
          })
          .catch((error) => {
            console.error("新增失败:", error);
            proxy.$modal.msgError("新增失败，请重试");
          });
      }
    }
  });
};
// 当前选中项的索引
const currentIndex = ref(0);
/** 行点击事件 */
const handleRowClick = (row) => {
  drawer.title = '数据详情';
  console.log("选中的规则数据:", row);
  // open.value = true;
  drawerRef.value.openDrawer();
  selectData.value = row;
  // 更新当前索引
  currentIndex.value = ruleAnalysisList.value.findIndex(item => item.pathId === row.pathId);
};


// 处理上一条数据
function handlePrevItem() {
  if (currentIndex.value > 0) {
    currentIndex.value--;
    const prevRow = ruleAnalysisList.value[currentIndex.value];
    selectData.value = prevRow;
    // 更新表格选中状态
    proxy.$refs.ruleAnalysisTableRef.setCurrentRow(prevRow);
  }
}

// 处理下一条数据
function handleNextItem() {
  if (currentIndex.value < ruleAnalysisList.value.length - 1) {
    currentIndex.value++;
    const nextRow = ruleAnalysisList.value[currentIndex.value];
    selectData.value = nextRow;
    // 更新表格选中状态
    proxy.$refs.ruleAnalysisTableRef.setCurrentRow(nextRow);
  }
}
/** 获取批次列表 */
const getBatchListById = (page) => {
  if (!selectedTask.value) return;

  if (page) {
    batchQueryParams.value.pageNum = page.pageNum;
    batchQueryParams.value.pageSize = page.pageSize;
  }

  console.log("获取批次数据，参数:", batchQueryParams.value);
  batchLoading.value = true;

  // 使用 Promise 包装请求
  return new Promise((resolve) => {
    getBatchList(batchQueryParams.value)
      .then((response) => {
        console.log("获取批次数据成功:", response);

        // 处理数据，转换字典项和时间格式
        const formattedData = (response.rows || []).map((row) => ({
          ...row,
          // 处理时间格式
          createTime: formatDateTime(row.createTime),
          endTime: formatDateTime(row.endTime),
          // 处理字典项
          dataType: getDictLabel(storage_data_type.value, row.dataType),
          status: getDictLabel(storage_last_state.value, row.status),
          // 格式化文件大小
          fileSize: row.fileSize ? Number(row.fileSize).toFixed(2) : "0",
        }));

        batchList.value = formattedData;
        batchTotal.value = response.total;
        batchLoading.value = false;
        resolve(response);
      })
      .catch((error) => {
        console.error("获取批次列表失败:", error);
        batchLoading.value = false;
        batchList.value = [];
        batchTotal.value = 0;
        resolve(null);
      });
  });
};

/** 监听收割频率变化 */
watch(
  () => form.value.cycleType,
  (newVal) => {
    if (newVal) {
      // 如果没有设置起始时间，尝试设置默认值
      if (!form.value.nextCycleStartDate) {
        const tomorrow = dayjs().add(1, "day").format("YYYY-MM-DD 00:00:00");
        form.value.nextCycleStartDate = tomorrow;
      }
      generateCronExpression();
      calculateNextExecutionTime();
    }
  }
);

/** 监听cycleDate变化 */
watch(
  () => form.value.cycleDate,
  (newVal) => {
    if (newVal) {
      // 如果没有设置类型，默认使用每日收割
      if (!form.value.cycleType) {
        form.value.cycleType = "daily";
      }

      // 如果没有设置起始时间，尝试设置默认值
      if (!form.value.nextCycleStartDate) {
        const tomorrow = dayjs().add(1, "day").format("YYYY-MM-DD 00:00:00");
        form.value.nextCycleStartDate = tomorrow;
      }

      generateCronExpression();
      calculateNextExecutionTime();
    }
  }
);

/** 监听cycleWeek变化 */
watch(
  () => form.value.cycleWeek,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      // 如果更改了星期，自动设置为每周收割
      form.value.cycleType = "weekly";

      // 如果没有设置起始时间，尝试设置默认值
      if (!form.value.nextCycleStartDate) {
        const tomorrow = dayjs().add(1, "day").format("YYYY-MM-DD 00:00:00");
        form.value.nextCycleStartDate = tomorrow;
      }

      generateCronExpression();
      calculateNextExecutionTime();
    }
  },
  { deep: true }
);

/** 监听起始时间变化 */
watch(
  () => form.value.nextCycleStartDate,
  (newVal) => {
    if (newVal) {
      if (!form.value.cycleType) {
        form.value.cycleType = "daily"; // 默认为每日收割
      }
      generateCronExpression();
      calculateNextExecutionTime();
    }
  }
);

/** 监听截止时间变化 */
watch(
  () => form.value.nextCycleEndDate,
  (newVal) => {
    if (form.value.nextCycleStartDate && form.value.cycleType) {
      generateCronExpression();
      calculateNextExecutionTime();
    }
  }
);

/** 监听nextCycleStartDate和nextCycleEndDate变化进行日期验证 */
watch(
  [() => form.value.nextCycleStartDate, () => form.value.nextCycleEndDate],
  () => {
    validateDateRange();
  }
);

/** 验证开始和结束时间 */
function validateDateRange() {
  if (form.value.nextCycleStartDate && form.value.nextCycleEndDate) {
    const startDate = new Date(form.value.nextCycleStartDate);
    const endDate = new Date(form.value.nextCycleEndDate);

    if (endDate <= startDate) {
      proxy.$modal.msgError("收割截止时间必须大于收割起始时间");
      form.value.nextCycleEndDate = null;
    }
  }
}

/** 优化generateCronExpression函数，提高代码可读性，修复星期几的映射 */
function generateCronExpression() {
  if (!form.value.cycleType || !form.value.nextCycleStartDate) {
    form.value.cycleCron = "";
    return;
  }

  // 解析时间部分
  let seconds = 0,
    minutes = 0,
    hours = 0;
  if (form.value.cycleDate) {
    const parts = form.value.cycleDate.split(":");
    hours = parseInt(parts[0]);
    minutes = parseInt(parts[1]);
    seconds = parts.length > 2 ? parseInt(parts[2]) : 0;
  } else {
    const startDate = dayjs(form.value.nextCycleStartDate);
    hours = startDate.hour();
    minutes = startDate.minute();
    seconds = startDate.second();
  }

  // 根据收割类型生成不同的cron表达式
  switch (form.value.cycleType) {
    case "immediate":
      // 立即收割，只执行一次
      const startDate = dayjs(form.value.nextCycleStartDate);
      form.value.cycleCron = `${seconds} ${minutes} ${hours} ${startDate.date()} ${
        startDate.month() + 1
      } ? *`;
      break;

    case "daily":
      // 每日收割，指定时分秒，每天执行
      form.value.cycleCron = `${seconds} ${minutes} ${hours} * * ? *`;
      break;

    case "weekly":
      // 每周收割，指定星期几执行
      let weekdaysStr;
      if (form.value.cycleWeek && form.value.cycleWeek.length > 0) {
        // 将UI中的周几值转换为Quartz的周几值
        // UI: 周日=7, 周一=1, ..., 周六=6
        // Quartz: 周日=1, 周一=2, ..., 周六=7
        const quartzWeekdays = form.value.cycleWeek.map((day) => {
          // 如果是周日(7)，转为Quartz的周日(1)
          // 否则加1，将周一至周六(1-6)转为Quartz的周一至周六(2-7)
          return day === 7 ? 1 : day + 1;
        });
        weekdaysStr = quartzWeekdays.sort().join(",");
      } else {
        // 默认为星期一（Quartz的周一=2）
        weekdaysStr = "2";
      }

      form.value.cycleCron = `${seconds} ${minutes} ${hours} ? * ${weekdaysStr} *`;
      break;
  }
}

/** 计算下次任务执行时间 */
function calculateNextExecutionTime() {
  if (
    !form.value.cycleCron ||
    !form.value.nextCycleStartDate ||
    !form.value.cycleDate
  ) {
    form.value.nextCycleDate = null;
    return;
  }

  try {
    const startDate = dayjs(form.value.nextCycleStartDate);
    const endDate = form.value.nextCycleEndDate
      ? dayjs(form.value.nextCycleEndDate)
      : null;
    const now = dayjs();

    // 确保cycleTime是一个有效的时间对象
    let cycleTimeParts = form.value.cycleDate.split(":");
    let hours = parseInt(cycleTimeParts[0]);
    let minutes = parseInt(cycleTimeParts[1]);
    let seconds = cycleTimeParts.length > 2 ? parseInt(cycleTimeParts[2]) : 0;

    // 如果当前时间早于起始时间，下次执行时间判断需考虑收割频率
    if (now.isBefore(startDate)) {
      switch (form.value.cycleType) {
        case "immediate":
          // 立即收割，下次执行时间就是起始时间
          form.value.nextCycleDate = startDate.format("YYYY-MM-DD HH:mm:ss");
          return;

        case "daily":
          // 每日收割，下次执行时间是起始日期那天的收割时间
          const nextTime = startDate
            .hour(hours)
            .minute(minutes)
            .second(seconds);
          form.value.nextCycleDate = nextTime.format("YYYY-MM-DD HH:mm:ss");
          return;

        case "weekly":
          // 每周收割，需要找到起始日期后的第一个符合条件的星期
          const startDayOfWeek = startDate.day() === 0 ? 7 : startDate.day();
          const weekdays =
            form.value.cycleWeek && form.value.cycleWeek.length > 0
              ? [...form.value.cycleWeek].sort((a, b) => a - b)
              : [startDayOfWeek];

          // 获取下一个执行日
          let nextDayOfWeek = null;
          let daysToAdd = 0;

          // 先检查起始日期当天是否是执行日
          if (weekdays.includes(startDayOfWeek)) {
            nextDayOfWeek = startDayOfWeek;
            daysToAdd = 0;
          } else {
            // 找出起始日期之后的第一个执行日
            for (const day of weekdays) {
              if (day > startDayOfWeek) {
                nextDayOfWeek = day;
                daysToAdd = day - startDayOfWeek;
                break;
              }
            }

            // 如果没有找到，取下一周的第一个执行日
            if (nextDayOfWeek === null) {
              nextDayOfWeek = weekdays[0];
              daysToAdd = 7 - startDayOfWeek + nextDayOfWeek;
            }
          }

          // 计算下次执行时间
          const nextWeeklyTime = startDate
            .add(daysToAdd, "day")
            .hour(hours)
            .minute(minutes)
            .second(seconds);

          form.value.nextCycleDate = nextWeeklyTime.format(
            "YYYY-MM-DD HH:mm:ss"
          );
          return;
      }
    }

    // 如果有截止时间且当前时间已超过截止时间，则不再执行
    if (endDate && now.isAfter(endDate)) {
      form.value.nextCycleDate = "已超出收割截止时间";
      return;
    }

    // 基于当前时间和cron表达式计算下次执行时间
    // 这里根据不同的收割频率类型来计算
    let nextExecutionTime;

    switch (form.value.cycleType) {
      case "immediate":
        // 立即收割只执行一次
        if (now.isAfter(startDate)) {
          form.value.nextCycleDate = "任务已执行";
          return;
        } else {
          nextExecutionTime = startDate
            .hour(hours)
            .minute(minutes)
            .second(seconds);
        }
        break;

      case "daily":
        // 每日收割
        // 构建今天的执行时间点
        nextExecutionTime = now.hour(hours).minute(minutes).second(seconds);

        // 如果今天的执行时间已过，则设置为明天
        if (nextExecutionTime.isBefore(now)) {
          nextExecutionTime = nextExecutionTime.add(1, "day");
        }
        break;

      case "weekly":
        // 每周收割
        const weekdays =
          form.value.cycleWeek && form.value.cycleWeek.length > 0
            ? [...form.value.cycleWeek].sort((a, b) => a - b)
            : [startDate.day() === 0 ? 7 : startDate.day()];

        const currentDayOfWeek = now.day() === 0 ? 7 : now.day();

        // 找出下一个执行日
        let nextDayOfWeek = null;
        let daysToAdd = 0;

        // 今天的执行时间点
        const todayExecTime = now.hour(hours).minute(minutes).second(seconds);
        const todayIsExecutionDay = weekdays.includes(currentDayOfWeek);

        // 先检查今天是否是执行日，且时间未过
        if (todayIsExecutionDay && todayExecTime.isAfter(now)) {
          nextDayOfWeek = currentDayOfWeek;
          daysToAdd = 0;
        } else {
          // 检查本周剩余天数
          for (const day of weekdays) {
            if (day > currentDayOfWeek) {
              nextDayOfWeek = day;
              daysToAdd = day - currentDayOfWeek;
              break;
            }
          }

          // 如果本周没有合适的日期，取下周第一个执行日
          if (nextDayOfWeek === null) {
            nextDayOfWeek = weekdays[0];
            daysToAdd = 7 - currentDayOfWeek + nextDayOfWeek;
          }
        }

        nextExecutionTime = now
          .add(daysToAdd, "day")
          .hour(hours)
          .minute(minutes)
          .second(seconds);
        break;
    }

    // 检查是否在有效时间范围内
    if (endDate && nextExecutionTime.isAfter(endDate)) {
      form.value.nextCycleDate = "已超出收割截止时间";
    } else {
      form.value.nextCycleDate = nextExecutionTime.format(
        "YYYY-MM-DD HH:mm:ss"
      );
    }
  } catch (error) {
    console.error("计算下次执行时间出错:", error);
    form.value.nextCycleDate = null;
  }
}

/** 处理状态变更 */
function handleStateChange(row) {
  // 确保状态格式正确
  const oldState = row.state === "1" ? "2" : "1";

  // 检查是否已配置任务
  if (!row.id) {
    row.state = oldState;
    proxy.$modal.msgWarning("请先配置任务");
    return;
  }

  // 准备请求数据
  const data = {
    id: row.id,
    dataId: row.dataId,
    state: row.state,
  };

  // 调用后端API更新状态
  updateState(data)
    .then((response) => {
      proxy.$modal.msgSuccess(row.state === "1" ? "启用成功" : "停用成功");
      getList(); // 刷新列表，获取最新状态
    })
    .catch((error) => {
      // 操作失败，恢复之前的状态
      row.state = oldState;
      console.error("状态更新失败:", error);
      proxy.$modal.msgError("操作失败，请重试");
    });
}

/** 重置表单 */
const resetForm = () => {
  if (datasourceform.value) {
    datasourceform.value.resetFields();
  }
  form.value = {
    dataSource: {
      name: "",
      dataId: "",
      dataType: undefined,
      harvestType: undefined,
      ifaceUrl: "",
      ifaceParams: "",
      ftpUrl: "",
      ftpPort: "",
      ftpUser: "",
      ftpPwd: "",
      sourceType: "",
      describes: "",
      analysisRuleId: undefined,
    },
    cycleType: "immediate",
    cycleDate: "",
    cycleWeek: [],
    nextCycleStartDate: "",
    nextCycleEndDate: "",
    nextCycleDate: "",
    cycleCron: "",
    state: 1,
  };
};

/** 获取任务详情 */
const getTask = (id) => {
  return new Promise((resolve, reject) => {
    // 使用 setTimeout 延迟获取数据，避免 DOM 操作冲突
    setTimeout(() => {
      getSource(id)
        .then((response) => {
          if (response.code === 200) {
            try {
              // 填充表单数据
              form.value = response.data;

              // 确保dataSource字段存在
              if (!form.value.dataSource) {
                form.value.dataSource = {};
              }

              // 处理周期设置，转换后端格式为前端格式
              if (form.value.cycleType) {
                if (form.value.cycleType === "0") {
                  form.value.cycleType = "immediate";
                } else if (form.value.cycleType === "1") {
                  form.value.cycleType = "daily";
                } else if (form.value.cycleType === "2") {
                  form.value.cycleType = "weekly";
                }
              } else {
                // 如果没有设置cycleType，默认为每日收割
                form.value.cycleType = "daily";
              }

              // 如果没有cycleDate，设置默认值
              if (!form.value.cycleDate) {
                const now = new Date();
                form.value.cycleDate = `${now
                  .getHours()
                  .toString()
                  .padStart(2, "0")}:${now
                  .getMinutes()
                  .toString()
                  .padStart(2, "0")}:${now
                  .getSeconds()
                  .toString()
                  .padStart(2, "0")}`;
              }

              // 如果没有设置起始时间，设置默认值为明天
              if (!form.value.nextCycleStartDate) {
                form.value.nextCycleStartDate = dayjs()
                  .add(1, "day")
                  .format("YYYY-MM-DD 00:00:00");
              }

              // 处理周选择数据格式
              if (form.value.cycleWeek) {
                form.value.cycleWeek = form.value.cycleWeek
                  .split(";")
                  .map(Number);
              } else if (form.value.cycleType === "weekly") {
                // 如果是每周收割但没有设置具体星期，默认设置为星期一
                form.value.cycleWeek = [1];
              }

              // 处理状态数据格式
              if (form.value.state) {
                if (form.value.state === "1") {
                  form.value.state = 1;
                } else if (form.value.state === "2") {
                  form.value.state = 2;
                }
              }

              // 强制重新生成CRON表达式和计算下次执行时间
              generateCronExpression();
              calculateNextExecutionTime();

              resolve(response.data);
            } catch (error) {
              console.error("处理表单数据出错:", error);
              ElMessage.error("处理数据失败，请重试");
              reject(error);
            }
          } else {
            ElMessage.error(response.msg || "获取数据失败");
            reject(new Error(response.msg || "获取数据失败"));
          }
        })
        .catch((error) => {
          console.error("获取数据失败:", error);
          ElMessage.error("获取数据失败，请重试");
          reject(error);
        });
    }, 100);
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  if (queryRef.value) {
    queryRef.value.resetFields();
  }
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 批次表格多选框选中数据 */
const handleBatchSelectionChange = (selection) => {
  selectedBatchIds.value = selection.map((item) => item.batchId);
};

/** 下载批次文件 */
const downloadBatchFiles = (row) => {
  if (!row.localFileUri) {
    ElMessage.error("文件路径不存在，无法下载！");
    return;
  }

  const fileName =
    row.fileName || decodeURIComponent(row.localFileUri.split("/").pop());

  downloadFromMinio(row.localFileUri)
    .then((response) => {
      if (!response) {
        ElMessage.error("下载失败：无数据");
        return;
      }

      // 正常下载
      const blob = new Blob([response]);
      const link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(link.href);
    })
    .catch((error) => {
      ElMessage.error("下载失败");
    });
};

/** 初始加载 */
getList();
// getRuleBaseSelect();
</script>
<style lang="scss" scoped>
// 引入 base.scss 样式文件
@import "../../source/components/base.scss";
.content-container {
    display: flex;
    width: 100%;
    flex-direction: column;

    $left-container-width: 470px;
    $right-container-width: calc(100% - $left-container-width);
    .top-row { // 新增的包裹层
        display: flex;
        flex-direction: row;
        width: 100%;
        .left-container {
          width: $left-container-width;
          min-width: $left-container-width;
          flex-basis: $left-container-width;
          border-right: 1px solid #bccde0;
        }

        :deep(.box-container-top-menu) {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            height: 40px;
            padding: 0 10px;
            background-color: #fff;
        }
        :deep(.menu-container) {
            font-size: 14px;
            font-family: "MicrosoftYaHei";
            color: rgb(0, 120, 212);

            .menu-title {
                display: inline-block;
                height: 40px;
                line-height: 40px;
                border-bottom: 2px solid #0078d4;
            }

            .menu-title-btn {
                margin: 0 15px;
                padding: 4px 13px;
                border-radius: 4px;
                background-color: #fff;
                border-width: 1px;
                border-color: rgb(219, 219, 219);
                border-style: solid;
                cursor: pointer;
                box-sizing: border-box;
            }
            .menu-title-btn-gray {
                color: rgb(153, 152, 152);
                background-color: rgb(231, 235, 245);
                border-color: rgb(231, 235, 245);
            }

            .menu-title-count {
                font-size: 14px;
                font-family: "MicrosoftYaHei";
                color: rgb(0, 0, 0);
                font-style: normal;
            }

            .menu-container-icon {
                border: 1px solid rgb(219, 219, 219);
                border-radius: 4px;
                background-color: #e7ebf5;
                width: 60px;
                height: 25px;
                display: flex;
                flex-direction: row;
                align-items: center;
                overflow: hidden;

                span {
                    min-width: 50%;
                    width: 50%;
                    height: 100%;
                    flex: 1;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    background-color: #e7ebf5;

                    &.active {
                        background-color: #fff;
                    }
                }
            }

        }

        :deep(.box-container-content) {
            width: 100%;
            height: 100%;
            padding: 10px;
            // background-color: #eff4fa;
        }
        :deep(.box-container-content-footer) {
            text-align: right;
            span.btn {
                border-width: 1px;
                border-color: rgb(219, 219, 219);
                border-style: solid;
                border-radius: 2px;
                background-color: rgb(255, 255, 255);
                font-size: 14px;
                font-family: "MicrosoftYaHei";
                color: rgb(0, 120, 212);
                padding: 3px 8px;
                margin-left: 4px;
                cursor: pointer;
            }


        }
        :deep(.smalltabel-content .smalltabel-content-col-left) {
            width: 109px;
            min-width: 109px;
            text-align: left;
            padding-left: 14px;
        }
        :deep(.smalltabel-content .right-text-class .smalltabel-content-col-right) {
            background-color: #f9e3de;
        }
        .right-container {
            flex: 1;
            width: $right-container-width;

            :deep(.drawer-content-new) {
                width: 470px;
            }
        }
    }
    
    .bottom-container{
        width: 100%;
        border-top: 1px solid #bccde0; // 可选的分隔线
        padding-top: 20px; // 可选间距
    }
}
:deep(.my_menu_content) {
    height: 40px;
    border-bottom: 1px solid #bccde0;
    padding-left: 17px;
    .my_menu_title_left {
        font-size: 14px;
        line-height: 40px;
        font-family: "MicrosoftYaHei";
        color: rgb(0, 120, 212);
        cursor: pointer;

        svg {
            margin-right: 10px;
        }
    }
}

:deep(.table-container) {
    height: calc(100vh - 37px - 44px - 40px - 40px);
}
.my_drawer_title {
  display: flex;
  justify-content: space-between;
}
.my_drawer_title {
  display: flex;
  justify-content: space-between;
}
.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
  }
  
  .btn_add {
    background-color: rgb(0, 120, 212);
  }
  .menu-title-btn{
    margin: 0 10px;
    padding: 5px 5px;
    border-radius: 4px;
    background-color: #fff;
    border-width: 1px;
    border-color: rgb(219, 219, 219);
    border-style: solid;
    cursor: pointer;
    box-sizing: border-box;
    color: rgb(0, 120, 212);
  }
  .menu-title-btn-gray{
    margin: 0 10px;
    padding: 5px 5px;
    border-radius: 4px;
    background-color:rgb(231, 235, 245);
    border-bottom-color:rgb(231, 235, 245);
    border-width: 1px;
    // border-color: rgb(219, 219, 219);
    border-style: solid;
    cursor: pointer;
    box-sizing: border-box;
    color:rgb(153, 152, 152);
  }

  .page-info{
    // display: inline-block;
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(0, 0, 0);
    font-style: normal;
  }
}
// .my_drawer_title_right {
//   & > span {
//     border-radius: 2px;
//     background-color: rgb(189, 189, 189);
//     font-size: 14px;
//     font-family: "MicrosoftYaHei";
//     color: rgb(255, 255, 255);
//     padding: 5px 12px;
//     margin: 0 5px;
//   }

//   .btn_add {
//     background-color: rgb(0, 120, 212);
//   }
// }

// 单独设置右侧抽屉的样式，防止搜索部分被遮挡。与设计图不同～
:deep(.drawer-content-new) {
  margin-top: 0;
  border-top: 1px solid #bccde0;
  max-height: calc(100vh - 85px);
}

.operation-btn {
  display: inline-block;
  margin: 0 5px;
  color: #0076d0 !important;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
}

.form-container {
  border-radius: 2px;
  border: 1px solid rgb(221, 229, 235);
  background-color: rgb(255, 255, 255);
  overflow: hidden;

  :deep(.el-form-item--small) {
    margin-bottom: 0;
  }

  :deep(.el-input) {
    width: 335px;
    height: 34px;
  }

  :deep(.el-select) {
    width: 335px;
    height: 34px;
  }

  :deep(.el-input__wrapper) {
    height: 32px;
  }

  :deep(.el-select--small .el-select__wrapper) {
    height: 32px;
  }

  :deep(.el-form-item__content) {
    padding-top: 6px !important;
    padding-left: 9px !important;
    padding-right: 6px !important;
    background-color: transparent;
  }

  :deep(.form-item-52 .el-form-item__label) {
    height: 52px !important;
    line-height: 52px !important;
  }

  :deep(.form-item-54 .el-form-item__label) {
    height: 54px !important;
    line-height: 54px !important;
  }

  :deep(.form-item-64 .el-form-item__label) {
    height: 64px !important;
    line-height: 64px !important;
  }

  // 确保标签区域背景色连续
  :deep(.el-form-item) {
    display: flex;

    .el-form-item__label {
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 0, 0);
      font-weight: bold;
      padding-left: 15px !important;
      padding-top: 6px !important;
      padding-right: 0 !important;
      height: 40px;
      line-height: 40px;
      background-color: #f7f7f7;
      width: 110px;
      flex-shrink: 0;

      em {
        font-style: normal;
        color: #eb3037;
      }
    }
  }

  // 确保middle-box不受表单样式影响
  .middle-box {
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
  }

  :deep(
      .el-form-item.is-required:not(.is-no-asterisk).asterisk-left
        > .el-form-item__label-wrap
        > .el-form-item__label:before
    ),
  :deep(
      .el-form-item.is-required:not(.is-no-asterisk).asterisk-left
        > .el-form-item__label:before
    ) {
    display: none;
  }

  // textarea项的特殊样式
  :deep(.textarea-item) {
    .el-form-item__label {
      height: 196px !important;
      align-items: flex-start;
      padding-top: 6px !important;
    }
  }
}

.middle-box {
  display: flex;
  flex-direction: row;

  .middle-box-left {
    width: 110px;
    background-color: #f7f7f7 !important;
    flex-shrink: 0;
  }

  .middle-box-none {
    height: 20px;
    flex: 1;
  }

  .middle-box-right {
    height: 80px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      border: 1px solid rgb(219, 219, 219);
      border-radius: 2px;
      background-color: rgb(255, 255, 255);
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 120, 212);
      padding: 3px 11px;
      cursor: pointer;
    }
  }
}
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
  
  .detail-card {
    margin-bottom: 20px;
    border-radius: 4px;
    
    :deep(.el-card__header) {
      padding: 12px 20px;
      border-bottom: 1px solid #ebeef5;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
    }

    .card-content {
      padding: 16px;

      .search-form {
        background-color: #fff;
        margin-bottom: 16px;

        :deep(.el-form-item) {
          margin-bottom: 18px;
          width: 100%;

          .el-form-item__label {
            font-weight: normal;
            color: #606266;
}

          .el-input,
          .el-select {
            width: 100%;
          }
        }

        .el-button {
          margin-right: 8px;
          padding: 9px 15px;
        }
      }

      .operation-buttons {
        margin: 16px 0;
        padding: 8px 0;
        display: flex;
        gap: 8px;
        border-bottom: 1px solid #ebeef5;

        .el-button {
          padding: 9px 15px;
        }
      }
    }
  }

  :deep(.el-descriptions) {
    padding: 8px 0;
    
    .el-descriptions__label {
      color: #606266;
      font-weight: normal;
      text-align: right;
    }

    .el-descriptions__content {
  color: #303133;
}
  }

  :deep(.el-table) {
    margin: 8px 0;
    
    .el-table__header th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: 500;
}

    .el-table__row {
      &:hover {
        td {
          background-color: #f5f7fa !important;
        }
      }
    }
  }

  .pagination-container {
    padding: 16px 0 0;
    display: flex;
    justify-content: flex-end;
}
}
</style>
