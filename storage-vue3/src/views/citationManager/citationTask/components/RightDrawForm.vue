<template>
  <div class="form-container">
    <!-- 批量导入 -->
    <el-form
      v-if="props.type==='upload'"
      ref="uploadFormRef"
      :model="uploadFormData"
      :rules="rules"
      label-width="140px"
      size="small"
      label-position="left"
    >
      <el-form-item label="处理类型" prop="handleType">
        <el-radio-group v-model="uploadFormData.handleType">
          <el-radio value="IMPORT_CITE">导入引文数据</el-radio>
          <el-radio value="NO_CITE">无引文数据，直接标记完成</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 来源库 -->
      <el-form-item label="来源库" prop="source">
        <el-select v-model="uploadFormData.source" placeholder="请选择" style="width:100%">
          <el-option label="库A" value="A" />
          <el-option label="库B" value="B" />
          <el-option label="库C" value="C" />
        </el-select>
      </el-form-item>

       <!-- 关联规则 -->
      <el-form-item label="关联规则" prop="rule">
        <div v-for="(ruleItem, index) in uploadFormData.rule" :key="index" class="rule-input-group">
          <el-input v-model="uploadFormData.rule[index]" placeholder="请输入关联规则" style="width: 220px;"/>
        </div>
        <el-button type="success" circle @click="addRule" style="margin-left: 8px;">
          <el-icon size="15px" color="white">
            <Plus />
          </el-icon>
        </el-button>
        <el-button v-if="uploadFormData.rule.length > 1"   type="warning"circle @click="removeRule" style="margin-left: 8px;">
          <el-icon size="15px" color="#ffffff" >
              <Minus />
          </el-icon>
        </el-button>
        
      </el-form-item>

      <!-- 数据文件 -->
      <el-form-item label="数据文件" prop="file" class="el-form-item__label-207">
        <el-upload
          ref="uploadRef"
          v-model:file-list="uploadFormData.fileList"
          :auto-upload="false"
          :limit="1"
          :on-change="handleChange"
          :on-exceed="() => ElMessage.warning('只能上传一个文件')"
          accept=".txt,.xml,.zip"
          drag
          style="width:100%"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">
            将文件拖到此处或 <em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 txt / xml / zip 格式，且不超过 30MB
            </div>
          </template>
        </el-upload>

        <!-- <el-button
          type="success"
          :loading="uploading"
          @click="submitUpload"
        >
          确认
        </el-button>
        <el-button style="margin-left:12px" @click="resetForUpload">
          取消
        </el-button> -->
      </el-form-item>
    </el-form>

    <!-- 引文导入 -->
    <el-form
      v-if="props.type === 'import'"
      ref="importFormRef"
      :model="importFormData"
      :rules="rules"
      label-width="140px"
      size="small"
      label-position="left"
    >
      <!-- 文章信息 -->
      <el-form-item label="文章ID" prop="articleId">
        <el-input 
          v-model="importFormData.articleId" 
          placeholder="请输入文章ID" 
          clearable
          :readonly="true"
        />
      </el-form-item>

      <el-form-item label="文章标题" prop="articleTitle">
        <el-input 
          v-model="importFormData.articleTitle" 
          placeholder="请输入文章标题" 
          :readonly="true"
          clearable
        />
      </el-form-item>

      <el-form-item label="期刊ID" prop="journalId">
        <el-input 
          v-model="importFormData.journalId" 
          placeholder="请输入期刊ID"
          :readonly="true" 
          clearable
        />
      </el-form-item>

      <el-form-item label="期刊标题" prop="journalTitle">
        <el-input 
          v-model="importFormData.journalTitle" 
          placeholder="请输入期刊标题" 
          :readonly="true"
          clearable
        />
      </el-form-item>

      <!-- 数据文件上传 -->
      <!-- 文件上传 -->
      <el-form-item 
        label="数据文件" 
        prop="file" 
        class="el-form-item__label-259"
      >
        <el-upload
          ref="uploadRef"
          v-model:file-list="fileList"
          :auto-upload="false"
          :limit="1"
          :on-change="handleChange"
          :on-exceed="() => ElMessage.warning('只能上传一个文件')"
          accept=".txt,.xml,.zip"
          drag
          style="width: 100%"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            拖拽文件到此处或 <em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              仅支持 Excel（.txt,.xml,.zip）文件，大小不超过 10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import {reactive, ref, computed, getCurrentInstance, nextTick} from "vue";
import {importCitationIssueTask} from "@/api/citationManager/citationIssueTask";
import { ElMessage } from 'element-plus'
const {proxy} = getCurrentInstance();

const props = defineProps({
  modal: {
    type: Object,
  },
  type:    { type: String, required: true }
});
/**字典*/
// const {sysDict, bizDict} = props.modal.dict
const form = computed({
  get() {
    return props.modal.form;
  },
  set() {
  }
});
//文件上传相关
const uploadFormRef   = ref()
const importFormRef   = ref()
const uploadRef = ref()
const fileList  = ref([])          // el-upload 的 v-model
const uploading = ref(false)
// 需要进行数组转换的字段
const arrayField = ref(['processFlow', 'analysisRuleId']);

const ruleFormRef = ref();

const testConnectionFlag = ref(null)
const uploadFormData = reactive({
  source: '',       // 来源库
  rule: ["doi","articleId"],             // 规则
  handleType:'', //处理类型
  file: null,          // 文件对象（关键：存储上传的文件）
  fileList: []         // 文件列表（用于上传组件显示）
});

const importFormData = reactive({
  articleId: '',       // 文章ID
  articleTitle: '',    // 文章标题
  journalId: '',       // 期刊ID
  journalTitle: '' ,    // 期刊标题
  file: null,          // 文件对象（关键：存储上传的文件）
  fileList: []         // 文件列表（用于上传组件显示）
});
const rules = reactive({
  // 来源库（下拉选择，必填）
  source: [
    { required: true, message: '请选择来源库（如 pubmed、cnki 等）', trigger: 'change'}
  ],

  // 校验规则（数组，至少选一个字段）
  rule: [
    { required: true, message: '请选择至少一条校验规则（如 doi、articleId）', trigger: 'change'}
  ],

  // 处理类型（输入/下拉，必填）
  handleType: [
    { required: true, message: '请输入处理类型（如 0: 基础处理，1: 高级处理）', trigger: 'blur' }
  ],

  // 文件对象（上传组件，必填且需为 Excel）
  file: [
    { required: true, message: '请上传 Excel 文件（.xlsx 或 .xls）', trigger: 'change' }
  ],

  articleId: [
    { required: true, message: '请输入文章ID', trigger: 'blur' }
  ],
  articleTitle: [
    { required: true, message: '请输入文章标题', trigger: 'blur' }
  ],
  journalId: [
    { required: true, message: '请输入期刊ID', trigger: 'blur' }
  ],
  journalTitle: [
    { required: true, message: '请输入期刊标题', trigger: 'blur' }
  ]
})
/* 文件变化时把文件塞进 form 并手动校验 */
function handleChange(file) {
  if(props.type=='upload'){
    console.log(file);

      // 1. 先清空旧数据
    fileList.value = []
    uploadFormData.file = null

    // 2. 没选文件直接返回
    if (!file?.raw) return

    const f = file.raw
    const okType = ['text/plain', 'application/xml', 'text/xml'].includes(f.type)
    const okSize = f.size / 1024 / 1024 < 20

    // 3. 格式或大小不合法
    if (!okType || !okSize) {
      ElMessage.warning(okType ? '文件 ≤ 20MB' : '仅支持 txt / xml')
      return
    }

    // 4. 同步到两边
    fileList.value = [file]  // el-upload 展示
    uploadFormData.file = f                  // form.file 校验
    nextTick(() => uploadFormRef.value?.validateField('file'))
  }else if(props.type=='import'){
    console.log(file);

      // 1. 先清空旧数据
    fileList.value = []
    uploadFormData.file = null

    // 2. 没选文件直接返回
    if (!file?.raw) return

    const f = file.raw
    const okType = ['text/plain', 'application/xml', 'text/xml'].includes(f.type)
    const okSize = f.size / 1024 / 1024 < 20

    // 3. 格式或大小不合法
    if (!okType || !okSize) {
      ElMessage.warning(okType ? '文件 ≤ 20MB' : '仅支持 txt / xml')
      return
    }

    // 4. 同步到两边
    fileList.value = [file]  // el-upload 展示
    importFormData.file = f                  // form.file 校验
    nextTick(() => importFormData.value?.validateField('file'))
  }
}

/* 真正上传 */
async function submitUpload() {
  await formRef.value.validate()
  uploading.value = true
  try {
    const fd = new FormData()
    fd.append('file', uploadFormData.file)
    await importCitationIssueTask(uploadFormData.file.raw || uploadFormData.file, 0);
    ElMessage.success('上传成功')
    reset()
  } catch (e) {
    ElMessage.error(e?.response?.data?.msg || '上传失败')
  } finally {
    uploading.value = false
  }
}

/* 重置 */
function resetForUpload() {
  formRef.value.resetFields()
  fileList.value = []
  form.file = null
}
// 添加规则（点击加号）
const addRule = () => {
  uploadFormData.rule.push(''); // 向数组末尾添加空字符串
};

// 删除规则（点击减号，传入要删除的索引）
const removeRule = () => {
  uploadFormData.rule.pop();
};

// 处理数组字段转换为字符串（保存时使用）
const processArrayToString = (data) => {
  const processedData = {...data};
  if (arrayField.value) {
    arrayField.value.forEach(field => {
      if (processedData[field] && Array.isArray(processedData[field])) {
        console.log(`转换数组字段 ${field}:`, processedData[field], '-> ', processedData[field].join(','));
        // 过滤空值并转换为字符串
        processedData[field] = processedData[field]
            .filter(item => item && item.trim() !== '')
            .join(',');
      }
    });
  }
  return processedData;
};

// 处理字符串转换为数组（编辑时使用）
const processStringToArray = (data, readonly = false) => {
  if (arrayField.value && !readonly) {
    arrayField.value.forEach(field => {
      if (data[field] && typeof data[field] === 'string') {
        console.log(`转换字符串字段 ${field}:`, data[field], '-> ', data[field].split(','));
        // 处理空字符串的情况
        if (data[field].trim() === '') {
          data[field] = [];
        } else {
          data[field] = data[field].split(',').filter(item => item.trim() !== '');
        }
      }
    });
  }
};

// 获取表单数据
const getFormData = () => {
  if(props.type==='upload'){
    return processArrayToString(uploadFormData);
  }else if(props.type==='import'){
    return processArrayToString(importFormData);
  }
};

// 重置表单
const resetForm = () => {
  console.log('=== 重置表单开始 ===');
  console.log('重置前 ftpUser:', formData.ftpUser, 'ftpPwd:', formData.ftpPwd);

  // 先清除表单验证状态
  ruleFormRef.value?.clearValidate();

  // 重置表单数据为初始状态
  Object.assign(formData, {
    name: '',
    dataId: '',
    pubType: '',
    dataLevel: '',
    dataType: '',
    processFlow: [],
    harvestType: '',
    ftpUrl: '',
    ftpPort: '',
    ftpUser: '',
    ftpPwd: '',
    fileType: '',
    ifaceUrl: '',
    analysisRuleId: '',
    sourceType: '',
    describes: ''
  });

  console.log('重置后 ftpUser:', formData.ftpUser, 'ftpPwd:', formData.ftpPwd);

  // 强制清空表单字段值，确保输入框显示为空
  setTimeout(() => {
    ruleFormRef.value?.resetFields();

    // 额外确保用户名和密码字段为空
    formData.ftpUser = '';
    formData.ftpPwd = '';

    console.log('最终检查 ftpUser:', formData.ftpUser, 'ftpPwd:', formData.ftpPwd);
    console.log('=== 重置表单完成 ===');
  }, 100);
};

// 设置表单数据
const setFormData = (data) => {
  console.log('=== 设置表单数据 ===');
  console.log('传入数据:', data);

  // 深拷贝数据，避免修改原始对象
  const processedData = JSON.parse(JSON.stringify(data || {}));

  // 处理字符串转数组
  processStringToArray(processedData);
  console.log('处理后数据:', processedData);
  // 设置到表单数据
  if(props.type==='import'){
    console.log("引文导入")
    Object.assign(importFormData, processedData);
    
  }else if(props.type==='upload'){
    console.log("批量导入")
    Object.assign(uploadFormData, processedData);
  }
  
  console.log('=== 设置表单数据完成 ===');
};

// 表单验证
// const validateForm = () => {
//   if(props.type=='upload'){
//     ruleFormRef.value.validate();
//   }
// }
  
const validateForm = () => {
  if(props.type=='upload'){
    uploadFormRef.value.validate();
  }else if(props.type=='import'){
    importFormRef.value.validate();
  }
};

// 专门用于新增时的完全重置
const resetForAdd = () => {
  console.log('=== 开始批量导入表单重置 ===');

  // 安全校验：确保表单和上传组件引用存在
  if (!uploadFormRef.value) {
    console.warn('表单引用未初始化，重置失败');
    return;
  }
  if (!uploadRef.value) {
    console.warn('文件上传组件引用未初始化，重置失败');
    return;
  }

  try {
    // 步骤1：清除表单验证状态（Element Plus 表单方法）
    uploadFormRef.value.clearValidate();

    // 步骤2：重置表单数据（核心逻辑）
    // 创建临时副本避免直接修改原数据（可选，根据需求调整）
    const resetData = { ...uploadFormData };

    // 遍历所有字段并重置（根据表单字段定制）
    Object.keys(resetData).forEach((key) => {
      switch (key) {
        case 'source':
          resetData[key] = '';       // 下拉框重置为空（可选：默认选中第一个选项）
          break;
        case 'rule':
        case 'updateTime':
          resetData[key] = '';       // 输入框重置为空字符串
          break;
        case 'file':
          resetData[key] = null;     // 文件对象重置为 null
          break;
        case 'fileList':
          resetData[key] = [];       // 文件列表重置为空数组（el-upload 内部使用）
          break;
        default:
          resetData[key] = '';       // 其他字段默认重置为空
      }
    });

    // 更新响应式表单数据（触发视图更新）
    Object.assign(uploadFormData, resetData);

    // 步骤3：清空文件上传组件的内部状态（关键！避免残留文件）
    uploadRef.value.fileList = [];  // 清空文件列表
    uploadRef.value.clearFiles();   // 清空文件缓存（Element Plus 方法）

    // 步骤4：强制同步表单状态（可选，确保与数据同步）
    nextTick(() => {
      uploadFormRef.value.resetFields();  // 重置表单字段（Element Plus 方法）
      console.log('批量导入表单重置完成，当前数据:', JSON.stringify(uploadFormData, null, 2));
      console.log('文件上传组件已清空，fileList:', uploadRef.value.fileList);
      console.log('=== 批量导入表单重置完成 ===');
    });

  } catch (error) {
    console.error('批量导入表单重置过程中发生错误:', error);
  }
};

// 暴露方法给父组件
defineExpose({
  getFormData,
  resetForm,
  resetForAdd,  // 新增专用重置方法
  setFormData,
  validateForm
});
</script>

<style lang="scss" scoped>
.form-container {
  border-radius: 2px;
  border: 1px solid rgb(221, 229, 235);
  background-color: rgb(255, 255, 255);
  overflow: hidden;

  :deep(.el-form-item--small) {
    margin-bottom: 0;
  }

  :deep(.el-input) {
    width: 335px;
    height: 34px;
  }
  :deep(.el-select) {
    width: 335px;
    height: 34px;
  }
  :deep(.el-input__wrapper) {
    height: 32px;
  }

  :deep(.el-select--small .el-select__wrapper) {
    height: 32px;
  }

  :deep(.el-form-item__content) {
    padding-top: 6px !important;
    padding-left: 9px !important;
    padding-right: 6px !important;
    background-color: transparent;
  }

  // 确保标签区域背景色连续
  :deep(.el-form-item) {
    display: flex;

    .el-form-item__label {
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 0, 0);
      font-weight: bold;
      padding-left: 15px !important;
      padding-top: 6px !important;
      padding-right: 0 !important;
      height: 40px;
      line-height: 40px;
      background-color: #f7f7f7;
      width: 110px;
      flex-shrink: 0;

      em {
        font-style: normal;
        color: #eb3037;
      }
    }
  }
  :deep(.el-form-item__label-259 .el-form-item__label) {
    height: 259px !important;
    line-height: 259px !important;
  }
  :deep(.el-form-item__label-74 .el-form-item__label) {
    height: 74px !important;
    line-height: 74px !important;
  }

  // 确保middle-box不受表单样式影响
  .middle-box {
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
  }

  :deep(.el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label-wrap>.el-form-item__label:before),
  :deep(.el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label:before) {
    display: none;
  }

  // textarea项的特殊样式
  :deep(.textarea-item) {
    .el-form-item__label {
      height: 196px !important;
      align-items: flex-start;
      padding-top: 6px !important;
    }
  }
}

.middle-box {
  display: flex;
  flex-direction: row;

  .middle-box-left {
    width: 110px;
    background-color: #f7f7f7 !important;
    flex-shrink: 0;
  }

  .middle-box-none {
    height: 20px;
    flex: 1;
  }

  .middle-box-right {
    height: 80px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      border: 1px solid rgb(219, 219, 219);
      border-radius: 2px;
      background-color: rgb(255, 255, 255);
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 120, 212);
      padding: 3px 11px;
      cursor: pointer;
    }
  }
}

// 映射规则标签样式
.rule-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
  min-height: 32px;
  padding: 2px 0;

  .rule-tag {
    margin: 0;
    font-size: 12px;
    border-radius: 3px;
    background-color: #e8f4fd;
    border: 1px solid #b3d9f7;
    color: #1890ff;
    padding: 2px 8px;
    height: auto;
    line-height: 1.4;

    :deep(.el-tag__content) {
      line-height: 1.4;
    }

    &:hover {
      background-color: #d6f0ff;
    }
  }

  .no-data-text {
    color: #999;
    font-size: 13px;
    font-style: italic;
    padding: 6px 0;
  }
}
</style>
