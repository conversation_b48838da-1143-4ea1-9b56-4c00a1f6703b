<template>
  <div class="app-container">
    <!-- 背景框框 -->
    <viewBody title="卷期任务管理">
      <template #content>
        <!-- 使用FormSearch组件替换搜索菜单 -->
        <FormSearch
          ref="formSearchRef"
          v-model="queryParams"
          :formItems="formItems"
          @search="handleQuery"
          @reset="resetQuery"
        >
          <template #btn>
            <el-button 
            type="primary" class="my_primary_btn"
            icon="Plus" 
            @click="handleAdd()" >
            批量导入</el-button>
          </template>
        </FormSearch>

        <!-- 使用TableForm组件替换原有表格 -->
        <TableForm
          ref="tableFormRef"
          :columns="columns"
          :tableData="dataSourceList"
          :total="total"
          :showOtherColumn="true"
          tableotherColumnLabel="操作"
          :isShowCount="true"
          :isShowSearchQuery="false"
          :isShowSelection="true"
          :columnsRightWidth=120
          :showIndex="false"
          @cellClick="handleRowClick"
        >
          <!-- 操作按钮插槽 -->
          <template #otherOperation="{ row }">
            <span class="operation-btn" @click.stop="handle(row)">
              处理
            </span>
            <span class="operation-btn" @click.stop="handleDetail(row)">
              日志
            </span>
          </template>
          <!-- 分页插槽 -->
          <template #pagination>
            <MyPagination
              v-if="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </template>

          <template #drawer>
            <MyDrawer ref="drawerRef">
              <template #header="{ titleId, titleClass }">
                <div :id="titleId" :class="titleClass" class="my_drawer_title">
                  <span class="active">{{ drawer.title }}</span>
                  <div class="my_drawer_title_right">
                    <span class="btn_add" @click="submitForm">保存</span>
                    <span class="btn_cancel" @click="cancel">取消</span>
                  </div>
                </div>
              </template>
              <template #form>
                <RightDrawForm ref="rightFormRef" :modal="form":type=drawer.type></RightDrawForm>
              </template>
            </MyDrawer>
          </template>
        </TableForm>
      </template>
    </viewBody>
  </div>
</template>

<script setup name="dataSource">
import {
  listSource,
  getSource,
  addSource,
  updateSource,
  updateState,
  getBatchList,
  downloadFromMinio,
  parseMappingBatch,
} from "@/api/task/task";
import {
  listCitationIssueTask,
  getCitationIssueTaskDetail
} from "@/api/citationManager/citationIssueTask.js";
import { queryRuleBaseSelect } from "@/api/processingtool/ruleBase";
import { useRouter } from "vue-router";
import { nextTick } from "vue";
import dayjs from "dayjs";
import MyPagination from "@/components/Pagination/new.vue";
import FormSearch from "@/views/citationManager/components/FormSearch.vue";
import TableForm from "@/views/citationManager/components/TableForm.vue";
import MyDrawer from "@/views/citationManager/components/MyDrawer.vue";
import RightDrawForm from "./components/RightDrawForm.vue";
import viewBody from "@/views/citationManager/components/viewBody.vue";
import { ElMessage } from "element-plus";
import { DICTIONARIES, SAMPLE_DATA,TOTAL } from "@/views/citationManager/components/citationData";

const { proxy } = getCurrentInstance();
const router = useRouter();
// const { storage_data_type } = proxy.useDict("storage_data_type");
// const { storage_harvest_type } = proxy.useDict("storage_harvest_type");
// const { storage_harvest_cycle } = proxy.useDict("storage_harvest_cycle");
// const { storage_last_state } = proxy.useDict("storage_last_state");

// 存储业务字典数据
const bizDict = reactive({
  ruleBaseSelectOptions: [],
});

const dataSourceList = ref(SAMPLE_DATA);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const tableFormRef = ref(null);
const formSearchRef = ref(null);
const batchTableFormRef = ref(null);
//抽屉

const drawer = reactive({
  visible: false,
  title: '',
  type: ''
});
const drawerOpen = ref(false);
const drawerRef = ref(null)
const rightFormRef = ref(null)
const currentEditRow = ref(null)

// 在 script setup 中添加表单引用
const datasourceform = ref(null);
//引文级别字典
const status = reactive([
  { label: "全部", value: 1 },
  { label: "待处理", value: 2 },
  { label: "已完成", value: 3 },
]);

// 搜索表单配置
const formItems = ref([
  {
    prop: "journalTitle",
    component: "el-input",
    props: {
      placeholder: "品种名称",
      clearable: true,

      style: { width: "200px" },
    },
  },
  {
    prop: "year",
    component: "el-input",
    props: {
      placeholder: "年份",
      clearable: true,
      style: { width: "120px" },
    },
  },
  {
    prop: "volume",
    component: "el-input",
    props: {
      placeholder: "卷号",
      clearable: true,
      style: { width: "120px" },
    },
  },
  {
    prop: "issue",
    component: "el-input",
    props: {
      placeholder: "期号",
      clearable: true,
      style: { width: "120px" },
    },
  },
  {
    prop: "status",
    component: "el-select",
    props: {
      placeholder: "状态",
      clearable: true,
      style: { width: "200px" },
    },
    children: () =>
      status.map((item) => ({
        value: item.value,
        label: item.label,
      })),
  },
  {
    label: '时间范围',
    prop: 'createTimeRange', // 表单数据中临时存储日期数组的字段（对应 el-date-picker 的 v-model）
    component: 'el-date-picker', // 关键：指定为 el-date-picker 组件
    props: {
      type: 'daterange', // 指定为日期范围选择器
      placeholder: '请选择创建时间范围',
      clearable: true, // 可清空
      style: { width: '280px' }, // 宽度（根据你的布局调整）
      format: 'YYYY-MM-DD', // 显示格式（可选）
      valueFormat: 'YYYY-MM-DD', // 传递给后端的格式（可选，若不需要可省略）
    },
    // 日期变化时拆分日期到 start/end 字段
    onChange: (val) => {
      if (val) {
        // val 是 [开始日期, 结束日期] 数组（Date 对象或字符串，取决于 valueFormat）
        queryParams.value.startCreateTime = val[0]; 
        queryParams.value.endCreateTime = val[1];
      } else {
        // 清空时重置两个时间字段
        queryParams.value.startCreateTime = null;
        queryParams.value.endCreateTime = null;
      }
    }
  }
])

// 表格列配置
const columns = ref([
  { prop: "journalId", label: "品种ID", width: "120" },
  { prop: "journalTitle", label: "品种名称", width: "100" },
  { prop: "ISSN", label: "ISSN", width: "100" },
  { prop: "year", label: "年份", width: "120" },
  { prop: "volume", label: "卷号", width: "100" },
  { prop: "issue", label: "期号", width: "140" },
  { prop: "articleNum", label: "待加工篇级数量", width: "180" },
  { prop: "status", label: "状态", width: "180" },
  { prop: "updateDate", label: "更新时间", width: "180" },
]);
// 新增批次数据相关变量
const selectedTask = ref(null);
const batchList = ref([]);
const batchLoading = ref(false);
const batchTotal = ref(0);
const selectedBatchIds = ref([]); // 存储选中的批次ID
const batchQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  sourceId: null,
});

const data = reactive({
  form: {
    dataSource: {},
    cycleType: "daily", // 默认设置为每日收割
    cycleDate: null,
    cycleWeek: [1], // 默认设置为周一
    nextCycleStartDate: null, // 默认设置为明天
    nextCycleEndDate: null,
    nextCycleDate: null,
    cycleCron: null,
    ifaceUrl: null,
    ifaceParams: null,
    ftpUrl: null,
    ftpPort: null,
    ftpUser: null,
    ftpPwd: null,
    state: 1,
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    journalTitle:'',
    journalId:'',
    year:'',
    issue:'',
    status:'',
    startCreateTime:'',
    endCreateTime:'',
    createTimeRange:[],
  },
  rules: {
    cycleType: [
      { required: true, message: "收割频率不能为空", trigger: "change" },
    ],
    cycleDate: [
      { required: true, message: "收割时间不能为空", trigger: "change" },
    ],
    cycleWeek: [
      { required: true, message: "请选择执行星期", trigger: "change" },
    ],
    nextCycleStartDate: [
      { required: true, message: "收割起始时间不能为空", trigger: "change" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

async function getRuleBaseSelect() {
  // , ruleStatus: 'COMPLETED'
  const { data } = await queryRuleBaseSelect({ ruleType: "ANALYSIS" });
  console.log("获取解析规则字典信息", data);
  bizDict.ruleBaseSelectOptions = data;
}
//============================================按钮的回调=======================
/** 处理按钮操作 */
function handle(row) {
  console.log("row", row)
  router.push({
    name: 'CitationTaskHandle', 
    query: {
      journalId:row.journalId
    },
  });
}
/** 查看按钮操作 */
function handleDetail(row) {
  // 跳转规则结果详情页
  console.log("row", row)
  router.push({
    name: 'CitationTaskView',
    query: {
      journalId:row.journalId
    },
  });
}
//批量导入按钮
//批量导入
async function handleAdd() {
  console.log('=== 批量导入按钮点击 ===');
  // reset();
  drawer.title = '批量导入';
  drawer.type='upload'
  currentEditRow.value = null;

  // 打开抽屉
  drawerRef.value.openDrawer();
}

/** 查询任务列表列表 */
function getList() {
  loading.value = true;
  if (queryParams.value.createTimeRange.length > 0) {
    queryParams.value.startCreateTime = queryParams.value.createTimeRange[0] + " 00:00:00";
    queryParams.value.endCreateTime = queryParams.value.createTimeRange[1] + " 23:59:59";
  }
  // 清空选中的任务和批次数据
  selectedTask.value = null;
  total.value=TOTAL;
  // 调用接口获取数据
  listCitationIssueTask(queryParams.value)
  .then(res => {
    console.log("得到的数据",res)
    // 假设接口返回结构为 { code: 200, data: { list: [], total: 100 } }
    if (res.code === 200) {
      // 检查接口返回的 list 是否为空数组（兼容可能的无 list 字段的情况）
      const isEmpty = !Array.isArray(res.data.records);
      if (isEmpty) {
        // 使用假数据替代空数据
        dataSourceList.value = SAMPLE_DATA;
        // total.value =SAMPLE_DATA.value.length; // 假数据总条数（10条）
        ElMessage.warning('当前筛选条件下无数据，显示示例数据'); // 可选：提示用户
      }else{
        dataSourceList.value = res.data.records; 
        dataSourceList.value.forEach(item => {
          if (item.status === 1) {
            item.articleNum = 0;
          } 
          item.status = item.status === 1 ? '已完成' : '待处理';
        })
        total.value = res.data.total; 
      }
    }else {
      ElMessage.error(res.message || '获取数据失败');
    }
  })
  .catch(error => {
    ElMessage.error('获取任务列表失败');
    console.error(error);
    loading.value = false; // 请求失败后关闭加载状态
  });
}
/** 处理解析映射按钮点击 */
function handleAnalysisMapping() {
  // 检查是否有选中的行和批次数据
  if (!selectedTask.value) {
    proxy.$modal.msgWarning("请先选择一个数据源");
    return;
  }

  if (!batchList.value || batchList.value.length === 0) {
    proxy.$modal.msgWarning("没有可用的批次数据");
    return;
  }

  // 检查是否有选中的批次
  if (!selectedBatchIds.value || selectedBatchIds.value.length === 0) {
    proxy.$modal.msgWarning("请选择需要解析映射的批次");
    return;
  }

  // 显示确认对话框
  proxy.$modal
    .confirm(
      `确定要对选中的 ${selectedBatchIds.value.length} 个批次执行解析映射操作吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    )
    .then(() => {
      // 用户确认，调用解析映射接口
      proxy.$modal.loading("正在执行解析映射操作，请稍候...");

      parseMappingBatch(selectedBatchIds.value)
        .then((response) => {
          proxy.$modal.closeLoading();
          if (response.code === 200) {
            proxy.$modal.msgSuccess("解析映射操作成功");
            // 刷新批次数据
            getBatchListById();
          } else {
            proxy.$modal.msgError(`解析映射操作失败: ${response.msg}`);
          }
        })
        .catch((error) => {
          proxy.$modal.closeLoading();
          console.error("解析映射操作失败:", error);
          proxy.$modal.msgError("解析映射操作失败，请重试");
        });
    })
    .catch(() => {
      // 用户取消操作
    });
}

/** 跳转到收割任务列表页面 */
function goToTaskList() {
  router.push({ path: "/collection/batch" });
}

/** 跳转到解析映射列表页 */
function goToMappingList() {
  router.push({ path: "/obtainConvert/parsingMappingTask" });
}


// 格式化日期时间
function formatDateTime(dateTime) {
  if (!dateTime) return "";

  // 处理字符串类型的日期时间
  if (typeof dateTime === "string") {
    // 替换T为空格，移除毫秒部分和时区信息
    return dateTime.replace("T", " ").replace(/\.\d+(\+|\-).*$/, "");
  }

  return dateTime;
}

// 获取字典标签
function getDictLabel(options, value) {
  if (!value || !options?.length) return "";
  return options.find((opt) => opt.value === value)?.label ?? value;
}

/** 编辑按钮打开抽屉 */
const handleUpdate = (row) => {
  if (!drawerRef.value) {
    ElMessage.error("抽屉组件未初始化");
    return;
  }

  title.value = row ? "修改收割任务" : "添加收割任务";
  resetForm();

  // 使用 nextTick 确保 DOM 已更新
  nextTick(() => {
    try {
      // 先打开抽屉
      drawerRef.value.openDrawer();

      // 打开后再获取数据
      setTimeout(() => {
        if (row) {
          const id = row.dataId;
          getTask(id);
        }
      }, 100);
    } catch (error) {
      console.error("打开抽屉失败:", error);
    }
  });
};

/** 取消按钮 */
const cancel = () => {
  if (!drawerRef.value) return;

  try {
    // 先重置表单
    resetForm();
    // 关闭抽屉
    drawerRef.value.closeDrawer();
  } catch (error) {
    console.error("关闭抽屉失败:", error);
  }
};

/** 表单提交 */
const submitForm = () => {
  proxy.$refs["datasourceform"].validate((valid) => {
    if (valid) {
      // 表单验证，确保每周收割时必须选择星期
      if (
        form.value.cycleType === "weekly" &&
        (!form.value.cycleWeek || form.value.cycleWeek.length === 0)
      ) {
        proxy.$modal.msgError("每周收割模式下必须选择星期");
        return;
      }

      // 表单验证，确保必须有cycleDate
      if (form.value.cycleType !== "immediate" && !form.value.cycleDate) {
        proxy.$modal.msgError("请设置收割时间");
        return;
      }

      // 强制重新生成cron表达式和计算下次执行时间
      generateCronExpression();
      calculateNextExecutionTime();

      // 处理周选择数据格式
      if (form.value.cycleWeek && Array.isArray(form.value.cycleWeek)) {
        form.value.cycleWeek = form.value.cycleWeek.join(";");
      }

      // 转换前端的cycleType格式为后端格式
      let backendCycleType;
      if (form.value.cycleType === "immediate") {
        backendCycleType = "0";
      } else if (form.value.cycleType === "daily") {
        backendCycleType = "1";
      } else if (form.value.cycleType === "weekly") {
        backendCycleType = "2";
      }

      // 创建提交数据对象
      const submitData = {
        ...form.value,
        cycleType: backendCycleType,
        cycleCron: form.value.cycleCron,
        // 处理特殊的下次执行时间显示文本
        nextCycleDate:
          form.value.nextCycleDate === "已超出收割截止时间" ||
          form.value.nextCycleDate === "任务已执行"
            ? null
            : form.value.nextCycleDate,
        // 确保dataId字段正确设置
        dataId: form.value.dataSource.dataId,
      };

      // 根据是否有id字段决定是新增还是修改
      if (form.value.id) {
        // 有id，执行修改操作
        updateSource(submitData)
          .then((response) => {
            proxy.$modal.msgSuccess("修改成功");
            cancel();
            getList();
          })
          .catch((error) => {
            console.error("修改失败:", error);
            proxy.$modal.msgError("修改失败，请重试");
          });
      } else {
        // 无id，执行新增操作
        addSource(submitData)
          .then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            cancel();
            getList();
          })
          .catch((error) => {
            console.error("新增失败:", error);
            proxy.$modal.msgError("新增失败，请重试");
          });
      }
    }
  });
};

/** 行点击事件 */
const handleRowClick = async (row) => {
  try {
    // 先设置选中任务
    selectedTask.value = row;

    // 重置批次查询参数
    batchQueryParams.value = {
      pageNum: 1,
      pageSize: 10,
      sourceId: row.dataId,
    };

    // 等待DOM更新完成
    await nextTick();

    // 获取批次数据
    await getBatchListById();
  } catch (error) {
    console.error("处理行点击事件失败:", error);
  }
};

/** 获取批次列表 */
const getBatchListById = (page) => {
  if (!selectedTask.value) return;

  if (page) {
    batchQueryParams.value.pageNum = page.pageNum;
    batchQueryParams.value.pageSize = page.pageSize;
  }

  console.log("获取批次数据，参数:", batchQueryParams.value);
  batchLoading.value = true;

  // 使用 Promise 包装请求
  return new Promise((resolve) => {
    getBatchList(batchQueryParams.value)
      .then((response) => {
        console.log("获取批次数据成功:", response);

        // 处理数据，转换字典项和时间格式
        const formattedData = (response.rows || []).map((row) => ({
          ...row,
          // 处理时间格式
          createTime: formatDateTime(row.createTime),
          endTime: formatDateTime(row.endTime),
          // 处理字典项
          dataType: getDictLabel(storage_data_type.value, row.dataType),
          status: getDictLabel(storage_last_state.value, row.status),
          // 格式化文件大小
          fileSize: row.fileSize ? Number(row.fileSize).toFixed(2) : "0",
        }));

        batchList.value = formattedData;
        batchTotal.value = response.total;
        batchLoading.value = false;
        resolve(response);
      })
      .catch((error) => {
        console.error("获取批次列表失败:", error);
        batchLoading.value = false;
        batchList.value = [];
        batchTotal.value = 0;
        resolve(null);
      });
  });
};

/** 监听收割频率变化 */
watch(
  () => form.value.cycleType,
  (newVal) => {
    if (newVal) {
      // 如果没有设置起始时间，尝试设置默认值
      if (!form.value.nextCycleStartDate) {
        const tomorrow = dayjs().add(1, "day").format("YYYY-MM-DD 00:00:00");
        form.value.nextCycleStartDate = tomorrow;
      }
      generateCronExpression();
      calculateNextExecutionTime();
    }
  }
);

/** 监听cycleDate变化 */
watch(
  () => form.value.cycleDate,
  (newVal) => {
    if (newVal) {
      // 如果没有设置类型，默认使用每日收割
      if (!form.value.cycleType) {
        form.value.cycleType = "daily";
      }

      // 如果没有设置起始时间，尝试设置默认值
      if (!form.value.nextCycleStartDate) {
        const tomorrow = dayjs().add(1, "day").format("YYYY-MM-DD 00:00:00");
        form.value.nextCycleStartDate = tomorrow;
      }

      generateCronExpression();
      calculateNextExecutionTime();
    }
  }
);

/** 监听cycleWeek变化 */
watch(
  () => form.value.cycleWeek,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      // 如果更改了星期，自动设置为每周收割
      form.value.cycleType = "weekly";

      // 如果没有设置起始时间，尝试设置默认值
      if (!form.value.nextCycleStartDate) {
        const tomorrow = dayjs().add(1, "day").format("YYYY-MM-DD 00:00:00");
        form.value.nextCycleStartDate = tomorrow;
      }

      generateCronExpression();
      calculateNextExecutionTime();
    }
  },
  { deep: true }
);

/** 监听起始时间变化 */
watch(
  () => form.value.nextCycleStartDate,
  (newVal) => {
    if (newVal) {
      if (!form.value.cycleType) {
        form.value.cycleType = "daily"; // 默认为每日收割
      }
      generateCronExpression();
      calculateNextExecutionTime();
    }
  }
);

/** 监听截止时间变化 */
watch(
  () => form.value.nextCycleEndDate,
  (newVal) => {
    if (form.value.nextCycleStartDate && form.value.cycleType) {
      generateCronExpression();
      calculateNextExecutionTime();
    }
  }
);

/** 监听nextCycleStartDate和nextCycleEndDate变化进行日期验证 */
watch(
  [() => form.value.nextCycleStartDate, () => form.value.nextCycleEndDate],
  () => {
    validateDateRange();
  }
);

/** 验证开始和结束时间 */
function validateDateRange() {
  if (form.value.nextCycleStartDate && form.value.nextCycleEndDate) {
    const startDate = new Date(form.value.nextCycleStartDate);
    const endDate = new Date(form.value.nextCycleEndDate);

    if (endDate <= startDate) {
      proxy.$modal.msgError("收割截止时间必须大于收割起始时间");
      form.value.nextCycleEndDate = null;
    }
  }
}

/** 优化generateCronExpression函数，提高代码可读性，修复星期几的映射 */
function generateCronExpression() {
  if (!form.value.cycleType || !form.value.nextCycleStartDate) {
    form.value.cycleCron = "";
    return;
  }

  // 解析时间部分
  let seconds = 0,
    minutes = 0,
    hours = 0;
  if (form.value.cycleDate) {
    const parts = form.value.cycleDate.split(":");
    hours = parseInt(parts[0]);
    minutes = parseInt(parts[1]);
    seconds = parts.length > 2 ? parseInt(parts[2]) : 0;
  } else {
    const startDate = dayjs(form.value.nextCycleStartDate);
    hours = startDate.hour();
    minutes = startDate.minute();
    seconds = startDate.second();
  }

  // 根据收割类型生成不同的cron表达式
  switch (form.value.cycleType) {
    case "immediate":
      // 立即收割，只执行一次
      const startDate = dayjs(form.value.nextCycleStartDate);
      form.value.cycleCron = `${seconds} ${minutes} ${hours} ${startDate.date()} ${
        startDate.month() + 1
      } ? *`;
      break;

    case "daily":
      // 每日收割，指定时分秒，每天执行
      form.value.cycleCron = `${seconds} ${minutes} ${hours} * * ? *`;
      break;

    case "weekly":
      // 每周收割，指定星期几执行
      let weekdaysStr;
      if (form.value.cycleWeek && form.value.cycleWeek.length > 0) {
        // 将UI中的周几值转换为Quartz的周几值
        // UI: 周日=7, 周一=1, ..., 周六=6
        // Quartz: 周日=1, 周一=2, ..., 周六=7
        const quartzWeekdays = form.value.cycleWeek.map((day) => {
          // 如果是周日(7)，转为Quartz的周日(1)
          // 否则加1，将周一至周六(1-6)转为Quartz的周一至周六(2-7)
          return day === 7 ? 1 : day + 1;
        });
        weekdaysStr = quartzWeekdays.sort().join(",");
      } else {
        // 默认为星期一（Quartz的周一=2）
        weekdaysStr = "2";
      }

      form.value.cycleCron = `${seconds} ${minutes} ${hours} ? * ${weekdaysStr} *`;
      break;
  }
}

/** 计算下次任务执行时间 */
function calculateNextExecutionTime() {
  if (
    !form.value.cycleCron ||
    !form.value.nextCycleStartDate ||
    !form.value.cycleDate
  ) {
    form.value.nextCycleDate = null;
    return;
  }

  try {
    const startDate = dayjs(form.value.nextCycleStartDate);
    const endDate = form.value.nextCycleEndDate
      ? dayjs(form.value.nextCycleEndDate)
      : null;
    const now = dayjs();

    // 确保cycleTime是一个有效的时间对象
    let cycleTimeParts = form.value.cycleDate.split(":");
    let hours = parseInt(cycleTimeParts[0]);
    let minutes = parseInt(cycleTimeParts[1]);
    let seconds = cycleTimeParts.length > 2 ? parseInt(cycleTimeParts[2]) : 0;

    // 如果当前时间早于起始时间，下次执行时间判断需考虑收割频率
    if (now.isBefore(startDate)) {
      switch (form.value.cycleType) {
        case "immediate":
          // 立即收割，下次执行时间就是起始时间
          form.value.nextCycleDate = startDate.format("YYYY-MM-DD HH:mm:ss");
          return;

        case "daily":
          // 每日收割，下次执行时间是起始日期那天的收割时间
          const nextTime = startDate
            .hour(hours)
            .minute(minutes)
            .second(seconds);
          form.value.nextCycleDate = nextTime.format("YYYY-MM-DD HH:mm:ss");
          return;

        case "weekly":
          // 每周收割，需要找到起始日期后的第一个符合条件的星期
          const startDayOfWeek = startDate.day() === 0 ? 7 : startDate.day();
          const weekdays =
            form.value.cycleWeek && form.value.cycleWeek.length > 0
              ? [...form.value.cycleWeek].sort((a, b) => a - b)
              : [startDayOfWeek];

          // 获取下一个执行日
          let nextDayOfWeek = null;
          let daysToAdd = 0;

          // 先检查起始日期当天是否是执行日
          if (weekdays.includes(startDayOfWeek)) {
            nextDayOfWeek = startDayOfWeek;
            daysToAdd = 0;
          } else {
            // 找出起始日期之后的第一个执行日
            for (const day of weekdays) {
              if (day > startDayOfWeek) {
                nextDayOfWeek = day;
                daysToAdd = day - startDayOfWeek;
                break;
              }
            }

            // 如果没有找到，取下一周的第一个执行日
            if (nextDayOfWeek === null) {
              nextDayOfWeek = weekdays[0];
              daysToAdd = 7 - startDayOfWeek + nextDayOfWeek;
            }
          }

          // 计算下次执行时间
          const nextWeeklyTime = startDate
            .add(daysToAdd, "day")
            .hour(hours)
            .minute(minutes)
            .second(seconds);

          form.value.nextCycleDate = nextWeeklyTime.format(
            "YYYY-MM-DD HH:mm:ss"
          );
          return;
      }
    }

    // 如果有截止时间且当前时间已超过截止时间，则不再执行
    if (endDate && now.isAfter(endDate)) {
      form.value.nextCycleDate = "已超出收割截止时间";
      return;
    }

    // 基于当前时间和cron表达式计算下次执行时间
    // 这里根据不同的收割频率类型来计算
    let nextExecutionTime;

    switch (form.value.cycleType) {
      case "immediate":
        // 立即收割只执行一次
        if (now.isAfter(startDate)) {
          form.value.nextCycleDate = "任务已执行";
          return;
        } else {
          nextExecutionTime = startDate
            .hour(hours)
            .minute(minutes)
            .second(seconds);
        }
        break;

      case "daily":
        // 每日收割
        // 构建今天的执行时间点
        nextExecutionTime = now.hour(hours).minute(minutes).second(seconds);

        // 如果今天的执行时间已过，则设置为明天
        if (nextExecutionTime.isBefore(now)) {
          nextExecutionTime = nextExecutionTime.add(1, "day");
        }
        break;

      case "weekly":
        // 每周收割
        const weekdays =
          form.value.cycleWeek && form.value.cycleWeek.length > 0
            ? [...form.value.cycleWeek].sort((a, b) => a - b)
            : [startDate.day() === 0 ? 7 : startDate.day()];

        const currentDayOfWeek = now.day() === 0 ? 7 : now.day();

        // 找出下一个执行日
        let nextDayOfWeek = null;
        let daysToAdd = 0;

        // 今天的执行时间点
        const todayExecTime = now.hour(hours).minute(minutes).second(seconds);
        const todayIsExecutionDay = weekdays.includes(currentDayOfWeek);

        // 先检查今天是否是执行日，且时间未过
        if (todayIsExecutionDay && todayExecTime.isAfter(now)) {
          nextDayOfWeek = currentDayOfWeek;
          daysToAdd = 0;
        } else {
          // 检查本周剩余天数
          for (const day of weekdays) {
            if (day > currentDayOfWeek) {
              nextDayOfWeek = day;
              daysToAdd = day - currentDayOfWeek;
              break;
            }
          }

          // 如果本周没有合适的日期，取下周第一个执行日
          if (nextDayOfWeek === null) {
            nextDayOfWeek = weekdays[0];
            daysToAdd = 7 - currentDayOfWeek + nextDayOfWeek;
          }
        }

        nextExecutionTime = now
          .add(daysToAdd, "day")
          .hour(hours)
          .minute(minutes)
          .second(seconds);
        break;
    }

    // 检查是否在有效时间范围内
    if (endDate && nextExecutionTime.isAfter(endDate)) {
      form.value.nextCycleDate = "已超出收割截止时间";
    } else {
      form.value.nextCycleDate = nextExecutionTime.format(
        "YYYY-MM-DD HH:mm:ss"
      );
    }
  } catch (error) {
    console.error("计算下次执行时间出错:", error);
    form.value.nextCycleDate = null;
  }
}

/** 处理状态变更 */
function handleStateChange(row) {
  // 确保状态格式正确
  const oldState = row.state === "1" ? "2" : "1";

  // 检查是否已配置任务
  if (!row.id) {
    row.state = oldState;
    proxy.$modal.msgWarning("请先配置任务");
    return;
  }

  // 准备请求数据
  const data = {
    id: row.id,
    dataId: row.dataId,
    state: row.state,
  };

  // 调用后端API更新状态
  updateState(data)
    .then((response) => {
      proxy.$modal.msgSuccess(row.state === "1" ? "启用成功" : "停用成功");
      getList(); // 刷新列表，获取最新状态
    })
    .catch((error) => {
      // 操作失败，恢复之前的状态
      row.state = oldState;
      console.error("状态更新失败:", error);
      proxy.$modal.msgError("操作失败，请重试");
    });
}

/** 重置表单 */
const resetForm = () => {
  if (datasourceform.value) {
    datasourceform.value.resetFields();
  }
  form.value = {
    dataSource: {
      name: "",
      dataId: "",
      dataType: undefined,
      harvestType: undefined,
      ifaceUrl: "",
      ifaceParams: "",
      ftpUrl: "",
      ftpPort: "",
      ftpUser: "",
      ftpPwd: "",
      sourceType: "",
      describes: "",
      analysisRuleId: undefined,
    },
    cycleType: "immediate",
    cycleDate: "",
    cycleWeek: [],
    nextCycleStartDate: "",
    nextCycleEndDate: "",
    nextCycleDate: "",
    cycleCron: "",
    state: 1,
  };
};

/** 获取任务详情 */
const getTask = (id) => {
  return new Promise((resolve, reject) => {
    // 使用 setTimeout 延迟获取数据，避免 DOM 操作冲突
    setTimeout(() => {
      getSource(id)
        .then((response) => {
          if (response.code === 200) {
            try {
              // 填充表单数据
              form.value = response.data;

              // 确保dataSource字段存在
              if (!form.value.dataSource) {
                form.value.dataSource = {};
              }

              // 处理周期设置，转换后端格式为前端格式
              if (form.value.cycleType) {
                if (form.value.cycleType === "0") {
                  form.value.cycleType = "immediate";
                } else if (form.value.cycleType === "1") {
                  form.value.cycleType = "daily";
                } else if (form.value.cycleType === "2") {
                  form.value.cycleType = "weekly";
                }
              } else {
                // 如果没有设置cycleType，默认为每日收割
                form.value.cycleType = "daily";
              }

              // 如果没有cycleDate，设置默认值
              if (!form.value.cycleDate) {
                const now = new Date();
                form.value.cycleDate = `${now
                  .getHours()
                  .toString()
                  .padStart(2, "0")}:${now
                  .getMinutes()
                  .toString()
                  .padStart(2, "0")}:${now
                  .getSeconds()
                  .toString()
                  .padStart(2, "0")}`;
              }

              // 如果没有设置起始时间，设置默认值为明天
              if (!form.value.nextCycleStartDate) {
                form.value.nextCycleStartDate = dayjs()
                  .add(1, "day")
                  .format("YYYY-MM-DD 00:00:00");
              }

              // 处理周选择数据格式
              if (form.value.cycleWeek) {
                form.value.cycleWeek = form.value.cycleWeek
                  .split(";")
                  .map(Number);
              } else if (form.value.cycleType === "weekly") {
                // 如果是每周收割但没有设置具体星期，默认设置为星期一
                form.value.cycleWeek = [1];
              }

              // 处理状态数据格式
              if (form.value.state) {
                if (form.value.state === "1") {
                  form.value.state = 1;
                } else if (form.value.state === "2") {
                  form.value.state = 2;
                }
              }

              // 强制重新生成CRON表达式和计算下次执行时间
              generateCronExpression();
              calculateNextExecutionTime();

              resolve(response.data);
            } catch (error) {
              console.error("处理表单数据出错:", error);
              ElMessage.error("处理数据失败，请重试");
              reject(error);
            }
          } else {
            ElMessage.error(response.msg || "获取数据失败");
            reject(new Error(response.msg || "获取数据失败"));
          }
        })
        .catch((error) => {
          console.error("获取数据失败:", error);
          ElMessage.error("获取数据失败，请重试");
          reject(error);
        });
    }, 100);
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  if (queryRef.value) {
    queryRef.value.resetFields();
  }
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 批次表格多选框选中数据 */
const handleBatchSelectionChange = (selection) => {
  selectedBatchIds.value = selection.map((item) => item.batchId);
};

/** 下载批次文件 */
const downloadBatchFiles = (row) => {
  if (!row.localFileUri) {
    ElMessage.error("文件路径不存在，无法下载！");
    return;
  }

  const fileName =
    row.fileName || decodeURIComponent(row.localFileUri.split("/").pop());

  downloadFromMinio(row.localFileUri)
    .then((response) => {
      if (!response) {
        ElMessage.error("下载失败：无数据");
        return;
      }

      // 正常下载
      const blob = new Blob([response]);
      const link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(link.href);
    })
    .catch((error) => {
      ElMessage.error("下载失败");
    });
};

/** 初始加载 */
getList();
// getRuleBaseSelect();
</script>
<style lang="scss" scoped>
// 引入 base.scss 样式文件
@import "../../source/components/base.scss";

.my_drawer_title {
  display: flex;
  justify-content: space-between;
}

.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
  }

  .btn_add {
    background-color: rgb(0, 120, 212);
  }
}

// 单独设置右侧抽屉的样式，防止搜索部分被遮挡。与设计图不同～
:deep(.drawer-content-new) {
  margin-top: 0;
  border-top: 1px solid #bccde0;
  max-height: calc(100vh - 85px);
}

.operation-btn {
  display: inline-block;
  margin: 0 5px;
  color: #0076d0 !important;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
}

.form-container {
  border-radius: 2px;
  border: 1px solid rgb(221, 229, 235);
  background-color: rgb(255, 255, 255);
  overflow: hidden;

  :deep(.el-form-item--small) {
    margin-bottom: 0;
  }

  :deep(.el-input) {
    width: 335px;
    height: 34px;
  }

  :deep(.el-select) {
    width: 335px;
    height: 34px;
  }

  :deep(.el-input__wrapper) {
    height: 32px;
  }

  :deep(.el-select--small .el-select__wrapper) {
    height: 32px;
  }

  :deep(.el-form-item__content) {
    padding-top: 6px !important;
    padding-left: 9px !important;
    padding-right: 6px !important;
    background-color: transparent;
  }

  :deep(.form-item-52 .el-form-item__label) {
    height: 52px !important;
    line-height: 52px !important;
  }

  :deep(.form-item-54 .el-form-item__label) {
    height: 54px !important;
    line-height: 54px !important;
  }

  :deep(.form-item-64 .el-form-item__label) {
    height: 64px !important;
    line-height: 64px !important;
  }

  // 确保标签区域背景色连续
  :deep(.el-form-item) {
    display: flex;

    .el-form-item__label {
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 0, 0);
      font-weight: bold;
      padding-left: 15px !important;
      padding-top: 6px !important;
      padding-right: 0 !important;
      height: 40px;
      line-height: 40px;
      background-color: #f7f7f7;
      width: 110px;
      flex-shrink: 0;

      em {
        font-style: normal;
        color: #eb3037;
      }
    }
  }

  // 确保middle-box不受表单样式影响
  .middle-box {
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
  }

  :deep(
      .el-form-item.is-required:not(.is-no-asterisk).asterisk-left
        > .el-form-item__label-wrap
        > .el-form-item__label:before
    ),
  :deep(
      .el-form-item.is-required:not(.is-no-asterisk).asterisk-left
        > .el-form-item__label:before
    ) {
    display: none;
  }

  // textarea项的特殊样式
  :deep(.textarea-item) {
    .el-form-item__label {
      height: 196px !important;
      align-items: flex-start;
      padding-top: 6px !important;
    }
  }
}

.middle-box {
  display: flex;
  flex-direction: row;

  .middle-box-left {
    width: 110px;
    background-color: #f7f7f7 !important;
    flex-shrink: 0;
  }

  .middle-box-none {
    height: 20px;
    flex: 1;
  }

  .middle-box-right {
    height: 80px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      border: 1px solid rgb(219, 219, 219);
      border-radius: 2px;
      background-color: rgb(255, 255, 255);
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 120, 212);
      padding: 3px 11px;
      cursor: pointer;
    }
  }
}
</style>
