<template>
  <div class="app-container">
    <!-- 背景框框 -->
    <viewBody title="被引数据管理">
      <template #content>
        <!-- 使用FormSearch组件替换搜索菜单 -->
        <FormSearch
          ref="formSearchRef"
          v-model="queryParams"
          :formItems="formItems"
          @search="handleQuery"
          @reset="resetQuery"
        >
          <template #btn>
            <el-button  type="primary"
              class="my_primary_btn"
              icon="Delete"
              @click="handleDelete()">
              删除</el-button>
          </template>
        </FormSearch>

        <!-- 使用TableForm组件替换原有表格 -->
        <TableForm
          ref="tableFormRef"
          :columns="columns"
          :tableData="citedList"
          :total="total"
          :showOtherColumn="true"
          tableotherColumnLabel="操作"
          :isShowCount="true"
          :isShowSearchQuery="false"
          :isShowSelection="true"
          :columnsRightWidth="columnsRightWidth"
          :showIndex="false"
          @selectionChange="handleSelectionChange"
        >
          <!-- 操作按钮插槽 -->
          <template #otherOperation="{ row }">
            <span class="operation-btn" @click.stop="handleEdit(row)">
              编辑
            </span>
            <span class="operation-btn" @click.stop="handleDetail(row)">
              详情
            </span>
          </template>
          <!-- 分页插槽 -->
          <template #pagination>
            <MyPagination
              v-if="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </template>
          <!-- 抽屉插槽 -->
          <template #drawer>
            <MyDrawer ref="drawerRef">
              <template #header="{ titleId, titleClass }">
                <div :id="titleId" :class="titleClass" class="my_drawer_title">
                  <span class="active">{{ drawer.title }}</span>
                  <div class="my_drawer_title_right">
                    <span class="btn_add" @click="submitForm">保存</span>
                    <span class="btn_cancel" @click="cancel">取消</span>
                  </div>
                </div>
              </template>
              <template #form>
                <RightDrawForm ref="rightFormRef" :modal="form" :type=drawer.type></RightDrawForm>
              </template>
            </MyDrawer>
          </template>
        </TableForm>
      </template>
    </viewBody>
  </div>
</template>

<script setup name="dataSource">
import {
  listSource,
  getSource,
  addSource,
  updateSource,
  updateState,
  getBatchList,
  downloadFromMinio,
  parseMappingBatch,
} from "@/api/task/task";
import { queryRuleBaseSelect } from "@/api/processingtool/ruleBase";
import { listQuoteArticle, deleteBatchQuoteArticle, updateQuoteArticle } from "@/api/citationManager/quoteArticle";
import { useRouter } from "vue-router";
import { nextTick } from "vue";
import dayjs from "dayjs";
import MyPagination from "@/components/Pagination/new.vue";
import FormSearch from "@/views/citationManager/components/FormSearch.vue";
import TableForm from "@/views/citationManager/components/TableForm.vue";
import MyDrawer from "@/views/citationManager/components/MyDrawer.vue";
import RightDrawForm from "./components/RightDrawForm.vue";
import viewBody from "@/views/citationManager/components/viewBody.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { DICTIONARIES, SAMPLE_DATA,TOTAL } from "@/views/citationManager/components/citationData";

const { proxy } = getCurrentInstance();
const router = useRouter();

// 存储业务字典数据
const bizDict = reactive({
  ruleBaseSelectOptions: [],
});
const columnsRightWidth = ref('120');
const citedList = ref(SAMPLE_DATA);
const drawerRef = ref(null);
const loading = ref(true);
const total = ref(0);
const title = ref("");
const tableFormRef = ref(null);
const formSearchRef = ref(null);
const batchTableFormRef = ref(null);
const currentEditRow = ref(null)
// 添加选中行跟踪变量
const selectedRows = ref([]);
// 在 script setup 中添加表单引用
const datasourceform = ref(null);
const rightFormRef = ref(null);
//引文级别字典
const results = reactive([
  { label: "疑似", value: 1 },
  { label: "挂接", value: 2 },
  { label: "关联不上", value: 3 },
]);

// 搜索表单配置
const formItems = ref([
  {
    prop: "articleId",
    component: "el-input",
    props: {
      placeholder: "被引文献ID",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    prop: "artilceTitle",
    component: "el-input",
    props: {
      placeholder: "题名",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    prop: "journalTitle",
    component: "el-input",
    props: {
      placeholder: "期刊题名",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    prop: "searchCreatTime",
    component: "el-date-picker",
    props: {
      type: 'daterange',
      placeholder: "请选择时间范围",
      clearable: true,
      style: { width: "280px" },
      format: 'YYYY-MM-DD', // 显示格式（可选）
      valueFormat: 'YYYY-MM-DD'
    },
    onChange: (val) => {
      console.log(val);
      if (val) {
        console.log(val);
        // val 是 [开始日期, 结束日期] 数组（Date 对象或字符串，取决于 valueFormat）
        queryParams.value.startCreateTime = val[0]; 
        queryParams.value.endCreateTime = val[1];
      } else {
        console.log(val);
        // 清空时重置两个时间字段
        queryParams.value.startCreateTime = null;
        queryParams.value.endCreateTime = null;
      }
    },
    position: "right_wrapper",
  },
]);

// 表格列配置
const columns = ref([
  { prop: "refId", label: "被引文献ID", width: "120" },
  { prop: "refTitle", label: "题名", width: "200" },
  { prop: "refJournalTitle", label: "期刊题名", width: "200" },
  { prop: "issn", label: "ISSN", width: "120" },
  { prop: "dateVolIssue", label: "年/卷/期", width: "120" },
  { prop: "citedCount", label: "被引用次数", width: "100" },
  { prop: "pageRange", label: "页码范围", width: "140" },
  { prop: "updateTime", label: "更新时间", width: "180" },
]);
// 新增批次数据相关变量
const selectedTask = ref(null);
const batchList = ref([]);
const batchLoading = ref(false);
const batchTotal = ref(0);
const selectedBatchIds = ref([]); // 存储选中的批次ID
const batchQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  sourceId: null,
});

const drawer = reactive({
  visible: false,
  title: '',
  type: ''
});
const data = reactive({
  form: {
    dataSource: {},
    cycleType: "daily", // 默认设置为每日收割
    cycleDate: null,
    cycleWeek: [1], // 默认设置为周一
    nextCycleStartDate: null, // 默认设置为明天
    nextCycleEndDate: null,
    nextCycleDate: null,
    cycleCron: null,
    ifaceUrl: null,
    ifaceParams: null,
    ftpUrl: null,
    ftpPort: null,
    ftpUser: null,
    ftpPwd: null,
    state: 1,
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    articleId: '',
    artilceTitle: '',
    journalTitle: '',
    citeType: "2",
    startCreateTime: '',
    endCreateTime: '',
    searchCreatTime: '',
  },
  rules: {
    cycleType: [
      { required: true, message: "收割频率不能为空", trigger: "change" },
    ],
    cycleDate: [
      { required: true, message: "收割时间不能为空", trigger: "change" },
    ],
    cycleWeek: [
      { required: true, message: "请选择执行星期", trigger: "change" },
    ],
    nextCycleStartDate: [
      { required: true, message: "收割起始时间不能为空", trigger: "change" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

async function getRuleBaseSelect() {
  // , ruleStatus: 'COMPLETED'
  const { data } = await queryRuleBaseSelect({ ruleType: "ANALYSIS" });
  console.log("获取解析规则字典信息", data);
  bizDict.ruleBaseSelectOptions = data;
}
//详情按钮的回调
/** 处理按钮操作 */
function handle(row) {
  // 跳转规则结果详情页
  console.log("row", row)
  router.push('/detail/${id}');
}
/** 主表格序号方法 */
function indexMethod(index) {
  // 计算序号，考虑分页
  const pageSize = queryParams.value.pageSize;
  const pageNum = queryParams.value.pageNum;
  return (pageNum - 1) * pageSize + index + 1;
}

/** 批次表格序号方法 */
function batchIndexMethod(index) {
  // 计算序号，考虑分页
  const pageSize = batchQueryParams.value.pageSize;
  const pageNum = batchQueryParams.value.pageNum;
  return (pageNum - 1) * pageSize + index + 1;
}

/** 处理解析映射按钮点击 */
function handleAnalysisMapping() {
  // 检查是否有选中的行和批次数据
  if (!selectedTask.value) {
    proxy.$modal.msgWarning("请先选择一个数据源");
    return;
  }

  if (!batchList.value || batchList.value.length === 0) {
    proxy.$modal.msgWarning("没有可用的批次数据");
    return;
  }

  // 检查是否有选中的批次
  if (!selectedBatchIds.value || selectedBatchIds.value.length === 0) {
    proxy.$modal.msgWarning("请选择需要解析映射的批次");
    return;
  }

  // 显示确认对话框
  proxy.$modal
    .confirm(
      `确定要对选中的 ${selectedBatchIds.value.length} 个批次执行解析映射操作吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    )
    .then(() => {
      // 用户确认，调用解析映射接口
      proxy.$modal.loading("正在执行解析映射操作，请稍候...");

      parseMappingBatch(selectedBatchIds.value)
        .then((response) => {
          proxy.$modal.closeLoading();
          if (response.code === 200) {
            proxy.$modal.msgSuccess("解析映射操作成功");
            // 刷新批次数据
            getBatchListById();
          } else {
            proxy.$modal.msgError(`解析映射操作失败: ${response.msg}`);
          }
        })
        .catch((error) => {
          proxy.$modal.closeLoading();
          console.error("解析映射操作失败:", error);
          proxy.$modal.msgError("解析映射操作失败，请重试");
        });
    })
    .catch(() => {
      // 用户取消操作
    });
}
//详情按钮的回调
/** 查看按钮操作 */
function handleDetail(row) {
  // 跳转规则结果详情页
  console.log("row", row)
  router.push({
    name: 'CitedDataDetail'
  });
}
// 编辑按钮的回调
function handleEdit(row) {
  drawer.type = 'edit';
  drawer.title = '编辑被引数据';
  currentEditRow.value = JSON.parse(JSON.stringify(row));
  drawerRef.value.openDrawer();
  setTimeout(() => {
    rightFormRef.value?.setFormData(currentEditRow.value);
  }, 100);
}
/** 跳转到收割任务列表页面 */
function goToTaskList() {
  router.push({ path: "/collection/batch" });
}

/** 跳转到解析映射列表页 */
function goToMappingList() {
  router.push({ path: "/obtainConvert/parsingMappingTask" });
}

/** 查询岗位列表 */
function getList() {
  loading.value = true;
  if (queryParams.value.searchCreatTime) {
    queryParams.value.startCreateTime = queryParams.value.searchCreatTime[0] + " 00:00:00";
    queryParams.value.endCreateTime = queryParams.value.searchCreatTime[1] + " 23:59:59";
  }
  // 调用后端接口，传递查询参数（包含分页和条件）
   listQuoteArticle(queryParams.value)
     .then(res => {
       // 假设接口返回结构为 { code: 200, data: { list: [], total: 100 } }
       if (res.code === 200) {
        citedList.value = res.data.contentData;
        citedList.value.map(item => {
          if (item.year && item.volume && item.issue) {
            item.dateVolIssue = item.year + '/' + item.volume + '/' + item.issue;
          }else if (item.year) {
            item.dateVolIssue = item.year;
          }else if (item.volume) {
            item.dateVolIssue = '/' + item.volume;
          } else if (item.issue) {
            item.dateVolIssue = '/' + item.issue;
          }else {
            item.dateVolIssue = '无';
          }
          if (!item.pageRange){
            if (item.firstPage && item.lastPage) {
              item.pageRange = item.firstPage + '-' + item.lastPage;
            }else if (item.firstPage) {
              item.pageRange = item.firstPage;
            }else if (item.lastPage) {
              item.pageRange = item.lastPage;
            }
          }
          return item;
        }); 
        total.value = res.data.totalSize; 
       } else {
         ElMessage.error(res.message || '获取数据失败');
       }
     })
     .catch(err => {
       console.error('请求出错：', err);
       ElMessage.error('网络异常，请稍后重试');
     })
     .finally(() => {
       loading.value = false; // 关闭加载状态
     });
}

// 格式化日期时间
function formatDateTime(dateTime) {
  if (!dateTime) return "";

  // 处理字符串类型的日期时间
  if (typeof dateTime === "string") {
    // 替换T为空格，移除毫秒部分和时区信息
    return dateTime.replace("T", " ").replace(/\.\d+(\+|\-).*$/, "");
  }

  return dateTime;
}

// 获取字典标签
function getDictLabel(options, value) {
  if (!value || !options?.length) return "";
  return options.find((opt) => opt.value === value)?.label ?? value;
}

/** 打开抽屉 */
const handleUpdate = (row) => {
  if (!drawerRef.value) {
    ElMessage.error("抽屉组件未初始化");
    return;
  }

  title.value = row ? "修改收割任务" : "添加收割任务";
  resetForm();

  // 使用 nextTick 确保 DOM 已更新
  nextTick(() => {
    try {
      // 先打开抽屉
      drawerRef.value.openDrawer();

      // 打开后再获取数据
      setTimeout(() => {
        if (row) {
          const id = row.dataId;
          getTask(id);
        }
      }, 100);
    } catch (error) {
      console.error("打开抽屉失败:", error);
    }
  });
};

/** 取消按钮 */
const cancel = () => {
  if (!drawerRef.value) return;

  try {
    // 先重置表单
    resetForm();
    // 关闭抽屉
    drawerRef.value.closeDrawer();
  } catch (error) {
    console.error("关闭抽屉失败:", error);
  }
};

/** 表单提交 */
const submitForm = async() => {
  if(drawer.type==='edit'){
    try {
      console.log(rightFormRef.value)
      // 2. 子组件校验
      await rightFormRef.value.validateForm()
      // 3. 拿表单数据
      const data = rightFormRef.value.getFormData()
      console.log("获取的表单数据",data);

      // 4. 调接口
      await updateQuoteArticle(data)

      ElMessage.success('修改成功')
      getList();
    } catch (e) {
      console.log(e)
      // 校验失败或接口报错
      ElMessage.error(e?.response?.data?.msg || '操作失败')
    }
  }
};

/** 行点击事件 */
const handleRowClick = async (row) => {
  try {
    // 先设置选中任务
    selectedTask.value = row;

    // 重置批次查询参数
    batchQueryParams.value = {
      pageNum: 1,
      pageSize: 10,
      sourceId: row.dataId,
    };

    // 等待DOM更新完成
    await nextTick();

    // 获取批次数据
    await getBatchListById();
  } catch (error) {
    console.error("处理行点击事件失败:", error);
  }
};

/** 获取批次列表 */
const getBatchListById = (page) => {
  if (!selectedTask.value) return;

  if (page) {
    batchQueryParams.value.pageNum = page.pageNum;
    batchQueryParams.value.pageSize = page.pageSize;
  }

  console.log("获取批次数据，参数:", batchQueryParams.value);
  batchLoading.value = true;

  // 使用 Promise 包装请求
  return new Promise((resolve) => {
    getBatchList(batchQueryParams.value)
      .then((response) => {
        console.log("获取批次数据成功:", response);

        // 处理数据，转换字典项和时间格式
        const formattedData = (response.rows || []).map((row) => ({
          ...row,
          // 处理时间格式
          createTime: formatDateTime(row.createTime),
          endTime: formatDateTime(row.endTime),
          // 处理字典项
          dataType: getDictLabel(storage_data_type.value, row.dataType),
          status: getDictLabel(storage_last_state.value, row.status),
          // 格式化文件大小
          fileSize: row.fileSize ? Number(row.fileSize).toFixed(2) : "0",
        }));

        batchList.value = formattedData;
        batchTotal.value = response.total;
        batchLoading.value = false;
        resolve(response);
      })
      .catch((error) => {
        console.error("获取批次列表失败:", error);
        batchLoading.value = false;
        batchList.value = [];
        batchTotal.value = 0;
        resolve(null);
      });
  });
};

/** 验证开始和结束时间 */
function validateDateRange() {
  if (form.value.nextCycleStartDate && form.value.nextCycleEndDate) {
    const startDate = new Date(form.value.nextCycleStartDate);
    const endDate = new Date(form.value.nextCycleEndDate);

    if (endDate <= startDate) {
      proxy.$modal.msgError("收割截止时间必须大于收割起始时间");
      form.value.nextCycleEndDate = null;
    }
  }
}
/** 重置表单 */
const resetForm = () => {
  if (datasourceform.value) {
    datasourceform.value.resetFields();
  }
  form.value = {
    dataSource: {
      name: "",
      dataId: "",
      dataType: undefined,
      harvestType: undefined,
      ifaceUrl: "",
      ifaceParams: "",
      ftpUrl: "",
      ftpPort: "",
      ftpUser: "",
      ftpPwd: "",
      sourceType: "",
      describes: "",
      analysisRuleId: undefined,
    },
    cycleType: "immediate",
    cycleDate: "",
    cycleWeek: [],
    nextCycleStartDate: "",
    nextCycleEndDate: "",
    nextCycleDate: "",
    cycleCron: "",
    state: 1,
  };
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  const initialParams = {
    articleId: '',
    artilceTitle: '',
    journalTitle: '',
    citeType: "2",
    startCreateTime: '',
    endCreateTime: '',
    searchCreatTime: '',
    pageNum: 1, 
    pageSize: 10
  };
  // 更新queryParams的值
  Object.assign(queryParams.value, initialParams);
  if (formSearchRef.value?.resetFields) {
    formSearchRef.value.resetFields()
  }
  getList();
};

const handleSelectionChange = (rows) => {
  selectedRows.value = rows;
};
/** 批量删除按钮操作 */
const handleDelete = (row) => {
 if (!selectedRows.value || selectedRows.value.length === 0) {
    ElMessage.warning("请选择需要删除的记录");
    return;
  }
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedRows.value.length} 条记录吗？`,
    "删除确认",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(() => {
    // 获取选中行的ID数组
    const ids = selectedRows.value.map(row => row.id);
    const idsStr = ids.join(",");
    // 调用批量删除API
    deleteBatchQuoteArticle(idsStr).then((res) => {
      if (res.code === 200) {
        ElMessage.success("删除成功");
        // 刷新列表
        getList();
        // 清空选中状态
        selectedRows.value = [];
        if (tableFormRef.value) {
          tableFormRef.value.clearSelection();
        }
      }
    });
  }).catch(() => {
    // 用户取消操作
  });
  /* deleteData(row.id).then((res) => {
    if (res.code == 200) {
      ElMessage.success("删除成功");
      getList();
    }
  }); */
};
/** 初始加载 */
getList();
// getRuleBaseSelect();
</script>
<style lang="scss" scoped>
// 引入 base.scss 样式文件
@import "../../source/components/base.scss";

.my_drawer_title {
  display: flex;
  justify-content: space-between;
}

.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
  }

  .btn_add {
    background-color: rgb(0, 120, 212);
  }
}

// 单独设置右侧抽屉的样式，防止搜索部分被遮挡。与设计图不同～
:deep(.drawer-content-new) {
  margin-top: 0;
  border-top: 1px solid #bccde0;
  max-height: calc(100vh - 85px);
}

.operation-btn {
  display: inline-block;
  margin: 0 5px;
  color: #0076d0 !important;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
}

.form-container {
  border-radius: 2px;
  border: 1px solid rgb(221, 229, 235);
  background-color: rgb(255, 255, 255);
  overflow: hidden;

  :deep(.el-form-item--small) {
    margin-bottom: 0;
  }

  :deep(.el-input) {
    width: 335px;
    height: 34px;
  }

  :deep(.el-select) {
    width: 335px;
    height: 34px;
  }

  :deep(.el-input__wrapper) {
    height: 32px;
  }

  :deep(.el-select--small .el-select__wrapper) {
    height: 32px;
  }

  :deep(.el-form-item__content) {
    padding-top: 6px !important;
    padding-left: 9px !important;
    padding-right: 6px !important;
    background-color: transparent;
  }

  :deep(.form-item-52 .el-form-item__label) {
    height: 52px !important;
    line-height: 52px !important;
  }

  :deep(.form-item-54 .el-form-item__label) {
    height: 54px !important;
    line-height: 54px !important;
  }

  :deep(.form-item-64 .el-form-item__label) {
    height: 64px !important;
    line-height: 64px !important;
  }

  // 确保标签区域背景色连续
  :deep(.el-form-item) {
    display: flex;

    .el-form-item__label {
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 0, 0);
      font-weight: bold;
      padding-left: 15px !important;
      padding-top: 6px !important;
      padding-right: 0 !important;
      height: 40px;
      line-height: 40px;
      background-color: #f7f7f7;
      width: 110px;
      flex-shrink: 0;

      em {
        font-style: normal;
        color: #eb3037;
      }
    }
  }

  // 确保middle-box不受表单样式影响
  .middle-box {
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
  }

  :deep(
      .el-form-item.is-required:not(.is-no-asterisk).asterisk-left
        > .el-form-item__label-wrap
        > .el-form-item__label:before
    ),
  :deep(
      .el-form-item.is-required:not(.is-no-asterisk).asterisk-left
        > .el-form-item__label:before
    ) {
    display: none;
  }

  // textarea项的特殊样式
  :deep(.textarea-item) {
    .el-form-item__label {
      height: 196px !important;
      align-items: flex-start;
      padding-top: 6px !important;
    }
  }
}

.middle-box {
  display: flex;
  flex-direction: row;

  .middle-box-left {
    width: 110px;
    background-color: #f7f7f7 !important;
    flex-shrink: 0;
  }

  .middle-box-none {
    height: 20px;
    flex: 1;
  }

  .middle-box-right {
    height: 80px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      border: 1px solid rgb(219, 219, 219);
      border-radius: 2px;
      background-color: rgb(255, 255, 255);
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 120, 212);
      padding: 3px 11px;
      cursor: pointer;
    }
  }
}
</style>
