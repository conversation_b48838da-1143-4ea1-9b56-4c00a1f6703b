<template>
  <el-card class="detail-panel" shadow="never" :style="{ height: height + 'px' }">
    <template #header>
      <div class="detail-header">
        <span>{{ title }}</span>
        <div class="header-actions">
          <el-button type="text" size="small" @click="handleGlobalEdit" :disabled="hasEditingGroup" class="edit-btn">
            <el-icon>
              <Edit/>
            </el-icon>
            编辑
          </el-button>
        </div>
      </div>
    </template>

    <div class="detail-content" v-if="data">
      <!-- 校验对象数据展示区域 -->
      <div class="object-data-section">
        <div class="section-header">
          <div class="section-title">属性值：{{ data.attributes }}</div>
        </div>
        <div class="object-data-content">
          {{ data }}
        </div>
      </div>
    </div>

    <div class="no-data" v-else>
      <el-empty description="暂无数据" :image-size="80"/>
    </div>
  </el-card>
</template>

<script setup>
import {ref, computed, watch, onMounted} from "vue";
import {Edit} from "@element-plus/icons-vue";
import {ElMessageBox, ElMessage} from "element-plus";

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  height: {
    type: Number,
    default: 500,
  },
  title: {
    type: String,
    default: "title",
  },
});

// 获取规则ID数据
const pathId = computed(() => {
  return props.data.id || "";
});


// 表单分组配置 - 通过接口动态获取
const formGroups = ref([]);
const groupEditStates = ref([]);

// 校验对象数据
const objectData = ref({});
// 下拉选项配置
const selectOptions = ref({
  checkType: [
    {label: "必填校验", value: "required"},
    {label: "格式校验", value: "format"},
    {label: "长度校验", value: "length"},
    {label: "范围校验", value: "range"},
    {label: "自定义校验", value: "custom"},
  ],
  priority: [
    {label: "高", value: "high"},
    {label: "中", value: "medium"},
    {label: "低", value: "low"},
  ],
});

// 响应式数据
const isEditing = ref(false);
const formData = ref({});
const originalData = ref({});

const hasEditingGroup = computed(() => {
  return groupEditStates.value.some((state) => state);
});

// 分组编辑相关方法
function handleGlobalEdit() {
  console.log("全局编辑");
}

// 监听props.data变化，触发数据获取
watch(
    () => props.data,
    async (newData, oldData) => {
      await fetchDetailData(newData);
      // todo页面逻辑实现
      console.log("DetailPanel: 数据变化，触发接口调用", newData.id);
      isEditing.value = false;
    },
    {immediate: true, deep: true},
);

// 组件挂载时初始化分组配置
onMounted(() => {
  if (formGroups.value.length === 0) {
    fetchDetailData({});
  }
});

// 数据获取接口
async function fetchDetailData() {
  try {
    console.log("DetailPanel: 数据变化，触发接口调用", data);
  } catch (error) {
    console.error("获取详细数据失败:", error);
  }
}

// 编辑处理
function handleEdit() {
  isEditing.value = true;
  console.log("开始编辑模式");
}

// 保存处理
async function handleSave() {
  try {
    console.log("保存数据:", formData.value);
    originalData.value = JSON.parse(JSON.stringify(formData.value));
    isEditing.value = false;
    console.log("数据保存成功");
  } catch (error) {
    console.error("保存数据时发生错误:", error);
  }
}

// 取消处理
function handleCancel() {
  formData.value = JSON.parse(JSON.stringify(originalData.value));
  isEditing.value = false;
  console.log("取消编辑，恢复原始数据");
}

// 确认删除
function confirmDelete() {
  ElMessageBox.confirm("确定要删除这条数据吗？删除后无法恢复。", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
      .then(() => {
        handleDelete();
      })
      .catch(() => {
        console.log("用户取消删除");
      });
}

// 删除数据
async function handleDelete() {
  try {
    console.log("删除数据:", formData.value);
    formData.value = {};
    originalData.value = {};
    isEditing.value = false;
  } catch (error) {
    console.error("删除数据时发生错误:", error);
  }
}
</script>

<style scoped lang="scss">
.detail-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  :deep(.el-card__header) {
    flex-shrink: 0;
  }

  :deep(.el-card__body) {
    flex: 1;
    overflow: hidden;
    padding: 0;
    display: flex;
    flex-direction: column;
  }

  .detail-header {
    font-weight: 600;
    color: #303133;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      .edit-btn {
        padding: 4px 8px;
        color: #409eff;

        &:hover {
          background-color: #ecf5ff;
          color: #337ecc;
        }

        &:disabled {
          color: #c0c4cc;
          cursor: not-allowed;
        }

        .el-icon {
          font-size: 16px;
        }
      }
    }
  }

  .detail-content {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
    box-sizing: border-box;
    min-height: 0;

    // 校验对象数据展示区域
    .object-data-section {
      margin-bottom: 24px;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      background-color: #ffffff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      .section-header {
        padding: 16px 20px;
        background-color: #f0f9ff;
        border-bottom: 1px solid #e4e7ed;
        border-radius: 6px 6px 0 0;
      }

      .section-title {
        font-weight: 600;
        color: #303133;
        font-size: 12px; // 调整标题字体大小
        margin: 0;
      }

      .object-data-content {
        padding: 16px 20px;

        .field-name {
          font-weight: 500;
          color: #303133;
        }

        .required-tag {
          margin-left: 8px;
        }

        .field-value {
          color: #606266;
        }
      }
    }

    // 校验规则分组展示区域
    .validation-group {
      margin-bottom: 24px;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      background-color: #ffffff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      &:last-child {
        margin-bottom: 0;
      }
    }

    .group-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      background-color: #f8f9fa;
      border-bottom: 1px solid #e4e7ed;
      border-radius: 6px 6px 0 0;

      .group-title {
        font-weight: 600;
        color: #303133;
        font-size: 16px;
        margin: 0;
      }

      .group-subtitle {
        font-size: 12px;
        color: #909399;
        margin-left: 10px;
      }
    }

    .group-actions {
      display: flex;
      gap: 8px;
    }

    .group-content {
      padding: 16px 20px;

      .group-properties {
        margin-bottom: 16px;
        padding: 12px;
        background-color: #f8fafe;
        border-radius: 4px;
        border-left: 3px solid #409eff;

        .properties-title {
          font-size: 14px;
          font-weight: 500;
          color: #409eff;
          margin-bottom: 8px;
        }

        .properties-list {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;

          .property-tag {
            background-color: #ecf5ff;
            color: #409eff;
            border: 1px solid #b3d8ff;
          }
        }
      }

      .add-row-btn {
        margin-top: 12px;
        text-align: center;
      }
    }
  }

  .no-data {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
    min-height: 0;
  }

  // 表格样式优化
  :deep(.el-table) {
    border-radius: 4px;
  }

  :deep(.el-table th) {
    background-color: #f5f7fa;
    font-weight: 600;
  }

  :deep(.el-table td) {
    padding: 8px 0;
  }

  :deep(.el-input__inner),
  :deep(.el-textarea__inner) {
    border-radius: 4px;
  }

  :deep(.el-select) {
    width: 100%;
  }
}
</style>
