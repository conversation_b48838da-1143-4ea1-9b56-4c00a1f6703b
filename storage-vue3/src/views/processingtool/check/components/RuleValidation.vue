<template>
  <viewBody title="规则验证">
    <template #content>
      <!-- 查询表单 -->
      <FormSearch
        v-model="queryParams"
        :formItems="formItems"
        searchBtnName="查询"
        @search="handleQuery"
        @reset="resetQuery"
      >
        <!-- 自定义按钮插槽 -->
        <template #btn>
          <el-button type="primary" plain icon="Plus" @click="handleValidRule"
            >验证规则</el-button
          >
          <el-button type="warning" plain icon="Download" @click="handleExport"
            >导出数据</el-button
          >
        </template>
      </FormSearch>

      <!-- 表格部分 -->
      <TableForm
        ref="tableFormRef"
        v-loading="loading"
        :total="total"
        :columns="columns"
        :tableData="dataList"
        :isShowSearchQuery="false"
        :isShowCount="true"
        :show-index="true"
        :show-note-column="false"
        :isShowSelection="true"
        :showEditBtn="false"
        :showOtherColumn="true"
        tableotherColumnLabel="操作"
        @selection-change="handleSelectionChange"
        @cellClick="cellClick"
        @handleClick="handleEdit"
      >
        <template #otherOperation="{ row }">
          <!-- 固定显示的主要操作按钮 -->
          <el-button
            v-if="row.status === 'SUCCESS'"
            link
            type="primary"
            icon="View"
            @click.stop="handleLook(row.ruleId, row.id)"
            v-hasPermi="['interfaces:interface:view']"
            size="small"
            >查看</el-button
          >

          <el-button
            link
            type="primary"
            icon="View"
            @click.stop="handleValidLog(row)"
            v-hasPermi="['interfaces:interface:view']"
            size="small"
            >日志</el-button
          >

          <el-button
            v-if="row.type === 1"
            link
            type="primary"
            icon="View"
            @click.stop="handleValidDelete(row)"
            v-hasPermi="['interfaces:interface:view']"
            size="small"
            >删除</el-button
          >

          <el-button
            v-if="row.status === 'FAILURE'"
            link
            type="primary"
            icon="View"
            @click.stop="handleValidFailMsg(row)"
            v-hasPermi="['interfaces:interface:view']"
            size="small"
            >失败原因</el-button
          >
        </template>

        <!-- 自定义分页插槽 -->
        <template #pagination>
          <MyPagination
            v-show="total > 0"
            :total="total"
            :page="queryParams.pageNum"
            :pageSize="queryParams.pageSize"
            @pagination="getList"
          />
        </template>
      </TableForm>
    </template>
  </viewBody>

  <!-- 验证规则弹窗 -->
  <RuleCheckValidationDialog :modal="validationModal" @success="getList" />
</template>

<script setup>
import { reactive, ref, computed, getCurrentInstance } from "vue";
import { useRouter } from "vue-router";
import viewBody from "@/components/view/viewBody.vue";
import FormSearch from "@/components/Business/FormSearch/index.vue";
import TableForm from "@/components/Business/TableForm/index.vue";
import MyPagination from "@/components/Pagination/new.vue";
import RuleCheckValidationDialog from "./RuleCheckValidationDialog.vue";
import { queryRuleBaseSelect } from "@/api/processingtool/ruleBase.js";
import {
  deleteRuleCheckValidation,
  listRuleCheckValidation,
} from "@/api/processingtool/ruleCheckValidation.js";

const { proxy } = getCurrentInstance();
const router = useRouter();

/**数据字典*/
const sysDict = reactive({
  ...proxy.useDict(
    "storage_data_type",
    "storage_rule_valid_status",
    "storage_rule_phase"
  ),
});

/**业务字典*/
const bizDict = reactive({ ruleBaseSelectOptions: [] });

const dataList = ref([]);
const loading = ref(true);
const total = ref(1);
const multiple = ref(true);
const ids = ref([]);
const selectRows = ref([]);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  ruleType: "VALIDATE",
  name: "",
  phase: "",
  status: "",
  searchCreatTime: "",
});

// 搜索表单配置
const formItems = computed(() => [
  {
    label: "规则名称",
    prop: "name",
    component: "el-input",
    props: {
      placeholder: "规则名称",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    label: "启用环节",
    prop: "phase",
    component: "el-select",
    props: {
      placeholder: "启用环节",
      clearable: true,
      style: { width: "200px" },
    },
    children: () =>
      sysDict.storage_rule_phase.map((dict) => ({
        value: dict.value,
        label: dict.label,
      })),
  },
  {
    label: "处理状态",
    prop: "status",
    component: "el-select",
    props: {
      placeholder: "处理状态",
      clearable: true,
      style: { width: "200px" },
    },
    children: () =>
      sysDict.storage_rule_valid_status.map((dict) => ({
        value: dict.value,
        label: dict.label,
      })),
  },
  // {
  //   label: "创建时间",
  //   prop: "searchCreatTime",
  //   component: "el-date-picker",
  //   props: {
  //     type: "daterange",
  //     "range-separator": "-",
  //     "start-placeholder": "开始日期",
  //     "end-placeholder": "结束日期",
  //     "value-format": "YYYY-MM-DD",
  //     style: { width: "308px" },
  //   },
  // },
]);

// 表格列配置
const columns = computed(() => [
  {
    label: "规则名称",
    prop: "name",
    align: "center",
  },
  { label: "文献类型", prop: "docType_dict", width: "120", align: "center" },
  { label: "启用环节", prop: "phase_dict", width: "120", align: "center" },
  {
    label: "文件名称",
    prop: "fileName",
    align: "center",
    width: "280",
    showOverflowTooltip: true,
  },
  {
    label: "文件大小",
    prop: "fileSize",
    align: "center",
  },
  {
    label: "操作人",
    prop: "createBy",
    align: "center",
    minWidth: "120",
  },
  { label: "处理状态", prop: "status_dict", width: "120", align: "center" },
  {
    label: "更新时间",
    prop: "updateTime",
    align: "center",
    minWidth: "120",
    width: "140",
  },
]);

// 验证规则弹窗数据
const validationModal = reactive({
  open: false,
  validRuleFlag: false,
  ruleBaseSelectFlag: true,
  form: {
    ruleId: "",
    sampleFile: "",
  },
  dict: {
    sysDict,
    bizDict,
  },
});

/** 查询规则验证列表 */
async function getList() {
  loading.value = true;
  const { data } = await listRuleCheckValidation(queryParams);
  // 处理数据字典映射
  dataList.value = data.records.map((row) => {
    return {
      ...row,
      ...mapDictLabels(row),
    };
  });
  total.value = data.total;
  loading.value = false;
}

// 映射规则管理字典标签
const mapDictLabels = (row) => {
  return {
    docType_dict: getDictLabel(sysDict.storage_data_type, row.docType),
    phase_dict: getDictLabel(sysDict.storage_rule_phase, row.phase),
    status_dict: getDictLabel(sysDict.storage_rule_valid_status, row.status),
  };
};

// 获取字典标签（优化版本）
const getDictLabel = (options, value) => {
  if (!value || !options?.length) return "";
  return options.find((opt) => opt.value === value)?.label ?? value;
};

/** 搜索按钮操作 */
function handleQuery(params) {
  Object.assign(queryParams, params);
  queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    ruleType: "VALIDATE",
    name: "",
    phase: "",
    status: "",
    searchCreatTime: "",
  });
  getList();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "/api/rule-check-validation/export",
    {
      ...queryParams,
    },
    `规则验证数据_${new Date().getTime()}.xlsx`
  );
}

/** 查看按钮操作 */
function handleLook(ruleId, ruleValidId) {
  if (ruleId == null || ruleId === "") {
    proxy.$modal.msgError("规则ID为空！");
    return;
  }
  router.push({
    name: "CheckDetail",
    query: {
      ruleId: ruleId,
      ruleValidId: ruleValidId,
    },
  });
}

/** 验证规则操作 */
function handleValidRule() {
  validationModal.open = true;
  validationModal.validRuleFlag = true;
  validationModal.ruleBaseSelectFlag = true;
  validationModal.form = {
    ruleId: "",
    sampleFile: "",
  };
}

/** 规则效验查看按钮操作 */
function handleValidLog(row) {
  console.log("查看日志", row);
}

/** 规则效验删除按钮操作 */
async function handleValidDelete(row) {
  if (row.type === 0) {
    proxy.$modal.msgError("默认校验规则文件无法删除！");
    return;
  }
  const confirmRes = await proxy.$modal
    .confirm('是否确认删除规则名称为"' + row.name + '"的数据项？')
    .catch(() => {});
  if (!confirmRes) return;
  await deleteRuleCheckValidation([row.id]);
  proxy.$modal.msgSuccess("删除成功！");
  await getList();
}

/** 规则效验失败原因按钮操作 */
function handleValidFailMsg(row) {
  console.log("查看失败原因", row);
}

/**获取解析规则字典信息*/
async function getRuleBaseSelect() {
  const { data } = await queryRuleBaseSelect({ ruleType: "VALIDATE" });
  bizDict.ruleBaseSelectOptions = data;
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  selectRows.value = selection;
  multiple.value = !selection.length;
}

function cellClick() {
  // 处理单元格点击事件
}

function handleEdit() {
  // 处理编辑事件
}

// 初始化
getList();
getRuleBaseSelect();
</script>

<style scoped lang="scss">
@import "@/components/Business/base.scss";

/* 操作按钮样式 */
.operation-btn {
  margin-left: 10px;
  vertical-align: middle;
  font-weight: 500;
  cursor: pointer;
}

/* 主要按钮样式 */
.my_primary_btn {
  font-family: MicrosoftYaHei;
  font-size: 14px;
  color: #ffffff;
  background-color: #0078d4;
  border-color: #0078d4;
  border-radius: 2px;

  &:hover {
    border-color: #2192e9ff;
    background-color: #106ebe;
  }

  &:active {
    background-color: #0069d4ff;
  }
}

/* 表格容器样式 */
:deep(.tableFormContent .table-container) {
  border: none;
  padding: 0;
}

/* 表格行样式优化 */
:deep(.el-table tbody tr:hover) {
  background-color: #f5f7fa !important;
}

:deep(.el-table .current-row) {
  background-color: #e6f7ff !important;
}

/* 表格头部样式 */
:deep(.el-table th) {
  background-color: #fafbfc;
  color: #606266;
  font-weight: 500;
  border-bottom: 1px solid #ebeef5;
}

/* 操作列按钮样式 */
:deep(.el-button--small) {
  padding: 5px 8px;
  font-size: 12px;
}

:deep(.el-button + .el-button) {
  margin-left: 8px;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 6px;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

/* 文件上传提示样式 */
.upload-tip {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}

/* 分割线样式 */
:deep(.el-divider--horizontal) {
  margin: 16px 0;
}

/* 对话框底部样式 */
.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 8px;
  }
}

/* 状态标签样式 */
:deep(.el-tag) {
  border-radius: 4px;
  font-size: 12px;
}

/* 链接按钮样式 */
:deep(.el-button--text) {
  color: #0078d4;

  &:hover {
    color: #106ebe;
  }
}
</style>
