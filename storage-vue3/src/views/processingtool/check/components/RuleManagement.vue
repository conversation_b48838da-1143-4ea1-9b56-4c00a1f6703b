<template>
  <ViewBody title="规则管理">
    <template #content>
      <!-- 查询表单 -->
      <FormSearch
        v-model="queryParams"
        :formItems="formItems"
        searchBtnName="查询"
        @search="handleQuery"
        @reset="resetQuery"
      >
        <!-- 自定义按钮插槽 -->
        <template #btn>
          <el-button type="primary" icon="Plus" @click="handleAdd">
            新建规则
          </el-button>
          <el-button type="success" icon="Download" @click="handleExport">
            导出规则
          </el-button>
          <el-button
            type="danger"
            icon="Delete"
            @click="handleDelete()"
            :disabled="multiple"
          >
            删除规则
          </el-button>
        </template>
      </FormSearch>

      <!-- 表格部分 -->
      <TableForm
        ref="tableFormRef"
        v-loading="loading"
        :total="total"
        :columns="columns"
        :tableData="dataList"
        :isShowSearchQuery="false"
        :isShowCount="true"
        :show-index="true"
        :show-note-column="false"
        :isShowSelection="true"
        :showEditBtn="false"
        :showOtherColumn="true"
        tableotherColumnLabel="操作"
        @selection-change="handleSelectionChange"
        @cellClick="cellClick"
        @handleClick="handleEdit"
      >
        <template #otherOperation="{ row }">
          <!-- 使用 flex 布局确保按钮在同一行 -->
          <div class="operation-buttons">
            <span
              class="operation-button"
              @click="handleLook(row.id, row.ruleValidId)"
              v-hasPermi="['interfaces:interface:view']"
            >
              查看
            </span>
            <span class="operation-button" @click="handleUpdate(row)">
              编辑
            </span>
            <!-- 更多操作下拉菜单 -->
            <el-dropdown
              @command="(command) => handleMoreAction(command, row)"
              trigger="click"
              class="operation-dropdown"
            >
              <span class="operation-button">
                更多
                <el-icon class="el-icon--right">
                  <ArrowDown />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-if="row.useStatus === 'N'"
                    command="delete"
                    icon="Delete"
                    >删除</el-dropdown-item
                  >
                  <el-dropdown-item
                    v-if="row.useStatus === 'Y'"
                    command="send"
                    icon="Promotion"
                    >发送</el-dropdown-item
                  >
                  <el-dropdown-item command="verify" icon="CircleCheck"
                    >验证</el-dropdown-item
                  >
                  <el-dropdown-item command="resend" icon="Refresh"
                    >重新发送</el-dropdown-item
                  >
                  <el-dropdown-item command="failReason" icon="Warning"
                    >失败理由</el-dropdown-item
                  >
                  <el-dropdown-item command="log" icon="Document"
                    >日志</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>

        <!-- 自定义分页插槽 -->
        <template #pagination>
          <Pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </template>

        <template #drawer>
          <!-- 右侧的抽屉内容 -->
          <DrawerForm ref="drawerRef">
            <template #header="{ titleId, titleClass }">
              <div :id="titleId" :class="titleClass" class="my_drawer_title">
                <span class="active">{{ modal.title }}</span>
                <div class="my_drawer_title_right">
                  <span class="btn_add" @click="handleSave">保存</span>
                  <span class="btn_cancel" @click="drawerRef?.closeDrawer()"
                    >取消</span
                  >
                </div>
              </div>
            </template>
            <template #form>
              <RightDrawForm
                ref="drawerFormRef"
                :modal="modal"
                @ok="getList"
              ></RightDrawForm>
            </template>
          </DrawerForm>
        </template>
      </TableForm>
    </template>
  </ViewBody>

  <!-- 验证规则弹窗 -->
  <RuleCheckValidationDialog :modal="validationModal" @success="getList" />
</template>

<script setup>
import { reactive, ref, computed, getCurrentInstance } from "vue";
import { ArrowDown } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import {
  FormSearch,
  TableForm,
  DrawerForm,
  ViewBody,
  Pagination,
} from "@/components/Business";
import RightDrawForm from "./RightDrawForm.vue";
import RuleCheckValidationDialog from "./RuleCheckValidationDialog.vue";
import { queryRuleBaseSelect } from "@/api/processingtool/ruleBase.js";
import {
  deleteRuleCheckBase,
  listRuleCheckBase,
  resendRuleCheckBaseApi,
  STORAGE_RULE_CHECK_BASE_URL,
  updateRuleCheckBase,
} from "@/api/processingtool/ruleCheckBase.js";

const { proxy } = getCurrentInstance();
const router = useRouter();

/**数据字典*/
const sysDict = reactive({
  ...proxy.useDict(
    "storage_data_type",
    "storage_use_status",
    "storage_rule_valid_status",
    "storage_tool_status",
    "storage_rule_check_status",
    "storage_rule_phase"
  ),
});

/**业务字典*/
const bizDict = reactive({ ruleBaseSelectOptions: [] });

// 表单引用
const drawerRef = ref(null);
const drawerFormRef = ref(null);

const dataList = ref([]);
const loading = ref(true);
const total = ref(1);
const multiple = ref(true);
const ids = ref([]);
const selectRows = ref([]);
const dateRange = ref([]);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  ruleType: "VALIDATE",
  name: "",
  phase: "",
  checkStatus: "",
  toolStatus: "",
  useStatus: "",
  searchCreatTime: "",
});

// 弹窗数据
const modal = reactive({
  isEdit: false,
  title: "",
  form: {},
  dict: {
    sysDict,
    bizDict,
  },
  arrayField: [],
});

// 验证规则弹窗数据
const validationModal = reactive({
  open: false,
  validRuleFlag: false,
  ruleBaseSelectFlag: true,
  form: {
    ruleId: "",
    sampleFile: "",
  },
  dict: {
    sysDict,
    bizDict,
  },
});

// 搜索表单配置
const formItems = computed(() => [
  {
    label: "规则名称",
    prop: "name",
    component: "el-input",
    props: {
      placeholder: "规则名称",
      clearable: true,
      style: { width: "200px" },
    },
  },
  {
    label: "启用环节",
    prop: "phase",
    component: "el-select",
    props: {
      placeholder: "启用环节",
      clearable: true,
      style: { width: "200px" },
    },
    children: () => sysDict.storage_rule_phase,
  },
  {
    label: "处理状态",
    prop: "checkStatus",
    component: "el-select",
    props: {
      placeholder: "处理状态",
      clearable: true,
      style: { width: "200px" },
    },
    children: () => sysDict.storage_rule_check_status,
  },
  {
    label: "工具交互状态",
    prop: "toolStatus",
    component: "el-select",
    props: {
      placeholder: "工具交互状态",
      clearable: true,
      style: { width: "200px" },
    },
    children: () => sysDict.storage_tool_status,
  },
  {
    label: "应用状态",
    prop: "useStatus",
    component: "el-select",
    props: {
      placeholder: "应用状态",
      clearable: true,
      style: { width: "200px" },
    },
    children: () => sysDict.storage_use_status,
  },
  // {
  //   label: "创建时间",
  //   prop: "searchCreatTime",
  //   component: "el-date-picker",
  //   props: {
  //     type: "daterange",
  //     "range-separator": "-",
  //     "start-placeholder": "开始日期",
  //     "end-placeholder": "结束日期",
  //     "value-format": "YYYY-MM-DD",
  //     style: { width: "308px" },
  //   },
  // },
]);

// 表格列配置
const columns = ref([
  { prop: "name", label: "规则名称", align: "center" },
  { prop: "docType_dict", label: "文献类型", width: "100", align: "center" },
  { prop: "phase_dict", label: "启用环节", width: "120", align: "center" },
  {
    prop: "checkStatus_dict",
    label: "处理状态",
    width: "120",
    align: "center",
  },
  {
    prop: "toolStatus_dict",
    label: "工具交互状态",
    width: "120",
    align: "center",
  },
  {
    prop: "useStatus_dict",
    label: "应用状态",
    width: "100",
    align: "center",
    type: "switch",
    activeText: "启用",
    inactiveText: "停用",
  },
  { prop: "createTime", label: "规则创建时间", width: "180", align: "center" },
  { prop: "updateTime", label: "规则更新时间", width: "180", align: "center" },
]);

// ============================== 规则效验方法 ================================

/** 查询接口列表 */
async function getList() {
  loading.value = true;
  try {
    const { data } = await listRuleCheckBase(queryParams);
    // 处理数据字典映射
    dataList.value = data.records.map((row) => {
      return {
        ...row,
        ...mapDictLabels(row),
      };
    });
    total.value = data.total;
  } catch (error) {
    console.error("获取规则列表失败:", error);
    proxy.$modal.msgError("获取规则列表失败");
  } finally {
    loading.value = false;
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  modal.isEdit = false;
  modal.title = "新增规则";
  modal.form = {
    useStatus: "N",
    toolStatus: "DATA_SENT",
    ruleStatus: "EXECUTING",
    ruleType: "VALIDATE",
  };
  // 打开抽屉
  drawerRef?.value.openDrawer();
}

/** 修改按钮操作 */
function handleUpdate(row) {
  modal.isEdit = true;
  modal.title = "编辑规则";
  modal.form = { ...row };
  // 打开抽屉
  drawerRef?.value.openDrawer();
}

/** 删除按钮操作 */
async function handleDelete(row) {
  if (!row) {
    const hasActiveStatusRows = selectRows.value.filter(
      (item) => item.useStatus === "Y"
    );
    if (hasActiveStatusRows.length > 0) {
      proxy.$modal.msgError("选择数据中包含已启用状态数据，无法进行删除！");
      return;
    }
  }
  const localIds = row ? [row.id] : ids.value;
  const selectNameArr = row
    ? row.name
    : selectRows.value.map((item) => item.name);
  const confirmRes = await proxy.$modal
    .confirm('是否确认删除效验规则名称为"' + selectNameArr + '"的数据项？')
    .catch(() => {});
  if (!confirmRes) return;
  await deleteRuleCheckBase(localIds);
  proxy.$modal.msgSuccess("删除成功！");
  await getList();
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    STORAGE_RULE_CHECK_BASE_URL + "/export",
    {
      ...queryParams,
    },
    `效验规则规则数据_${new Date().getTime()}.xlsx`
  );
}

/** 查看按钮操作 */
function handleLook(ruleId, ruleValidId) {
  if (ruleId == null || ruleId === "") {
    proxy.$modal.msgError("规则ID为空！");
    return;
  }
  router.push({
    name: "CheckDetail",
    query: {
      ruleId: ruleId,
      ruleValidId: ruleValidId,
    },
  });
}

/** 发送按钮操作 */
function handleSend(row) {
  console.log("发送", row);
}

/** 规则验证弹窗操作 */
function handleVerify(row) {
  validationModal.open = true;
  validationModal.validRuleFlag = true;
  validationModal.ruleBaseSelectFlag = false;
  validationModal.form = {
    ruleId: row.id,
    sampleFile: "",
  };
}

/** 重新发送操作 */
async function handleResend(row) {
  const confirmRes = await proxy.$modal
    .confirm('是否确认重新发送规则名称为"' + row.name + '"的数据项？')
    .catch(() => {});
  if (!confirmRes) return;
  await resendRuleCheckBaseApi(row.id);
  proxy.$modal.msgSuccess("重新发送成功！");
  await getList();
}

/** 失败理由操作 */
function handleFailReason(row) {
  console.log("失败理由", row);
}

/** 日志操作 */
function handleLog(row) {
  console.log("日志", row);
}

/** 处理更多操作下拉菜单命令 */
function handleMoreAction(command, row) {
  switch (command) {
    case "delete":
      handleDelete(row);
      break;
    case "send":
      handleSend(row);
      break;
    case "verify":
      handleVerify(row);
      break;
    case "resend":
      handleResend(row);
      break;
    case "failReason":
      handleFailReason(row);
      break;
    case "log":
      handleLog(row);
      break;
    default:
      console.warn("未知的操作命令:", command);
  }
}

// 通用操作方法
async function handleUseStatusChange(row) {
  // 转换开关值为 Y/N 格式
  const newStatus = row.useStatus === "N" ? "Y" : "N";
  let text = newStatus === "Y" ? "启用" : "停用";
  const confirmRes = await proxy.$modal
    .confirm("确认要" + text + "吗?")
    .catch(() => {});
  if (!confirmRes) {
    row.useStatus_dict = newStatus === "N";
    return;
  }
  try {
    // 准备更新数据，确保使用 Y/N 格式
    const updateData = { ...row, useStatus: newStatus };
    await updateRuleCheckBase(updateData);
    proxy.$modal.msgSuccess(text + "成功");
    // 更新本地数据
    row.useStatus = newStatus;
    row.useStatus_dict = newStatus === "Y";
  } catch (error) {
    // 更新失败，恢复原状态
    row.useStatus_dict = newStatus === "N";
    console.log(text + "失败", error);
  }
}

/**获取解析规则字典信息*/
async function getRuleBaseSelect() {
  const { data } = await queryRuleBaseSelect({ ruleType: "VALIDATE" });
  bizDict.ruleBaseSelectOptions = data;
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  selectRows.value = selection;
  multiple.value = !selection.length;
}

// 处理单元格点击事件（用于开关状态变化）
function cellClick(row, column, cellValue, event) {
  // 如果是应用状态列的开关变化
  if (column.property === "useStatus_dict") {
    handleUseStatusChange(row);
  }
}

function handleEdit() {
  // 处理编辑事件
}

// 抽屉保存方法
const handleSave = async () => {
  await drawerFormRef?.value.submitForm();
  drawerRef?.value.closeDrawer();
};

// 映射规则管理字典标签
const mapDictLabels = (row) => {
  return {
    docType_dict: getDictLabel(sysDict.storage_data_type, row.docType),
    phase_dict: getDictLabel(sysDict.storage_rule_phase, row.phase),
    checkStatus_dict: getDictLabel(
      sysDict.storage_rule_check_status,
      row.checkStatus
    ),
    toolStatus_dict: getDictLabel(sysDict.storage_tool_status, row.toolStatus),
    // 转换 useStatus 为布尔值供开关组件使用
    useStatus_dict: row.useStatus === "Y",
  };
};

// 获取字典标签（优化版本）
const getDictLabel = (options, value) => {
  if (!value || !options?.length) return "";
  return options.find((opt) => opt.value === value)?.label ?? value;
};

// 初始化
getList();
getRuleBaseSelect();
</script>

<style scoped lang="scss">
@import "@/components/Business/base.scss";

.my_drawer_title {
  display: flex;
  justify-content: space-between;
}

.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
  }

  .btn_add {
    background-color: rgb(0, 120, 212);
  }
}

// 单独设置右侧抽屉的样式，防止搜索部分被遮挡。与设计图不同～
:deep(.drawer-content-new) {
  margin-top: 0;
  border-top: 1px solid #bccde0;
  max-height: calc(100vh - 85px);
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  align-items: center;
}

.operation-button {
  margin-left: 10px;
  color: #0076d0;
  font-weight: 500;
  cursor: pointer;
  font-size: 12px;
}

.operation-dropdown {
  font-size: 12px;
  line-height: 23px;
}
</style>
