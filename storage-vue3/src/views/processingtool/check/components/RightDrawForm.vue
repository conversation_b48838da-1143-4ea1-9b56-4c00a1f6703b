<template>
  <div class="form-container">
    <el-form
      ref="fromRef"
      :rules="rules"
      :model="form"
      label-width="120px"
      size="small"
      label-position="left"
    >
      <el-form-item label="规则名称" prop="name">
        <template #label> 规则名称<em>*</em>： </template>
        <el-input v-model="form.name" placeholder="规则名称" />
        <template #error><em /></template>
      </el-form-item>

      <!-- 启用环节 -->
      <el-form-item label="启用环节" prop="phase">
        <template #label> 启用环节<em>*</em>： </template>
        <el-select clearable filterable v-model="form.phase" placeholder="">
          <el-option
            v-for="dict in sysDict.storage_rule_phase"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
        <template #error><em /></template>
      </el-form-item>

      <!-- 数据类型 -->
      <el-form-item label="数据类型" prop="docType">
        <template #label> 数据类型<em>*</em>： </template>
        <el-select clearable filterable v-model="form.docType" placeholder="">
          <el-option
            v-for="dict in sysDict.storage_data_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
        <template #error><em /></template>
      </el-form-item>

      <el-form-item label="应用状态" prop="useStatus">
        <template #label> 应用状态<em>*</em>： </template>
        <el-select
          filterable
          :disabled="true"
          v-model="form.useStatus"
          placeholder=""
        >
          <el-option
            v-for="dict in sysDict.storage_use_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
        <template #error><em /></template>
      </el-form-item>

      <el-form-item label="规则描述" prop="description" class="form-item-52">
        <template #label> 规则描述<em>*</em>： </template>
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="规则描述"
        ></el-input>
      </el-form-item>

      <el-form-item
        v-if="!modal.isEdit"
        label="样例文件"
        prop="sampleFile"
        class="form-item-95"
      >
        <FileUpload
          v-model="form.sampleFile"
          :action="'/file/common/upload'"
          :limit="1"
          :fileSize="20"
          :fileType="['zip', 'xml']"
          :fileList="[]"
          :isShowTip="true"
          :data="uploadParams"
          @upload-change="handleUploadChange"
          :uploadTip="customUploadTip"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { reactive, ref, computed, getCurrentInstance } from "vue";
import FileUpload from "@/components/FileUpload/index.vue";
import {
  updateRuleCheckBase,
  addRuleCheckBase,
} from "@/api/processingtool/ruleCheckBase.js";
const { proxy } = getCurrentInstance();

const props = defineProps({
  modal: {
    type: Object,
  },
});

const emit = defineEmits(["ok"]);

/**字典*/
const { sysDict, bizDict } = props.modal.dict;

// 需要进行数组转换的字段
const arrayField = computed(() => {
  return props.modal.arrayField;
});

const customUploadTip = ref("样例文件仅用于规则生成，文件及处理结果均不入库");

const form = computed({
  get() {
    return props.modal.form;
  },
  set() {},
});

const rules = reactive({
  name: [{ required: true, message: "规则名称不能为空", trigger: "change" }],
  phase: [{ required: true, message: "启用环节不能为空", trigger: "change" }],
  docType: [{ required: true, message: "数据类型不能为空", trigger: "change" }],
  useStatus: [
    { required: true, message: "应用状态不能为空", trigger: "change" },
  ],
});

const uploadParams = ref({
  projectPath: "storage/rule/check",
  isSpecifiedFile: true,
});

function handleUploadChange(fileList) {
  console.log("handleUploadChange", fileList);
  if (!fileList || fileList.length === 0) {
    return;
  }
  form.value.sampleFile = fileList[0]?.url || "";
  form.value.fileList = fileList;
  console.log(form.value.sampleFile);
  console.log(form.value.fileList);
}

// 处理数组字段转换为字符串（保存时使用）
const processArrayToString = (data) => {
  const processedData = { ...data };
  if (arrayField.value) {
    arrayField.value.forEach((field) => {
      if (processedData[field] && Array.isArray(processedData[field])) {
        console.log(
          `转换数组字段 ${field}:`,
          processedData[field],
          "-> ",
          processedData[field].join(",")
        );
        // 过滤空值并转换为字符串
        processedData[field] = processedData[field]
          .filter((item) => item && item.trim() !== "")
          .join(",");
      }
    });
  }
  return processedData;
};

// 处理字符串转换为数组（编辑时使用）
const processStringToArray = (data, readonly = false) => {
  if (arrayField.value && !readonly) {
    arrayField.value.forEach((field) => {
      if (data[field] && typeof data[field] === "string") {
        console.log(
          `转换字符串字段 ${field}:`,
          data[field],
          "-> ",
          data[field].split(",")
        );
        // 处理空字符串的情况
        if (data[field].trim() === "") {
          data[field] = [];
        } else {
          data[field] = data[field]
            .split(",")
            .filter((item) => item.trim() !== "");
        }
      }
    });
  }
};

// 获取表单数据
const getFormData = () => {
  // 返回处理后的数据（数组转为字符串）
  console.log("表单数据", form);
  return processArrayToString(form);
};

// 提交表单
async function submitForm() {
  await proxy.$refs["fromRef"].validate();
  console.log("验证通过，提交表单数据:", form.value);

  if (!props.modal.isEdit) {
    // 设置默认值
    form.value.useStatus = "N";
    form.value.toolStatus = "DATA_SENT";
    form.value.checkStatus = "EXECUTING";
    form.value.ruleType = "VALIDATE";
    await addRuleCheckBase(form.value);
  } else {
    await updateRuleCheckBase(form.value);
  }
  proxy.$modal.msgSuccess(!props.modal.isEdit ? "添加成功" : "修改成功");
  emit("ok");
}

// 暴露方法给父组件
defineExpose({
  getFormData,
  submitForm,
});
</script>

<style lang="scss" scoped>
@import "@/components/Business/style/drawer-form.scss";
</style>
