<template>
  <el-dialog
    :title="modal.title"
    v-model="modal.open"
    width="500px"
    append-to-body
    destroy-on-close
  >
    <el-form ref="checkRef" :rules="rules" :model="form" label-width="120px">
      <el-form-item label="规则名称" prop="name">
        <el-input v-model="form.name" placeholder="规则名称" />
      </el-form-item>
      <el-form-item label="启用环节" prop="phase">
        <el-select clearable filterable v-model="form.phase" placeholder="">
          <el-option
            v-for="dict in sysDict.storage_rule_phase"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据类型" prop="docType">
        <el-select clearable filterable v-model="form.docType" placeholder="">
          <el-option
            v-for="dict in sysDict.storage_data_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="应用状态" prop="useStatus">
        <el-select
          filterable
          :disabled="true"
          v-model="form.useStatus"
          placeholder=""
        >
          <el-option
            v-for="dict in sysDict.storage_use_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="规则描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="规则描述"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-divider />
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  addRuleCheckBase,
  updateRuleCheckBase,
} from "@/api/processingtool/ruleCheckBase.js";
import { getCurrentInstance } from "vue";

const { proxy } = getCurrentInstance();

const props = defineProps({
  modal: {
    type: Object,
  },
});
const emit = defineEmits(["ok"]);
/**字典*/
const { sysDict, bizDict } = props.modal.dict;
const form = computed({
  get() {
    return props.modal.form;
  },
  set() {},
});

const rules = reactive({
  name: [{ required: true, message: "规则名称不能为空", trigger: "change" }],
  phase: [{ required: true, message: "启用环节不能为空", trigger: "change" }],
  docType: [{ required: true, message: "数据类型不能为空", trigger: "change" }],
  useStatus: [
    { required: true, message: "应用状态不能为空", trigger: "change" },
  ],
});

/** 提交按钮 */
async function submitForm() {
  await proxy.$refs["checkRef"].validate();
  console.log("验证通过，提交表单数据:", form.value);
  console.log("提交表单", form.value);

  if (!props.modal.isEdit) {
    await addRuleCheckBase(form.value);
  } else {
    await updateRuleCheckBase(form.value);
  }
  proxy.$modal.msgSuccess(!props.modal.isEdit ? "添加成功" : "修改成功");
  props.modal.open = false;
  emit("ok");
}

function cancel() {
  props.modal.open = false;
  props.modal.isEdit = false;
}
</script>

<style scoped>
.upload-tip {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
}
</style>
