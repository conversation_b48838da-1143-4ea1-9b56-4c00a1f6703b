<template>
  <el-dialog
    :title="modal.title"
    v-model="modal.open"
    width="500px"
    append-to-body
    destroy-on-close
  >
    <el-form
      ref="checkDetailRef"
      :rules="rules"
      :model="form"
      label-width="120px"
    >
      <!--      <el-form-item label="关联路径" prop="pathId">-->
      <!--        <el-select v-model="form.pathId" placeholder="请选择关联路径" clearable style="width: 100%">-->
      <!--          <el-option-->
      <!--              v-for="dict in bizDict.ruleCheckSelectOptions"-->
      <!--              :key="dict.value"-->
      <!--              :label="dict.text"-->
      <!--              :value="dict.value"-->
      <!--          />-->
      <!--        </el-select>-->
      <!--      </el-form-item>-->
      <el-form-item label="字段名称" prop="fieldName">
        <el-input v-model="form.fieldName" placeholder="请输入字段名称" />
      </el-form-item>
      <el-form-item label="校验项" prop="checkItem">
        <el-input v-model="form.checkItem" placeholder="请输入校验项" />
      </el-form-item>
      <el-form-item label="条件类型" prop="checkType">
        <el-select
          v-model="form.checkType"
          placeholder="请选择条件类型"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="dict in sysDict.storage_rule_check_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="条件值/表达式" prop="expression">
        <el-input v-model="form.expression" placeholder="请输入条件值/表达式" />
      </el-form-item>
      <el-form-item label="提示信息" prop="prompt">
        <el-input v-model="form.prompt" placeholder="请输入提示信息" />
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-select
          v-model="form.priority"
          placeholder="请输入优先级"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="dict in sysDict.storage_rule_check_priority"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="备注"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-divider />
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  addRuleCheckDetail,
  updateRuleCheckDetail,
} from "@/api/processingtool/ruleCheckDetail.js";
import { getCurrentInstance, computed, reactive } from "vue";

const { proxy } = getCurrentInstance();

const props = defineProps({
  modal: {
    type: Object,
    required: true,
  },
});
const emit = defineEmits(["ok"]);
/**字典*/
const { sysDict, bizDict } = props.modal.dict;
const form = computed({
  get() {
    return props.modal.form;
  },
  set() {},
});

const rules = reactive({
  // pathId: [
  //   {required: true, message: "关联路径不能为空", trigger: "change"}
  // ],
  fieldName: [{ required: true, message: "字段名称不能为空", trigger: "blur" }],
  checkItem: [{ required: true, message: "校验项不能为空", trigger: "blur" }],
  checkType: [
    { required: true, message: "条件类型不能为空", trigger: "change" },
  ],
  expression: [
    { required: true, message: "条件值/表达式不能为空", trigger: "blur" },
  ],
  priority: [{ required: true, message: "优先级不能为空", trigger: "blur" }],
  docType: [{ required: true, message: "数据类型不能为空", trigger: "change" }],
  useStatus: [
    { required: true, message: "应用状态不能为空", trigger: "change" },
  ],
});

/** 提交按钮 */
async function submitForm() {
  try {
    await proxy.$refs["checkDetailRef"].validate();
    console.log("验证通过，提交表单数据:", form.value);

    if (!props.modal.isEdit) {
      await addRuleCheckDetail(form.value);
      proxy.$modal.msgSuccess("添加成功");
    } else {
      await updateRuleCheckDetail(form.value);
      proxy.$modal.msgSuccess("修改成功");
    }
    props.modal.open = false;
    emit("ok");
  } catch (error) {
    console.error("表单验证失败", error);
  }
}

function cancel() {
  props.modal.open = false;
  props.modal.isEdit = false;
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
