<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="search-form">
        <el-form-item label="品种ID" prop="fusionJournalId">
          <el-input
              v-model="queryParams.fusionJournalId"
              placeholder="请输入品种ID"
              clearable
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="期刊题名" prop="sourceTitle">
          <el-input
              v-model="queryParams.sourceTitle"
              placeholder="请输入期刊题名"
              clearable
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="ISSN" prop="issn">
          <el-input
              v-model="queryParams.issn"
              placeholder="请输入ISSN"
              clearable
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="数据状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="数据状态" clearable style="width: 200px" @keyup.enter.native="handleQuery">
            <el-option value="0" label="停用">停用</el-option>
            <el-option value="1" label="正常" selected>正常</el-option>
            <el-option value="2" label="回退">回退</el-option>
          </el-select>
        </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <div class="flex-container">
        <div class="table-container" :class="{ 'table-collapsed': drawerOpen }">
        <el-row :gutter="10" class="mb8">
           <right-toolbar 
             v-model:showSearch="showSearch" 
             @queryTable="getList"
             :columns="columnList"
             :show-columns-type="'checkbox'"
             @checkbox-change="handleColumnChange"
           ></right-toolbar>
        </el-row>

      <el-table v-loading="loading" :data="dataSourceList" @row-click="handleRowClick">
        <template v-for="col in columnList" :key="col.key">
          <el-table-column
            v-if="col.visible"
            :label="col.label"
            :prop="col.key"
            align="center"
            show-overflow-tooltip
            :min-width="col.key === 'sourceTitle' ? 200 : 100"
          >
            <template #default="scope">
              <div class="cell-content" v-if="col.key !== 'status'">{{ scope.row[col.key] }}</div>
              <div v-else class="cell-content">
                <el-tag v-if="scope.row.status === '0'" type="danger" size="small">停用</el-tag>
                <el-tag v-else-if="scope.row.status === '1'" type="success" size="small">正常</el-tag>
                <el-tag v-else-if="scope.row.status === '2'" type="warning" size="small">回退</el-tag>
                <span v-else>{{ scope.row.status }}</span>
              </div>
            </template>
          </el-table-column>
        </template>
        <el-table-column label="操作" width="180" align="center" class-name="small-padding fixed-width" fixed="right">
          <template #default="scope">
            <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click.stop="handleDetail(scope.row)"
            >详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="handlePagination"
      />
        </div>

        <!-- 右侧抽屉式表单 -->
        <div class="drawer-container" v-show="drawer.visible" :class="{ 'open': drawer.visible }">
          <div class="drawer-header">
            <div class="drawer-title">期刊详情</div>
            <el-button class="drawer-close" @click="cancel" icon="Close" circle></el-button>
          </div>
          <div class="drawer-body">
            <!-- Tab 切换 -->
            <el-tabs v-model="activeTab">
              <el-tab-pane label="品种详情" name="journal">
                <div class="journal-info">
                  <div class="info-item">
                    <span class="label">Source Id:</span>
                    <span class="value">{{ journalForm.fusionJournalId }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Source Title:</span>
                    <span class="value">{{ journalForm.sourceTitle }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">ISSN:</span>
                    <span class="value">{{ journalForm.issn }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Publisher Name:</span>
                    <span class="value">{{ journalForm.publisherName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Pub Year:</span>
                    <span class="value">{{ journalForm.pubYear }}</span>
                  </div>
                </div>
              </el-tab-pane>
              
              <el-tab-pane label="卷期详情" name="volume">
                <div class="volume-info">
                  <el-table :data="volumeList" style="width: 100%" border>
                    <el-table-column prop="volume" label="卷号" align="center" />
                    <el-table-column prop="issue" label="期号" align="center" />
                    <el-table-column prop="year" label="出版年" align="center" />
                  </el-table>
                  <!-- 添加分页组件 -->
                  <div class="pagination-container">
                    <pagination
                      v-if="volumeTotal > 0"
                      :total="volumeTotal"
                      v-model:page="volumeQueryParams.pageNum"
                      v-model:limit="volumeQueryParams.pageSize"
                      @pagination="handleVolumePagination"
                      :small="true"
                      layout="prev, pager, next"
                    />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
     <!-- 挂接品种数据表格（在主界面下方）-->
     <div class="related-data-section" :class="{ 'table-collapsed': drawerOpen }" v-if="showRelatedData">
       <div class="section-title">挂接品种数据</div>
       <div class="related-table-container">
         <el-table
             :data="relatedJournals"
             border
             style="width: 100%"
             :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
         >
           <el-table-column label="单源品种ID" prop="singleJournalId" align="center" show-overflow-tooltip min-width="120" />
           <el-table-column label="数据源" prop="dataSource" align="center" show-overflow-tooltip min-width="100" />
           <el-table-column label="期刊题名" prop="sourceTitle" align="center" show-overflow-tooltip min-width="200" />
           <el-table-column label="ISSN" prop="issn" align="center" show-overflow-tooltip min-width="100" />
           <el-table-column label="年/卷/期区间" prop="interval" align="center" show-overflow-tooltip min-width="150" />
           <el-table-column label="更新时间" prop="updateTime" align="center" show-overflow-tooltip min-width="150" />
         </el-table>

         <!-- 分页组件 -->
         <pagination
             v-show="relatedTotal > 0"
             :total="relatedTotal"
             v-model:page="relatedQueryParams.pageNum"
             v-model:limit="relatedQueryParams.pageSize"
             @pagination="getRelatedJournals"
         />
       </div>
     </div>
   </div>
</template>

<script setup name="storageService">
import { listMutchJournals, getMutchJournalInfo, getMutchJournalVolumes, listRelatedMutchJournals, listMutchJournalsScroll } from "@/api/storageService/storageService";
import { useRouter } from "vue-router";

const router = useRouter();
const { proxy } = getCurrentInstance();
const { storage_last_state } = proxy.useDict("storage_last_state");

// 列配置
const columnList = ref([
  { key: 'fusionJournalId', label: '品种ID', visible: true },
  { key: 'sourceTitle', label: '期刊题名', visible: true },
  { key: 'issn', label: 'ISSN', visible: true },
  { key: 'interval', label: '年/卷/期区间', visible: true },
  { key: 'articleNum', label: '篇级数量', visible: true },
  { key: 'language', label: '语种', visible: true },
  { key: 'status', label: '数据状态', visible: true },
  { key: 'updateTime', label: '更新时间', visible: true }
]);

// 处理列显示变化
const handleColumnChange = (checked, label) => {
  const newColumnList = columnList.value.map(col => {
    if (col.label === label) {
      return { ...col, visible: checked };
    }
    return col;
  });
  columnList.value = newColumnList; // 通过整体替换触发响应式更新
};

const dataSourceList = ref([]);
const drawerOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
// 深度翻页相关
const scrollId = ref('');
const isDeepPaging = ref(false);
const currentPage = ref(1);
const lastAllowedPage = ref(1);
const MAX_NORMAL_COUNT = 10000; // ES默认最大可查询数据条数

const drawer = ref({
  visible: false
});

const journalForm = ref({});

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    fusionJournalId: null,
    sourceTitle: null,
    status: "1",
    issn: null
  }
});

const { queryParams } = toRefs(data);

// 挂接篇级数据列表
const relatedJournals = ref([]);

// 挂接篇级数据查询参数
const relatedQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  journalId: ''
});

// 挂接篇级数据总数
const relatedTotal = ref(0);

// 添加控制关联表格显示的变量
const showRelatedData = ref(false);

// Tab 激活状态
const activeTab = ref('journal');

// 卷期列表数据
const volumeList = ref([]);

// 在data部分添加卷期分页参数
const volumeQueryParams = ref({
  pageNum: 1,
  pageSize: 10
});
const volumeTotal = ref(0);

/** 查询多源品种列表 */
function getList() {
  loading.value = true;
  const params = {
    ...queryParams.value,
    scrollId: scrollId.value
  };

  // 根据是否深度翻页选择不同的接口
  if (isDeepPaging.value) {
    listMutchJournalsScroll(params).then(response => {
      if (response.rows && response.rows.length > 0) {
        // 如果是深度翻页,需要保存新的scrollId
        if (response.rows[0].scrollId) {
          scrollId.value = response.rows[0].scrollId;
        }
        dataSourceList.value = response.rows;
        total.value = response.total; // 深度翻页模式下使用固定值
      } else {
        // 如果没有数据,重置scrollId
        scrollId.value = '';
        isDeepPaging.value = false;
        dataSourceList.value = [];
        total.value = 0;
      }
      loading.value = false;
    });
  } else {
    listMutchJournals(params).then(response => {
      dataSourceList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }
}

/**
 * 点击详情按钮，跳转到详情页面
 * @param {Object} row 行数据
 */
function handleDetail(row) {
  router.push({
    path: '/storageService/mutch/journal/detail',
    query: {
      journalId: row.fusionJournalId
    }
  });
}

function handleRowClick(row) {
  // 获取品种详情
  getMutchJournalInfo(row.fusionJournalId).then(response => {
    journalForm.value = response.data;
  });

  // 设置关联查询参数
  relatedQueryParams.value.journalId = row.fusionJournalId;
  volumeQueryParams.value.pageNum = 1;
  volumeQueryParams.value.pageSize = 10;

  // 获取卷期列表
  getVolumeList(row.fusionJournalId);

  // 获取关联数据
  getRelatedJournals();
  
  // 显示抽屉和关联数据
  drawer.value.visible = true;
  drawerOpen.value = true;
  showRelatedData.value = true;
  
  // 默认显示第一个 tab
  activeTab.value = 'journal';
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  scrollId.value = '';
  isDeepPaging.value = false;
  currentPage.value = 1;
  lastAllowedPage.value = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  scrollId.value = '';
  isDeepPaging.value = false;
  currentPage.value = 1;
  lastAllowedPage.value = 1;
  handleQuery();
}

// 获取挂接品种数据
function getRelatedJournals() {
  listRelatedMutchJournals(relatedQueryParams.value.journalId).then(response => {
    relatedJournals.value = response.rows;
    relatedTotal.value = response.total;
  });
}

/** 取消按钮 */
function cancel() {
  drawer.value.visible = false;
  drawerOpen.value = false;
  resetDrawerForm();
  // 隐藏挂接品种数据表格
  showRelatedData.value = false;
}

/** 重置抽屉表单 */
function resetDrawerForm() {
  journalForm.value = {};
  activeTab.value = 'journal';
  volumeList.value = [];
  volumeTotal.value = 0;
  // 清空关联数据
  relatedJournals.value = [];
  relatedTotal.value = 0;
}

/** 分页组件事件处理 */
function handlePagination({ page, limit }) {
  // 计算即将查询的数据位置
  const targetDataCount = page * limit;

  // 如果已经是深度翻页模式
  if (isDeepPaging.value) {
    // 只允许向后翻页
    if (page < currentPage.value) {
      proxy.$modal.msgWarning('深度翻页模式下不支持向前翻页,请重新执行查询');
      queryParams.value.pageNum = currentPage.value;
      return;
    }
    // 只允许翻到下一页
    if (page > currentPage.value + 1) {
      proxy.$modal.msgWarning('深度翻页模式下只能一页一页向后翻');
      queryParams.value.pageNum = currentPage.value;
      return;
    }
  }
  // 首次跳页时的处理
  else if (!isDeepPaging.value) {
    // 如果即将查询的数据超过最大限制
    if (targetDataCount > MAX_NORMAL_COUNT) {
      // 计算最后允许的正常页码
      const maxAllowedPage = Math.ceil(MAX_NORMAL_COUNT / limit);
      
      proxy.$modal.confirm(
        `查询数据超过${MAX_NORMAL_COUNT}条，系统将自动跳转到第${maxAllowedPage}页并进入深度翻页模式，是否继续？`,
        '提示',
        {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          // 跳转到最大允许页
          page = maxAllowedPage;
          isDeepPaging.value = true;
          scrollId.value = '';
          currentPage.value = page;
          lastAllowedPage.value = page;
          queryParams.value.pageNum = page;
          queryParams.value.pageSize = limit;
          getList();
        })
        .catch(() => {
          queryParams.value.pageNum = lastAllowedPage.value;
        });
      return;
    }
  }

  currentPage.value = page;
  lastAllowedPage.value = page;
  queryParams.value.pageNum = page;
  queryParams.value.pageSize = limit;
  getList();
}

// 修改获取卷期列表的调用
function getVolumeList(journalId) {
  getMutchJournalVolumes(journalId, volumeQueryParams.value).then(response => {
    volumeList.value = response.rows;
    volumeTotal.value = response.total;
  });
}

// 卷期分页处理
function handleVolumePagination({ page, limit }) {
  volumeQueryParams.value.pageNum = page;
  volumeQueryParams.value.pageSize = limit;
  getVolumeList(relatedQueryParams.value.journalId);
}

/** 初始加载 */
getList();
</script>
<style scoped>
.flex-container {
  display: flex;
  position: relative;
  width: 100%;
  overflow: visible; /* 防止内容被裁剪 */
  margin-bottom: 20px; /* 为下方的关联表格留出空间 */
}

.table-container {
  flex-grow: 1;
  transition: all 0.3s ease;
  width: 100%;
  margin-right: 0; /* 默认状态无右边距 */
}

.table-container.table-collapsed {
  width: calc(100% - 450px); /* 增加与抽屉之间的距离 */
  margin-right: 450px; /* 添加右边距，确保表格内容不被遮挡 */
}

.drawer-container {
  position: absolute;
  right: 0;
  top: 0;
  width: 430px;
  background-color: #fff;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  border-left: 1px solid #e6e6e6;
  z-index: 100;
  height: calc(100vh - 185px);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.drawer-container.open {
  transform: none;
}

.drawer-header {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.drawer-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.drawer-close {
  padding: 8px;
  font-size: 18px;
  color: #909399;
  transition: color 0.2s;
}

.drawer-close:hover {
  color: #409EFF;
}

.drawer-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-tabs) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
  flex-shrink: 0;
}

:deep(.el-tabs__content) {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
}

.journal-info {
  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 14px;

    .label {
      color: #606266;
      font-weight: 500;
      min-width: 120px;
      margin-right: 10px;
    }

    .value {
      color: #303133;
      flex: 1;
      word-break: break-all;
    }
  }
}

.volume-info {
  margin-top: 10px;
}

:deep(.el-drawer__body) {
  padding: 0;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__nav) {
  width: 100%;
}

:deep(.el-tabs__item) {
  flex: 1;
  text-align: center;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table__header-wrapper th) {
  font-weight: 500;
  background-color: #F5F7FA;
}



.custom-switch :deep(.el-switch__core) {
  background-color: #ff4949;
}

.custom-switch :deep(.el-switch__core)::after {
  content: attr(data-text);
  position: absolute;
  font-size: 12px;
  color: #fff;
  left: 25px;
  top: 0;
  line-height: 20px;
}

.custom-switch.is-checked :deep(.el-switch__core)::after {
  content: "启用";
  left: 5px;
}

.custom-switch :deep(.el-switch__core)::before {
  content: "停用";
  position: absolute;
  font-size: 12px;
  color: #fff;
  right: 5px;
  top: 0;
  line-height: 20px;
}

.form-group-title {
  font-size:14px;
  font-weight: bold;
  margin: 15px 0 10px;
  padding-left: 5px;
  border-left: 3px solid #409EFF;
}

/* 新增样式 */
.no-margin {
  margin-bottom: 5px !important;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  margin-top: 5px;
  padding-left: 2px;
}

.el-form-item.is-required .el-form-item__label::before {
  content: '*';
  color: #F56C6C;
  margin-right: 4px;
}

/* 响应式样式调整 */
@media screen and (max-width: 1400px) {
  .drawer-container {
    width: 380px;
  }

  .table-container.table-collapsed {
    width: calc(100% - 400px);
    margin-right: 400px;
  }
}

.xml-content {
  background-color: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 15px;
  height: 100%;
  overflow: auto;
}

.xml-code {
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  margin: 0;
  white-space: pre-wrap;
}

.xml-code .tag {
  color: #0000ff;
}

.xml-code .attr {
  color: #ff0000;
}

.xml-code .string {
  color: #008000;
}

.related-data-section {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
  width: 100%;
  clear: both;
  transition: all 0.3s ease;
}

.related-data-section.table-collapsed {
  width: calc(100% - 450px);
  margin-right: 450px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
}

.related-table-container {
  margin-bottom: 20px;
}

/* 添加表格单元格样式 */
:deep(.cell-content) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.el-table td) {
  padding: 8px 0;
}

:deep(.el-table .cell) {
  white-space: nowrap;
}

:deep(.el-tooltip__popper) {
  max-width: 50%;
  word-break: break-all;
}

/* 表格hover效果增强 */
:deep(.el-table tbody tr:hover > td) {
  background-color: #f0f7ff !important;
}

.pagination-container {
  margin-top: 15px;
  text-align: center;
  overflow: hidden;
}

/* 设置分页组件在抽屉内的样式 */
:deep(.el-pagination) {
  justify-content: center;
  white-space: nowrap;
  padding: 0;
}

:deep(.el-pagination .el-pagination__total) {
  font-size: 12px;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  padding: 0 6px;
}

:deep(.el-pagination .el-pager li) {
  min-width: 24px;
}
</style>

