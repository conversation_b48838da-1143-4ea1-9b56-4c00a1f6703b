<template>
  <div class="app-container">
    <!-- 基本信息模块 -->
    <div class="module-container" v-show="showBasicInfo">
      <div class="module-header">
        <span class="module-title">基本信息</span>
        <el-button link class="collapse-btn" @click="showBasicInfo = false">收起</el-button>
      </div>
      <div class="module-content">
        <div class="info-grid">
          <div class="info-row">
            <span class="info-label">Source Id:</span>
            <span class="info-value">{{ journalInfo.fusionJournalId }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Source Title:</span>
            <span class="info-value">{{ journalInfo.sourceTitle }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">ISSN:</span>
            <span class="info-value">{{ journalInfo.issn }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Publisher Name:</span>
            <span class="info-value">{{ journalInfo.publisherName }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Pub Year:</span>
            <span class="info-value">{{ journalInfo.pubYear }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">language:</span>
            <span class="info-value">{{ journalInfo.language }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 收起状态显示恢复按钮 -->
    <div class="module-collapsed" v-if="!showBasicInfo">
      <div class="module-header">
        <span class="module-title">基本信息</span>
        <el-button link class="collapse-btn" @click="showBasicInfo = true">展开</el-button>
      </div>
    </div>

    <!-- 品种详情模块 -->
    <div class="module-container" v-show="showJournalDetail">
      <div class="module-header">
        <span class="module-title">品种详情</span>
        <el-button link class="collapse-btn" @click="showJournalDetail = false">收起</el-button>
      </div>
      <div class="module-content">
        <!-- Tab切换 -->
        <el-tabs v-model="activeTab" class="detail-tabs" @tab-click="handleTabClick">
          <el-tab-pane label="卷期列表" name="volumes">
            <div class="volume-table-container">
              <el-table 
                :data="volumeList" 
                border 
                style="width: 100%" 
                @row-click="handleVolumeClick"
              >
                <el-table-column prop="volume" label="卷号" align="center" width="100" />
                <el-table-column prop="issue" label="期号" align="center" width="100" />
                <el-table-column prop="year" label="出版年" align="center" width="100" />
                <el-table-column prop="articleCount" label="文章数" align="center" width="80" />
                <el-table-column prop="updateTime" label="更新时间" align="center" width="160" />
              </el-table>
              
              <!-- 分页组件 -->
              <div class="pagination-container">
                <pagination
                  v-if="total > 0"
                  :total="total"
                  v-model:page="volumeQueryParams.pageNum"
                  v-model:limit="volumeQueryParams.pageSize"
                  @pagination="handleVolumePagination"
                  :small="true"
                  layout="prev, pager, next"
                />
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="篇级详情" name="article">
            <!-- 篇级详情表格 -->
            <div class="volume-table-container">
              <!-- 添加卷期筛选提示 -->
              <div v-if="activeVolume" class="filter-tips">
                <span>当前筛选：{{ formatYearVolumeIssue(activeVolume) }}</span>
                <el-button type="primary" link @click="clearVolumeFilter">清除筛选</el-button>
              </div>
              <div v-else class="filter-info">
                <span>显示全部篇级数据</span>
              </div>
              
              <el-table
                :data="articleDetailList"
                border
                style="width: 100%"
                :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
              >
                <el-table-column label="篇级ID" prop="fusionArticleId" align="center" show-overflow-tooltip />
                <el-table-column label="篇级题名" align="center" show-overflow-tooltip>
                  <template #default="scope">
                    <span v-if="scope.row.articleTitle">{{ formatArticleTitle(scope.row.articleTitle) }}</span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column label="年/卷/期" align="center" width="120">
                  <template #default="scope">
                    <span>{{ formatYearVolumeIssue(scope.row) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="作者" align="center" show-overflow-tooltip>
                  <template #default="scope">
                    <span>{{ scope.row.fullName || '-' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="文章DOI" prop="articleId" align="center" show-overflow-tooltip />
                <el-table-column label="页码范围" align="center" width="100">
                  <template #default="scope">
                    {{ formatPageRange(scope.row) }}
                  </template>
                </el-table-column>
                <el-table-column label="更新时间" prop="updateTime" align="center" width="160" />
              </el-table>

              <!-- 分页组件 -->
              <div class="pagination-container">
                <pagination
                  v-if="articleTotal > 0"
                  :total="articleTotal"
                  v-model:page="articleQueryParams.pageNum"
                  v-model:limit="articleQueryParams.pageSize"
                  @pagination="getArticleList"
                  :small="true"
                  layout="prev, pager, next"
                />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 收起状态显示恢复按钮 -->
    <div class="module-collapsed" v-if="!showJournalDetail">
      <div class="module-header">
        <span class="module-title">品种详情</span>
        <el-button link class="collapse-btn" @click="showJournalDetail = true">展开</el-button>
      </div>
    </div>

    <!-- 挂接品种数据模块 -->
    <div class="module-container" v-show="showRelateJournal">
      <div class="module-header">
        <span class="module-title">挂接品种数据</span>
        <el-button link class="collapse-btn" @click="showRelateJournal = false">收起</el-button>
      </div>
      <div class="module-content">
            <div class="volume-table-container">
              <el-table
                  :data="relateJournalList"
                  border
                  style="width: 100%"
                  :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
              >
                <el-table-column label="单源品种ID" prop="singleJournalId" align="center" show-overflow-tooltip />
                <el-table-column label="数据源" prop="dataSource" align="center" />
                <el-table-column label="期刊题名" prop="journalTitle" align="center" show-overflow-tooltip />
                <el-table-column label="ISSN" prop="issn" align="center" />
                <el-table-column label="年/卷/期区间" prop="interval" align="center" />
                <el-table-column label="更新时间" prop="updateTime" align="center" />
              </el-table>

              <!-- 分页组件 -->
              <div class="pagination-container">
                <pagination
                  v-show="relateTotal > 0"
                  :total="relateTotal"
                  v-model:page="relateQueryParams.pageNum"
                  v-model:limit="relateQueryParams.pageSize"
                  @pagination="getRelateJournalList"
                  :small="true"
                  layout="prev, pager, next"
                />
              </div>
            </div>
      </div>
    </div>

    <!-- 收起状态显示恢复按钮 -->
    <div class="module-collapsed" v-if="!showRelateJournal">
      <div class="module-header">
        <span class="module-title">挂接品种数据</span>
        <el-button link class="collapse-btn" @click="showRelateJournal = true">展开</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { getMutchJournalInfo, getMutchJournalVolumes, getMutchJournalArticles, listRelatedMutchJournals } from "@/api/storageService/storageService";

const route = useRoute();

// 模块显示状态控制
const showBasicInfo = ref(true);
const showJournalDetail = ref(true);
const showRelateJournal = ref(true);

// 获取路由参数中的期刊ID
const journalId = ref(route.params.journalId || route.query.journalId);

// 当前激活的标签页
const activeTab = ref('volumes');

// 品种基本信息
const journalInfo = ref({});

// 分页和查询参数
const data = reactive({
  // 查询参数
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    journalId: '',
  },
  total: 0
});

// 篇级分页和查询参数
const articleData = reactive({
  // 查询参数
  articleQueryParams: {
    pageNum: 1,
    pageSize: 10,
    journalId: '',
  },
  articleTotal: 0
});

// 分页和查询参数 - 挂接品种
const relateData = reactive({
  // 查询参数
  relateQueryParams: {
    pageNum: 1,
    pageSize: 10,
    journalId: '',
  },
  relateTotal: 0
});

// 在data部分添加卷期分页参数
const volumeQueryParams = ref({
  pageNum: 1,
  pageSize: 10
});

const { queryParams, total } = toRefs(data);
const { articleQueryParams, articleTotal } = toRefs(articleData);
const { relateQueryParams, relateTotal } = toRefs(relateData);

// 卷期列表数据
const volumeList = ref([]);

// 篇级详情数据
const articleDetailList = ref([]);

// 挂接品种数据
const relateJournalList = ref([]);

// 添加activeVolume变量用于存储当前选中的卷期
const activeVolume = ref(null);

// 获取期刊详情
async function getJournalDetail() {
  try {
    const res = await getMutchJournalInfo(journalId.value);
    journalInfo.value = res.data;
  } catch (error) {
    console.error('获取多源品种详情失败:', error);
  }
}

// 获取卷期列表
async function getVolumeList() {
  try {
    const res = await getMutchJournalVolumes(journalId.value, volumeQueryParams.value);
    volumeList.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error('获取卷期列表失败:', error);
  }
}

// 卷期分页处理
function handleVolumePagination({ page, limit }) {
  volumeQueryParams.value.pageNum = page;
  volumeQueryParams.value.pageSize = limit;
  getVolumeList();
}

// 获取篇级列表
async function getArticleList() {
  try {
    // 添加卷期筛选参数
    const params = {
      ...articleQueryParams.value,
      pageNum: articleQueryParams.value.pageNum,
      pageSize: articleQueryParams.value.pageSize
    };
    
    // 只有在有活动卷期筛选时才添加筛选参数
    if (activeVolume.value) {
      params.year = activeVolume.value.year;
      params.volume = activeVolume.value.volume;
      params.issue = activeVolume.value.issue;
    }
    
    const res = await getMutchJournalArticles(journalId.value, params);
    articleDetailList.value = res.rows || [];
    articleTotal.value = res.total || 0;
    
    // 打印日志，便于调试
    console.log('篇级数据:', articleDetailList.value);
  } catch (error) {
    console.error('获取多源品种篇级列表失败:', error);
    articleDetailList.value = [];
    articleTotal.value = 0;
  }
}

// 获取挂接品种列表
async function getRelateJournalList() {
  try {
    const res = await listRelatedMutchJournals(journalId.value);
    relateJournalList.value = res.rows;
    relateTotal.value = res.total;
  } catch (error) {
    console.error('获取挂接品种列表失败:', error);
  }
}

// 添加卷期点击事件处理
function handleVolumeClick(row) {
  activeVolume.value = row;
  activeTab.value = 'article';
  articleQueryParams.value.pageNum = 1;
  articleQueryParams.value.pageSize = 10;
  getArticleList();
}

// 添加清除卷期筛选的方法
function clearVolumeFilter() {
  activeVolume.value = null;
  articleQueryParams.value.pageNum = 1;
  articleQueryParams.value.pageSize = 10;
  getArticleList();
}

// 添加标签页切换事件处理
function handleTabClick(tab) {
  if (tab.props.name === 'article' && articleDetailList.value.length === 0) {
    // 切换到篇级详情标签页时，如果数据为空则加载数据
    getArticleList();
  }
}

// 页面加载时初始化数据
onMounted(() => {
  queryParams.value.journalId = journalId.value;
  articleQueryParams.value.journalId = journalId.value;
  relateQueryParams.value.journalId = journalId.value;
  
  getJournalDetail();
  getVolumeList();
  getArticleList();
  getRelateJournalList();
});

function formatArticleTitle(title) {
  if (!title) return '-';
  
  // 移除末尾的句点（如果有）
  let formattedTitle = title.endsWith('.') ? title.slice(0, -1) : title;
  
  // 适当处理带方括号的标题，保留方括号但确保它们显示正确
  // 这里我们不移除方括号，因为它们可能是标题的一部分
  // 但我们可以确保它们在UI中正确显示
  
  return formattedTitle;
}

function formatYearVolumeIssue(row) {
  // 组合年卷期信息，处理可能缺失的情况
  const year = row.year || '-';
  const volume = row.volume || '-';
  const issue = row.issue || '-';
  
  if (year === '-' && volume === '-' && issue === '-') {
    // 如果年卷期都缺失，则尝试使用interval字段
    return row.interval || '-';
  }
  
  return `${year}年 ${volume}卷 ${issue}期`;
}

function formatPageRange(row) {
  if (!row.firstPage && !row.lastPage) return '-';
  
  const firstPage = row.firstPage || '-';
  const lastPage = row.lastPage || '-';
  
  if (firstPage === lastPage) {
    return firstPage;
  }
  
  return `${firstPage}-${lastPage}`;
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}


.module-container {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.module-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.collapse-btn {
  color: #409EFF;
  font-size: 14px;
}

.module-content {
  padding: 20px;
}

.module-collapsed {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.info-row {
  display: flex;
  line-height: 20px;
  font-size: 14px;
}

.info-label {
  color: #606266;
  font-weight: 500;
  min-width: 150px;
  text-align: right;
  padding-right: 15px;
}

.info-value {
  color: #303133;
  flex: 1;
}

.abstract-row {
  align-items: flex-start;
}

.abstract-row .info-value {
  line-height: 1.6;
}

.detail-tabs {
  width: 100%;
}

.volume-table-container {
  margin-top: 10px;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__item) {
  height: 40px;
  line-height: 40px;
  padding: 0 20px;
}

.placeholder {
  padding: 50px 0;
  text-align: center;
  color: #909399;
  font-size: 14px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.xml-content {
  background-color: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 15px;
  height: 100%;
  overflow: auto;
}

.xml-code {
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  margin: 0;
  white-space: pre-wrap;
}

.xml-code .tag {
  color: #0000ff;
}

.xml-code .attr {
  color: #ff0000;
}

.xml-code .string {
  color: #008000;
}

.tab-header {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.tab-header span {
  padding: 8px 16px;
  cursor: pointer;
  color: #606266;
  font-size: 14px;
}

.tab-header span.active-tab {
  color: #409EFF;
  border-bottom: 2px solid #409EFF;
}

.pagination-container {
  margin-top: 10px;
  text-align: center;
  overflow: hidden;
}

/* 设置分页组件样式 */
:deep(.el-pagination) {
  justify-content: center;
  white-space: nowrap;
  padding: 0;
}

:deep(.el-pagination .el-pagination__total) {
  font-size: 12px;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  padding: 0 6px;
}

:deep(.el-pagination .el-pager li) {
  min-width: 24px;
}

.filter-tips {
  margin-bottom: 10px;
  padding: 8px 15px;
  background-color: #f0f9eb;
  color: #67c23a;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
}

.filter-info {
  margin-bottom: 10px;
  padding: 8px 15px;
  background-color: #f0f9eb;
  color: #606266;
  border-radius: 4px;
  font-size: 14px;
}
</style>