<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="search-form">
        <el-form-item label="卷期ID" prop="issueId">
          <el-input
              v-model="queryParams.issueId"
              placeholder="请输入品种ID"
              clearable
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="期刊题名" prop="journalTitle">
          <el-input
              v-model="queryParams.journalTitle"
              placeholder="请输入期刊题名"
              clearable
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="ISSN" prop="issn">
          <el-input
              v-model="queryParams.issn"
              placeholder="请输入ISSN"
              clearable
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="数据状态" prop="state">
          <el-select v-model="queryParams.state" placeholder="数据状态" clearable style="width: 200px" @keyup.enter.native="handleQuery">
            <el-option value="0" label="停用">停用</el-option>
            <el-option value="1" label="启用">启用</el-option>
          </el-select>
        </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <div class="flex-container">
        <div class="table-container" :class="{ 'table-collapsed': drawerOpen }">
        <el-row :gutter="10" class="mb8">
           <right-toolbar 
             v-model:showSearch="showSearch" 
             @queryTable="getList"
             :columns="columnList"
             :show-columns-type="'checkbox'"
             @checkbox-change="handleColumnChange"
           ></right-toolbar>
        </el-row>

      <el-table v-loading="loading" :data="dataSourceList" @row-click="handleRowClick">
        <template v-for="col in columnList" :key="col.key">
          <el-table-column
            v-if="col.visible"
            :label="col.label"
            :prop="col.key"
            align="center"
            show-overflow-tooltip
          />
        </template>
        <el-table-column label="操作" width="180" align="center" class-name="small-padding fixed-width" fixed="right">
          <template #default="scope">
            <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click.stop="handleDetail(scope.row)"
            >详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 添加深度翻页状态提示 -->
      <div v-if="isDeepPaging" class="deep-paging-tip">
        <el-alert
          title="当前处于深度翻页模式"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <div>在深度翻页模式下，只能向后翻页，每次只能翻一页。如需重新查询，请点击搜索或重置按钮。</div>
          </template>
        </el-alert>
      </div>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="handlePagination"
      />
        </div>

        <!-- 右侧抽屉式表单 -->
        <div class="drawer-container" v-show="drawer.visible" :class="{ 'open': drawer.visible }">
          <div class="drawer-header">
            <div class="drawer-title">卷期详情</div>
            <el-button class="drawer-close" @click="cancel" icon="Close" circle></el-button>
          </div>
          <div class="drawer-body">
                <div class="journal-info">
                  <div class="info-item">
                <span class="label">卷期ID:</span>
                    <span class="value">{{ issueForm.fusionIssueId }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">期刊题名:</span>
                    <span class="value">{{ issueForm.sourceTitle }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">ISSN:</span>
                    <span class="value">{{ issueForm.issn }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">出版商:</span>
                    <span class="value">{{ issueForm.publisherName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">出版年份:</span>
                    <span class="value">{{ issueForm.year }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">卷号:</span>
                    <span class="value">{{ issueForm.volume }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">期号:</span>
                    <span class="value">{{ issueForm.issue }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">语种:</span>
                    <span class="value">{{ issueForm.language }}</span>
                  </div>
                </div>
          </div>
        </div>
      </div>
     <!-- 挂接卷期数据表格（移到flex-container外部） -->
     <div class="related-data-section" :class="{ 'table-collapsed': drawerOpen }" v-if="showRelatedData">
       <div class="section-title">挂接卷期数据</div>
       <div class="related-table-container">
         <el-table
             :data="relatedIssues"
             border
             style="width: 100%"
             :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
         >
           <el-table-column label="单源卷期ID" prop="singleIssueId" align="center" show-overflow-tooltip />
           <el-table-column label="数据源" prop="dataSource" align="center" show-overflow-tooltip />
           <el-table-column label="期刊题名" prop="journalTitle" align="center" show-overflow-tooltip />
           <el-table-column label="ISSN" prop="issn" align="center" show-overflow-tooltip />
           <el-table-column label="年份" prop="year" align="center" show-overflow-tooltip />
           <el-table-column label="卷号" prop="volume" align="center" show-overflow-tooltip />
           <el-table-column label="期号" prop="issue" align="center" show-overflow-tooltip />
           <el-table-column label="更新时间" prop="updateTime" align="center" show-overflow-tooltip />
         </el-table>

         <!-- 分页组件 -->
         <pagination
             v-show="relatedTotal > 0"
             :total="relatedTotal"
             v-model:page="relatedQueryParams.pageNum"
             v-model:limit="relatedQueryParams.pageSize"
             @pagination="getRelatedIssues"
         />
       </div>
     </div>
   </div>
</template>

<script setup name="storageService">
import { listIssues, listIssuesScroll, listRelatedMutchIssues, getMutchIssueInfo } from "@/api/storageService/storageService";
import { useRouter } from "vue-router";

const router = useRouter();
const { proxy } = getCurrentInstance();
const { storage_last_state } = proxy.useDict("storage_last_state");

// 列配置
const columnList = ref([
  { key: 'fusionIssueId', label: '卷期ID', visible: true },
  { key: 'sourceTitle', label: '期刊题名', visible: true },
  { key: 'issn', label: 'ISSN', visible: true },
  { key: 'year', label: '年份', visible: true },
  { key: 'volume', label: '卷号', visible: true },
  { key: 'issue', label: '期号', visible: true },
  { key: 'articleNum', label: '篇级数量', visible: true },
  { key: 'language', label: '语种', visible: true },
  { key: 'status', label: '数据状态', visible: true },
  { key: 'updateTime', label: '更新时间', visible: true }
]);

// 处理列显示变化
const handleColumnChange = (checked, label) => {
  const newColumnList = columnList.value.map(col => {
    if (col.label === label) {
      return { ...col, visible: checked };
    }
    return col;
  });
  columnList.value = newColumnList; // 通过整体替换触发响应式更新
};

const dataSourceList = ref([]);
const drawerOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
// 添加深度翻页相关变量
const scrollId = ref('');
const isDeepPaging = ref(false);
const currentPage = ref(1);
const lastAllowedPage = ref(1);
const MAX_NORMAL_COUNT = 10000; // ES默认最大可查询数据条数

const drawer = ref({
  visible: false
});

const issueForm = ref({
  fusionIssueId: '',
  sourceTitle: '',
  issn: '',
  publisherName: '',
  pubYear: '',
  volume: '',
  issue: '',
  language: '',
  articleNum: 0
});

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    issueId: null,
    journalTitle: null,
    state: null,
    issn: null
  }
});

const { queryParams } = toRefs(data);

// 挂接篇级数据列表
const relatedIssues = ref([]);

// 挂接篇级数据查询参数
const relatedQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  issueId: ''
});

// 挂接篇级数据总数
const relatedTotal = ref(0);

// 添加控制关联表格显示的变量
const showRelatedData = ref(false);

/** 查询卷期列表 */
function getList() {
  loading.value = true;
  const params = {
    ...queryParams.value,
    scrollId: scrollId.value
  };

  // 根据是否深度翻页选择不同的接口
  if (isDeepPaging.value) {
    listIssuesScroll(params).then(response => {
      if (response.rows && response.rows.length > 0) {
        // 如果是深度翻页,需要保存新的scrollId
        if (response.rows[0].scrollId) {
          scrollId.value = response.rows[0].scrollId;
        }
        dataSourceList.value = response.rows;
        total.value = response.total; // 深度翻页模式下使用固定值
      } else {
        // 如果没有数据,重置scrollId
        scrollId.value = '';
        isDeepPaging.value = false;
        dataSourceList.value = [];
        total.value = 0;
      }
      loading.value = false;
    });
  } else {
    listIssues(params).then(response => {
      dataSourceList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }
}

/**
 * 点击详情按钮，跳转到详情页面
 * @param {Object} row 行数据
 */
function handleDetail(row) {
  router.push({
    path: '/storageService/mutch/issue/detail',
    query: {
      issueId: row.fusionIssueId
    }
  });
}

async function handleRowClick(row) {
  try {
    // 获取多源卷期详情
    const res = await getMutchIssueInfo(row.fusionIssueId);
    issueForm.value = res.data;
    
    // 设置关联查询参数
    relatedQueryParams.value.issueId = row.fusionIssueId;

    // 获取关联数据
    getRelatedIssues();
    
    // 显示抽屉和关联数据
    drawer.value.visible = true;
    drawerOpen.value = true;
    showRelatedData.value = true;
  } catch (error) {
    console.error('获取卷期详情失败:', error);
  }
}

/** 分页组件事件处理 */
function handlePagination({ page, limit }) {
  // 计算即将查询的数据位置
  const targetDataCount = page * limit;

  // 如果已经是深度翻页模式
  if (isDeepPaging.value) {
    // 只允许向后翻页
    if (page < currentPage.value) {
      proxy.$modal.msgWarning('深度翻页模式下不支持向前翻页,请重新执行查询');
      queryParams.value.pageNum = currentPage.value;
      return;
    }
    // 只允许翻到下一页
    if (page > currentPage.value + 1) {
      proxy.$modal.msgWarning('深度翻页模式下只能一页一页向后翻');
      queryParams.value.pageNum = currentPage.value;
      return;
    }
  }
  // 首次跳页时的处理
  else if (!isDeepPaging.value) {
    // 如果即将查询的数据超过最大限制
    if (targetDataCount > MAX_NORMAL_COUNT) {
      // 计算最后允许的正常页码
      const maxAllowedPage = Math.ceil(MAX_NORMAL_COUNT / limit);
      
      proxy.$modal.confirm(
        `查询数据超过${MAX_NORMAL_COUNT}条，系统将自动跳转到第${maxAllowedPage}页并进入深度翻页模式，是否继续？`,
        '提示',
        {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          // 跳转到最大允许页
          page = maxAllowedPage;
          isDeepPaging.value = true;
          scrollId.value = '';
          currentPage.value = page;
          lastAllowedPage.value = page;
          queryParams.value.pageNum = page;
          queryParams.value.pageSize = limit;
          getList();
        })
        .catch(() => {
          queryParams.value.pageNum = lastAllowedPage.value;
        });
      return;
    }
  }

  currentPage.value = page;
  lastAllowedPage.value = page;
  queryParams.value.pageNum = page;
  queryParams.value.pageSize = limit;
  getList();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  scrollId.value = '';
  isDeepPaging.value = false;
  currentPage.value = 1;
  lastAllowedPage.value = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  scrollId.value = '';
  isDeepPaging.value = false;
  currentPage.value = 1;
  lastAllowedPage.value = 1;
  handleQuery();
}

// 获取挂接卷期数据
async function getRelatedIssues() {
  try {
    const params = {
      pageNum: relatedQueryParams.value.pageNum,
      pageSize: relatedQueryParams.value.pageSize
    };
    
    const res = await listRelatedMutchIssues(relatedQueryParams.value.issueId, params);
    if (res && res.rows) {
      relatedIssues.value = res.rows;
      relatedTotal.value = res.total || 0;
    } else {
      relatedIssues.value = [];
      relatedTotal.value = 0;
    }
  } catch (error) {
    console.error('获取挂接卷期数据失败:', error);
    relatedIssues.value = [];
    relatedTotal.value = 0;
  }
}

/** 取消按钮 */
function cancel() {
  drawer.value.visible = false;
  drawerOpen.value = false;
  resetDrawerForm();
  // 隐藏挂接卷期数据表格
  showRelatedData.value = false;
}

/** 重置抽屉表单 */
function resetDrawerForm() {
  issueForm.value = {
    fusionIssueId: '',
    sourceTitle: '',
    issn: '',
    publisherName: '',
    pubYear: '',
    volume: '',
    issue: '',
    language: '',
    articleNum: 0
  };
  // 清空关联数据
  relatedIssues.value = [];
  relatedTotal.value = 0;
}

/** 初始加载 */
getList();
</script>
<style scoped>
.flex-container {
  display: flex;
  position: relative;
  width: 100%;
  overflow: visible;
  margin-bottom: 20px;
}

.table-container {
  flex-grow: 1;
  transition: all 0.3s ease;
  width: 100%;
  margin-right: 0;
}

.table-container.table-collapsed {
  width: calc(100% - 450px);
  margin-right: 450px;
}

.drawer-container {
  position: absolute;
  right: 0;
  top: 0;
  width: 430px;
  background-color: #fff;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  border-left: 1px solid #e6e6e6;
  z-index: 100;
  height: calc(100vh - 185px);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.drawer-container.open {
  transform: none;
}

.drawer-header {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.drawer-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.drawer-close {
  padding: 8px;
  font-size: 18px;
  color: #909399;
  transition: color 0.2s;
}

.drawer-close:hover {
  color: #409EFF;
}

.drawer-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 20px;
}

.journal-info {
  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 14px;

    .label {
      color: #606266;
      font-weight: 500;
      min-width: 120px;
      margin-right: 10px;
    }

    .value {
      color: #303133;
      flex: 1;
      word-break: break-all;
}
  }
}

/* 响应式样式调整 */
@media screen and (max-width: 1400px) {
  .drawer-container {
    width: 380px;
  }

  .table-container.table-collapsed {
    width: calc(100% - 400px);
    margin-right: 400px;
  }
}

.related-data-section {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
  width: 100%;
  clear: both;
  transition: all 0.3s ease;
}

.related-data-section.table-collapsed {
  width: calc(100% - 450px);
  margin-right: 450px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
}

.related-table-container {
  margin-bottom: 40px;
}

/* 深度翻页提示样式 */
.deep-paging-tip {
  margin: 10px 0;
}

.deep-paging-tip :deep(.el-alert__title) {
  font-size: 14px;
  font-weight: bold;
}

.deep-paging-tip :deep(.el-alert__content) {
  width: 100%;
}
</style>

