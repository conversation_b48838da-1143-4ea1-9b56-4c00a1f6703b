<template>
  <div class="app-container">
    <!-- 基本信息模块 -->
    <div class="module-container" v-show="showBasicInfo">
      <div class="module-header">
        <span class="module-title">基本信息</span>
        <el-button link class="collapse-btn" @click="showBasicInfo = false">收起</el-button>
      </div>
      <div class="module-content">
        <div class="info-grid">
          <div class="info-row">
            <span class="info-label">卷期ID:</span>
            <span class="info-value">{{ issueInfo.fusionIssueId }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">期刊题名:</span>
            <span class="info-value">{{ issueInfo.sourceTitle }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">ISSN:</span>
            <span class="info-value">{{ issueInfo.issn }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">出版商:</span>
            <span class="info-value">{{ issueInfo.publisherName }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">出版年份:</span>
            <span class="info-value">{{ issueInfo.year }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">语种:</span>
            <span class="info-value">{{ issueInfo.language }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">卷号:</span>
            <span class="info-value">{{ issueInfo.volume }}</span>
          </div>
          <div class="info-row abstract-row">
            <span class="info-label">期号:</span>
            <span class="info-value">{{ issueInfo.issue }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 收起状态显示恢复按钮 -->
    <div class="module-collapsed" v-if="!showBasicInfo">
      <div class="module-header">
        <span class="module-title">基本信息</span>
        <el-button link class="collapse-btn" @click="showBasicInfo = true">展开</el-button>
      </div>
    </div>

    <!-- 品种详情模块 -->
    <div class="module-container" v-show="showIssueDetail">
      <div class="module-header">
        <span class="module-title">关联篇级</span>
        <el-button link class="collapse-btn" @click="showIssueDetail = false">收起</el-button>
      </div>
      <div class="module-content">
            <div class="volume-table-container">
              <el-table
                :data="articleList"
                border
                style="width: 100%"
                :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
              >
                <el-table-column label="篇级ID" prop="fusionArticleId" align="center" width="160" show-overflow-tooltip />
                <el-table-column label="篇级题名" prop="articleTitle" align="center" show-overflow-tooltip />
                <el-table-column label="作者" prop="fullName" align="center" width="160" show-overflow-tooltip />
                <el-table-column label="DOI" prop="doi" align="center" width="120" show-overflow-tooltip />
                <el-table-column label="页码范围" align="center" width="100" show-overflow-tooltip>
                  <template #default="scope">
                    <span>{{ formatPageRange(scope.row) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="更新时间" prop="updateTime" align="center" width="160" show-overflow-tooltip />
              </el-table>

              <!-- 分页组件 -->
              <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="handlePagination"
              />
            </div>
      </div>
    </div>

    <!-- 收起状态显示恢复按钮 -->
    <div class="module-collapsed" v-if="!showIssueDetail">
      <div class="module-header">
        <span class="module-title">关联篇级</span>
        <el-button link class="collapse-btn" @click="showIssueDetail = true">展开</el-button>
      </div>
    </div>

    <!-- 挂接品种数据模块 -->
    <div class="module-container" v-show="showRelateIssue">
      <div class="module-header">
        <span class="module-title">挂接卷期数据</span>
        <el-button link class="collapse-btn" @click="showRelateIssue = false">收起</el-button>
      </div>
      <div class="module-content">
            <div class="volume-table-container">
              <el-table
                  :data="relateIssueList"
                  border
                  style="width: 100%"
                  :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
              >
                <el-table-column label="单源卷期ID" prop="singleIssueId" align="center" width="160" show-overflow-tooltip />
                <el-table-column label="数据源" prop="dataSource" align="center" width="100" show-overflow-tooltip />
                <el-table-column label="期刊题名" prop="journalTitle" align="center" show-overflow-tooltip />
                <el-table-column label="ISSN" prop="issn" align="center" width="100" show-overflow-tooltip />
                <el-table-column label="年份" prop="year" align="center" width="80" show-overflow-tooltip />
                <el-table-column label="卷号" prop="volume" align="center" width="80" show-overflow-tooltip />
                <el-table-column label="期号" prop="issue" align="center" width="80" show-overflow-tooltip />
                <el-table-column label="更新时间" prop="updateTime" align="center" width="160" show-overflow-tooltip />
              </el-table>

              <!-- 分页组件 -->
              <pagination
                  v-show="relateTotal > 0"
                  :total="relateTotal"
                  v-model:page="relateQueryParams.pageNum"
                  v-model:limit="relateQueryParams.pageSize"
                  @pagination="handleRelatePagination"
              />
            </div>
      </div>
    </div>

    <!-- 收起状态显示恢复按钮 -->
    <div class="module-collapsed" v-if="!showRelateIssue">
      <div class="module-header">
        <span class="module-title">挂接卷期数据</span>
        <el-button link class="collapse-btn" @click="showRelateIssue = true">展开</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { getMutchIssueInfo, getMutchIssueArticles, listRelatedMutchIssues } from "@/api/storageService/storageService";

const route = useRoute();

// 模块显示状态控制
const showBasicInfo = ref(true);
const showIssueDetail = ref(true);
const showRelateIssue = ref(true);
// 获取路由参数中的期刊ID
const issueId = ref(route.params.issueId || route.query.issueId);

// 卷期基本信息
const issueInfo = ref({});

// 分页和查询参数
const data = reactive({
  // 查询参数
  queryParams: {
    pageNum: 1,
    pageSize: 10
  },
  total: 0
});

// 分页和查询参数 - 挂接卷期
const relateData = reactive({
  // 查询参数
  relateQueryParams: {
    pageNum: 1,
    pageSize: 10
  },
  relateTotal: 0
});

const { queryParams, total } = toRefs(data);
const { relateQueryParams, relateTotal } = toRefs(relateData);

// 篇级列表数据
const articleList = ref([]);

// 挂接卷期数据
const relateIssueList = ref([]);

// 获取卷期详情
async function getIssueDetail() {
  try {
    const res = await getMutchIssueInfo(issueId.value);
    if (res && res.data) {
      issueInfo.value = res.data;
    } else {
      console.error('获取卷期详情返回数据为空');
    }
  } catch (error) {
    console.error('获取卷期详情失败:', error);
  }
}

// 获取篇级列表
async function getArticleList() {
  try {
    const params = {
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize
    };
    
    const res = await getMutchIssueArticles(issueId.value, params);
    if (res && res.rows) {
      articleList.value = res.rows;
      total.value = res.total || 0;
    } else {
      articleList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取篇级列表失败:', error);
    articleList.value = [];
    total.value = 0;
  }
}

// 获取挂接卷期列表
async function getRelateIssueList() {
  try {
    const params = {
      pageNum: relateQueryParams.value.pageNum,
      pageSize: relateQueryParams.value.pageSize
    };
    
    const res = await listRelatedMutchIssues(issueId.value, params);
    if (res && res.rows) {
      relateIssueList.value = res.rows;
      relateTotal.value = res.total || 0;
    } else {
      relateIssueList.value = [];
      relateTotal.value = 0;
    }
  } catch (error) {
    console.error('获取挂接卷期列表失败:', error);
    relateIssueList.value = [];
    relateTotal.value = 0;
  }
}

// 处理分页事件
function handlePagination({ page, limit }) {
  queryParams.value.pageNum = page;
  queryParams.value.pageSize = limit;
  getArticleList();
}

// 处理关联卷期分页事件
function handleRelatePagination({ page, limit }) {
  relateQueryParams.value.pageNum = page;
  relateQueryParams.value.pageSize = limit;
  getRelateIssueList();
}

// 格式化页码范围
function formatPageRange(row) {
  if (!row.firstPage && !row.lastPage) return '-';
  
  const firstPage = row.firstPage || '-';
  const lastPage = row.lastPage || '-';
  
  if (firstPage === lastPage) {
    return firstPage;
  }
  
  return `${firstPage}-${lastPage}`;
}

// 页面加载时初始化数据
onMounted(async () => {
  if (!issueId.value) {
    console.error('卷期ID未提供');
    return;
  }
  
  // 先获取卷期详情
  await getIssueDetail();
  
  // 再获取关联数据
  getArticleList();
  getRelateIssueList();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}


.module-container {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.module-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.collapse-btn {
  color: #409EFF;
  font-size: 14px;
}

.module-content {
  padding: 20px;
}

.module-collapsed {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.info-row {
  display: flex;
  line-height: 20px;
  font-size: 14px;
}

.info-label {
  color: #606266;
  font-weight: 500;
  min-width: 150px;
  text-align: right;
  padding-right: 15px;
}

.info-value {
  color: #303133;
  flex: 1;
}

.abstract-row {
  align-items: flex-start;
}

.abstract-row .info-value {
  line-height: 1.6;
}

.detail-tabs {
  width: 100%;
}

.volume-table-container {
  margin-top: 10px;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__item) {
  height: 40px;
  line-height: 40px;
  padding: 0 20px;
}

.placeholder {
  padding: 50px 0;
  text-align: center;
  color: #909399;
  font-size: 14px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.xml-content {
  background-color: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 15px;
  height: 100%;
  overflow: auto;
}

.xml-code {
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  margin: 0;
  white-space: pre-wrap;
}

.xml-code .tag {
  color: #0000ff;
}

.xml-code .attr {
  color: #ff0000;
}

.xml-code .string {
  color: #008000;
}

.tab-header {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.tab-header span {
  padding: 8px 16px;
  cursor: pointer;
  color: #606266;
  font-size: 14px;
}

.tab-header span.active-tab {
  color: #409EFF;
  border-bottom: 2px solid #409EFF;
}
</style>