<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="search-form">
        <el-form-item label="篇级ID" prop="articleId">
          <el-input
              v-model="queryParams.articleId"
              placeholder="请输入篇级ID"
              clearable
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="篇级题名" prop="articleTitle">
          <el-input
              v-model="queryParams.articleTitle"
              placeholder="请输入篇级题名"
              clearable
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="ISSN" prop="issn">
          <el-input
              v-model="queryParams.issn"
              placeholder="请输入ISSN"
              clearable
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="数据状态" prop="state">
          <el-select v-model="queryParams.state" placeholder="数据状态" clearable style="width: 200px" @keyup.enter.native="handleQuery">
            <el-option value="0" label="停用">停用</el-option>
            <el-option value="1" label="启用">启用</el-option>
          </el-select>
        </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <div class="flex-container">
        <div class="table-container" :class="{ 'table-collapsed': drawerOpen }">
        <el-row :gutter="10" class="mb8">
           <right-toolbar 
             v-model:showSearch="showSearch" 
             @queryTable="getList"
             :columns="columnList"
             :show-columns-type="'checkbox'"
             @checkbox-change="handleColumnChange"
           ></right-toolbar>
        </el-row>

      <el-table v-loading="loading" :data="dataSourceList" @row-click="handleRowClick">
        <template v-for="col in columnList" :key="col.key">
          <el-table-column
            v-if="col.visible"
            :label="col.label"
            :prop="col.key"
            align="center"
            show-overflow-tooltip
          />
        </template>
        <el-table-column label="操作" width="180" align="center" class-name="small-padding fixed-width" fixed="right">
          <template #default="scope">
            <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click.stop="handleDetail(scope.row)"
            >详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="handlePagination"
         :disabled="isDeepPaging"
         :hide-on-single-page="false"
      />
      
      <!-- 深度翻页模式提示 -->
      <div v-if="isDeepPaging" class="deep-paging-tip">
        <el-alert
          title="当前处于深度翻页模式"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>1. 只能向后翻页,不能跳页或向前翻页</p>
            <p>2. 如需重新查询,请使用搜索或重置按钮</p>
            <p>3. 当前页码: {{ currentPage }}</p>
          </template>
        </el-alert>
      </div>
        </div>

        <!-- 右侧抽屉式表单 -->
        <div class="drawer-container" v-show="drawer.visible" :class="{ 'open': drawer.visible }">
          <div class="drawer-header">
            <div class="drawer-title">篇级详情</div>
            <el-button class="drawer-close" @click="cancel" icon="Close" circle></el-button>
          </div>
          <div class="drawer-body">
            <!-- Tab 切换 -->
            <div class="drawer-tabs">
            <el-tabs v-model="activeTab">
              <el-tab-pane label="列表展示" name="col">
                  <div class="drawer-content">
                <div class="journal-info">
                  <div class="info-item">
                    <span class="label">Source Id:</span>
                    <span class="value">{{ articleForm.fusionArticleId }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Source Title:</span>
                    <span class="value">{{ articleForm.sourceTitle || articleForm.journalTitle }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">ISSN:</span>
                    <span class="value">{{ articleForm.issn }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Publisher Name:</span>
                    <span class="value">{{ articleForm.publisherName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Year:</span>
                    <span class="value">{{ articleForm.year }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Volume:</span>
                    <span class="value">{{ articleForm.volume }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Issue:</span>
                    <span class="value">{{ articleForm.issue }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Language:</span>
                    <span class="value">{{ articleForm.language }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Article Id:</span>
                    <span class="value">{{ articleForm.fusionArticleId }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Article Title:</span>
                    <span class="value">{{ articleForm.articleTitle }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">First Page:</span>
                    <span class="value">{{ articleForm.firstPage }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">LastPage:</span>
                    <span class="value">{{ articleForm.lastPage }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Author FullName:</span>
                    <span class="value">{{ articleForm.fullName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Abstract:</span>
                    <span class="value">{{ articleForm.abstract }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Keyword:</span>
                    <span class="value">{{ articleForm.keyword }}</span>
                  </div>
                </div>
              </div>
              </el-tab-pane>
              
              <el-tab-pane label="XML展示" name="xml">
                  <div class="drawer-content">
                <div class="xml-content">
                  <pre class="xml-code">
&lt;?record xmlns:xlink="http://www.w3.org/1999/xlink"&gt;
  &lt;source-meta&gt;
    &lt;publisher&gt;
      &lt;institution-wrap&gt;
        &lt;institution&gt;{{ articleForm.publisherName || '内容' }}&lt;/institution&gt;
      &lt;/institution-wrap&gt;
    &lt;/publisher&gt;
    &lt;source-title-group&gt;
      &lt;source-title xml:lang="eng"&gt;{{ articleForm.sourceTitle || articleForm.journalTitle || '内容' }}&lt;/source-title&gt;
    &lt;/source-title-group&gt;
    &lt;issn publication-format="print"&gt;{{ articleForm.issn || '内容' }}&lt;/issn&gt;
    &lt;volume-issue-group&gt;
      &lt;volume&gt;{{ articleForm.volume || '内容' }}&lt;/volume&gt;
      &lt;issue&gt;{{ articleForm.issue || '内容' }}&lt;/issue&gt;
    &lt;/volume-issue-group&gt;
    &lt;pub-date&gt;
      &lt;year&gt;{{ articleForm.year || '内容' }}&lt;/year&gt;
      &lt;month&gt;内容&lt;/month&gt;
      &lt;day&gt;内容&lt;/day&gt;
    &lt;/pub-date&gt;
  &lt;/source-meta&gt;
  &lt;article-meta&gt;
    &lt;title-group&gt;
      &lt;article-title xml:lang="eng"&gt;{{ articleForm.articleTitle || '内容' }}&lt;/article-title&gt;
    &lt;/title-group&gt;
    &lt;fpage&gt;{{ articleForm.firstPage || '内容' }}&lt;/fpage&gt;
    &lt;lpage&gt;{{ articleForm.lastPage || '内容' }}&lt;/lpage&gt;
    &lt;article-type&gt;内容&lt;/article-type&gt;
    &lt;article-id pub-id-type="内容"&gt;{{ articleForm.fusionArticleId || '内容' }}&lt;/article-id&gt;
    &lt;history&gt;
      &lt;date publication-format="内容"&gt;
        &lt;year&gt;{{ articleForm.year || '内容' }}&lt;/year&gt;
        &lt;month&gt;内容&lt;/month&gt;
        &lt;day&gt;内容&lt;/day&gt;
      &lt;/date&gt;
    &lt;/history&gt;
  &lt;/article-meta&gt;
&lt;/record&gt;
                  </pre>
                    </div>
                </div>
              </el-tab-pane>
            </el-tabs>
            </div>
          </div>
        </div>
      </div>

      <!-- 挂接篇级数据表格（移到flex-container外部） -->
      <div class="related-data-section" :class="{ 'table-collapsed': drawerOpen }" v-if="showRelatedData">
        <div class="section-title">挂接篇级数据</div>
        <div class="related-table-container">
          <el-table
            :data="relatedArticles"
            border
            style="width: 100%"
            :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          >
            <el-table-column label="篇级ID" prop="singleArticleId" align="center" width="120" show-overflow-tooltip />
            <el-table-column label="批次号" prop="batchId" align="center" width="100" show-overflow-tooltip />
            <el-table-column label="篇级题名" prop="articleTitle" align="center" show-overflow-tooltip />
            <el-table-column label="期刊题名" prop="sourceTitle" align="center" show-overflow-tooltip />
            <el-table-column label="ISSN" prop="issn" align="center" width="100" show-overflow-tooltip />
            <el-table-column label="年/卷/期" prop="interval" align="center" width="100" show-overflow-tooltip />
            <el-table-column label="作者" prop="fullName" align="center" width="160" show-overflow-tooltip />
            <el-table-column label="DOI" prop="doi" align="center" width="120" show-overflow-tooltip />
            <el-table-column label="页码范围" align="center" width="100" show-overflow-tooltip>
              <template #default="scope">
                <span>{{ formatPageRange(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="更新时间" prop="updateTime" align="center" width="160" show-overflow-tooltip />
          </el-table>
          
          <!-- 分页组件 -->
          <pagination
            v-show="relatedTotal > 0"
            :total="relatedTotal"
            v-model:page="relatedQueryParams.pageNum"
            v-model:limit="relatedQueryParams.pageSize"
            @pagination="getRelatedArticles"
          />
        </div>
      </div>
   </div>
</template>

<script setup name="storageService">
import { listMutchArticles, listMutchArticlesScroll, listRelatedMutchArticles } from "@/api/storageService/storageService";
import { useRouter } from "vue-router";

const router = useRouter();
const { proxy } = getCurrentInstance();
const { storage_last_state } = proxy.useDict("storage_last_state");

const MAX_NORMAL_COUNT = 10000; // ES默认最大可查询数据条数

const dataSourceList = ref([]);
const drawerOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const scrollId = ref('');
const isDeepPaging = ref(false);
const currentPage = ref(1);
const lastAllowedPage = ref(1);

const drawer = reactive({
  visible: false
});

const articleForm = ref({
  fusionArticleId: '',
  sourceTitle: '',
  journalTitle: '',
  issn: '',
  publisherName: '',
  year: '',
  volume: '',
  issue: '',
  language: '',
  articleTitle: '',
  firstPage: '',
  lastPage: '',
  fullName: '',
  abstract: '',
  keyword: ''
});

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    articleId: null,
    articleTitle: null,
    state: null,
    issn: null
  }
});

const { queryParams } = toRefs(data);

// Tab 激活状态
const activeTab = ref('col');

// 挂接篇级数据列表
const relatedArticles = ref([]);

// 挂接篇级数据查询参数
const relatedQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  articleId: ''
});

// 挂接篇级数据总数
const relatedTotal = ref(0);

// 添加控制关联表格显示的变量
const showRelatedData = ref(false);

// 列配置
const columnList = ref([
  { key: 'fusionArticleId', label: '篇级ID', visible: true },
  { key: 'articleTitle', label: '篇级题名', visible: true },
  { key: 'sourceTitle', label: '期刊题名', visible: true },
  { key: 'issn', label: 'ISSN', visible: true },
  { key: 'interval', label: '年/卷/期', visible: true },
  { key: 'language', label: '语种', visible: true },
  { key: 'status', label: '数据状态', visible: true },
  { key: 'updateTime', label: '更新时间', visible: true }
]);

// 处理列显示变化
const handleColumnChange = (checked, label) => {
  const newColumnList = columnList.value.map(col => {
    if (col.label === label) {
      return { ...col, visible: checked };
    }
    return col;
  });
  columnList.value = newColumnList; // 通过整体替换触发响应式更新
};

// 监听drawer.visible变化同步到drawerOpen
watch(() => drawer.visible, (val) => {
  drawerOpen.value = val;
});

/** 查询篇级列表 */
function getList() {
  loading.value = true;
  const params = {
    ...queryParams.value,
    scrollId: scrollId.value
  };

  // 根据是否深度翻页选择不同的接口
  if (isDeepPaging.value) {
    listMutchArticlesScroll(params).then(response => {
      if (response.rows && response.rows.length > 0) {
        // 保存新的scrollId
        if (response.rows[0].scrollId) {
          scrollId.value = response.rows[0].scrollId;
        }
        dataSourceList.value = response.rows;
        total.value = response.total || 100000; // 使用实际的总数，如果没有则使用默认值
      } else {
        // 如果没有数据,重置scrollId
        scrollId.value = '';
        isDeepPaging.value = false;
        dataSourceList.value = [];
        total.value = 0;
      }
      loading.value = false;
    }).catch(err => {
      console.error('深度翻页查询失败:', err);
      loading.value = false;
      dataSourceList.value = [];
      total.value = 0;
    });
  } else {
    listMutchArticles(params).then(response => {
      if (response.rows && response.rows.length > 0) {
        dataSourceList.value = response.rows;
        total.value = response.total || 0;
      } else {
        dataSourceList.value = [];
        total.value = 0;
      }
      loading.value = false;
    }).catch(err => {
      console.error('列表查询失败:', err);
      loading.value = false;
      dataSourceList.value = [];
      total.value = 0;
    });
  }
}

/**
 * 点击详情按钮，跳转到详情页面
 * @param {Object} row 行数据
 */
function handleDetail(row) {
  router.push({
    path: '/storageService/mutch/article/detail',
    query: {
      articleId: row.fusionArticleId
    }
  });
}

/** 点击行显示详情抽屉 */
function handleRowClick(row) {
  // 先设置基本数据
  articleForm.value = row;
  
  // 再进行API调用和抽屉打开
  drawer.visible = true;
  drawerOpen.value = true;
  
  // 设置关联ID
  relatedQueryParams.value.articleId = row.fusionArticleId;
  
  // 最后再显示关联数据和获取关联数据
  showRelatedData.value = true;
  getRelatedArticles();
}

/** 取消按钮 */
function cancel() {
  // 先关闭抽屉
  drawer.visible = false;
  drawerOpen.value = false;
  
  // 再隐藏关联数据表格
  showRelatedData.value = false;
  
  // 最后重置表单数据
  resetDrawerForm();
}

/** 重置抽屉表单数据 */
function resetDrawerForm() {
  articleForm.value = {
    fusionArticleId: '',
    sourceTitle: '',
    journalTitle: '',
    issn: '',
    publisherName: '',
    year: '',
    volume: '',
    issue: '',
    language: '',
    articleTitle: '',
    firstPage: '',
    lastPage: '',
    fullName: '',
    abstract: '',
    keyword: ''
  };
  // 重置标签页
  activeTab.value = 'col';
  // 清空关联数据
  relatedArticles.value = [];
  relatedTotal.value = 0;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  scrollId.value = '';
  isDeepPaging.value = false;
  currentPage.value = 1;
  lastAllowedPage.value = 1;
  getList();
  // 隐藏挂接篇级数据表格和抽屉
  showRelatedData.value = false;
  drawer.visible = false;
  drawerOpen.value = false;
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  scrollId.value = '';
  isDeepPaging.value = false;
  currentPage.value = 1;
  lastAllowedPage.value = 1;
  handleQuery();
}

/** 分页组件事件处理 */
function handlePagination({ page, limit }) {
  // 计算即将查询的数据位置
  const targetDataCount = page * limit;

  // 如果已经是深度翻页模式
  if (isDeepPaging.value) {
    // 只允许向后翻页
    if (page < currentPage.value) {
      proxy.$modal.msgWarning('深度翻页模式下不支持向前翻页,请重新执行查询');
      queryParams.value.pageNum = currentPage.value;
      return;
    }
    // 只允许翻到下一页
    if (page > currentPage.value + 1) {
      proxy.$modal.msgWarning('深度翻页模式下只能一页一页向后翻');
      queryParams.value.pageNum = currentPage.value;
      return;
    }
  }
  // 首次跳页时的处理
  else if (!isDeepPaging.value) {
    // 如果即将查询的数据超过最大限制
    if (targetDataCount > MAX_NORMAL_COUNT) {
      // 计算最后允许的正常页码
      const maxAllowedPage = Math.floor(MAX_NORMAL_COUNT / limit);
      
      proxy.$modal.confirm(
        `查询数据超过${MAX_NORMAL_COUNT}条，系统将自动跳转到第${maxAllowedPage}页并进入深度翻页模式，是否继续？`,
        '提示',
        {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          // 跳转到最大允许页
          page = maxAllowedPage;
          isDeepPaging.value = true;
          scrollId.value = '';
          currentPage.value = page;
          lastAllowedPage.value = page;
          queryParams.value.pageNum = page;
          queryParams.value.pageSize = limit;
          getList();
        })
        .catch(() => {
          queryParams.value.pageNum = lastAllowedPage.value;
        });
      return;
    }
  }

  currentPage.value = page;
  lastAllowedPage.value = page;
  queryParams.value.pageNum = page;
  queryParams.value.pageSize = limit;
  getList();
}

// 获取挂接篇级数据
async function getRelatedArticles() {
  try {
    const params = {
      pageNum: relatedQueryParams.value.pageNum,
      pageSize: relatedQueryParams.value.pageSize
    };
    
    const res = await listRelatedMutchArticles(relatedQueryParams.value.articleId, params);
    if (res && res.rows) {
      relatedArticles.value = res.rows;
      relatedTotal.value = res.total || 0;
    } else {
      relatedArticles.value = [];
      relatedTotal.value = 0;
    }
  } catch (error) {
    console.error('获取挂接篇级数据失败:', error);
    relatedArticles.value = [];
    relatedTotal.value = 0;
  }
}

// 格式化页码范围
function formatPageRange(row) {
  if (!row.firstPage && !row.lastPage) return '-';
  
  const firstPage = row.firstPage || '-';
  const lastPage = row.lastPage || '-';
  
  if (firstPage === lastPage) {
    return firstPage;
  }
  
  return `${firstPage}-${lastPage}`;
}

/** 初始加载 */
getList();
</script>
<style scoped>
.flex-container {
  display: flex;
  position: relative;
  width: 100%;
  overflow: visible;
  margin-bottom: 20px;
}

.table-container {
  flex-grow: 1;
  transition: all 0.3s ease;
  width: 100%;
  margin-right: 0;
}

.table-container.table-collapsed {
  width: calc(100% - 450px);
  margin-right: 450px;
}

.drawer-container {
  position: absolute;
  right: 0;
  top: 0;
  width: 430px;
  background-color: #fff;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  border-left: 1px solid #e6e6e6;
  z-index: 100;
  transform: translateX(100%);
  height: calc(100vh - 185px);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.drawer-container.open {
  transform: translateX(0);
}

.drawer-header {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.drawer-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.drawer-close {
  padding: 8px;
  font-size: 18px;
  color: #909399;
  transition: color 0.2s;
}

.drawer-close:hover {
  color: #409EFF;
}

.drawer-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.drawer-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-tabs) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-tabs__header) {
  margin: 0;
  padding: 10px 20px 0;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

:deep(.el-tabs__content) {
  flex: 1;
  overflow: hidden;
  position: relative;
}

:deep(.el-tab-pane) {
  height: 100%;
  overflow: hidden;
  position: relative;
}

.drawer-content {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.journal-info {
  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 14px;
    
    .label {
      color: #606266;
      font-weight: 500;
      min-width: 120px;
      margin-right: 10px;
    }
    
    .value {
      color: #303133;
      flex: 1;
      word-break: break-all;
    }
  }
}

.xml-content {
  background-color: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 15px;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
}

.xml-code {
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  margin: 0;
  white-space: pre-wrap;
}

.xml-code .tag {
  color: #0000ff;
}

.xml-code .attr {
  color: #ff0000;
}

.xml-code .string {
  color: #008000;
}

.related-data-section {
  margin-top: 20px;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
  width: 100%;
  clear: both;
  transition: all 0.3s ease;
}

.related-data-section.table-collapsed {
  width: calc(100% - 450px);
  margin-right: 450px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
}

.related-table-container {
  margin-bottom: 40px;
}

/* 响应式样式调整 */
@media screen and (max-width: 1400px) {
  .drawer-container {
    width: 380px;
  }
  
  .table-container.table-collapsed {
    width: calc(100% - 400px);
    margin-right: 400px;
  }
}

/* 添加深度翻页提示样式 */
.deep-paging-tip {
  margin-top: 15px;
  margin-bottom: 15px;
}

.deep-paging-tip :deep(.el-alert__content) {
  display: block;
}

.deep-paging-tip p {
  margin: 5px 0;
  font-size: 13px;
  color: #666;
}

/* 禁用分页组件跳页输入框和部分按钮 */
.pagination-container :deep(.is-disabled) {
  cursor: not-allowed !important;
  background-color: #f5f7fa !important;
  color: #c0c4cc !important;
}
</style>

