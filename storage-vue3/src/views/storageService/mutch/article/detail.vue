<template>
  <div class="app-container">
    <!-- 基本信息模块 -->
    <div class="module-container" v-show="showBasicInfo">
      <div class="module-header">
        <span class="module-title">基本信息</span>
        <el-button link class="collapse-btn" @click="showBasicInfo = false">收起</el-button>
      </div>
      <div class="module-content">
        <div class="info-grid">
          <div class="info-row">
            <span class="info-label">Article Id:</span>
            <span class="info-value">{{ articleInfo.fusionArticleId || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Source Id:</span>
            <span class="info-value">{{ articleInfo.fusionJournalId || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Source Title:</span>
            <span class="info-value">{{ articleInfo.sourceTitle || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">ISSN:</span>
            <span class="info-value">{{ articleInfo.issn || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Publisher Name:</span>
            <span class="info-value">{{ articleInfo.publisherName || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Pub Year:</span>
            <span class="info-value">{{ articleInfo.year || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Volume:</span>
            <span class="info-value">{{ articleInfo.volume || '-' }}</span>
          </div>
          <div class="info-row abstract-row">
            <span class="info-label">Issue:</span>
            <span class="info-value">{{ articleInfo.issue || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">DOI:</span>
            <span class="info-value">{{ articleInfo.doi || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">ArticleTitle:</span>
            <span class="info-value">{{ articleInfo.articleTitle || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">FirstPage:</span>
            <span class="info-value">{{ articleInfo.firstPage || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">LastPage:</span>
            <span class="info-value">{{ articleInfo.lastPage || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Author-FullName:</span>
            <span class="info-value">{{ articleInfo.fullName || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Abstract:</span>
            <span class="info-value">{{ articleInfo.abstract || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Keyword:</span>
            <span class="info-value">{{ articleInfo.keyword || '-' }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 收起状态显示恢复按钮 -->
    <div class="module-collapsed" v-if="!showBasicInfo">
      <div class="module-header">
        <span class="module-title">基本信息</span>
        <el-button link class="collapse-btn" @click="showBasicInfo = true">展开</el-button>
      </div>
    </div>

    <!-- 品种详情模块 -->
    <div class="module-container" v-show="showArticleDetail">
      <div class="module-header">
        <span class="module-title">篇级详情</span>
        <el-button link class="collapse-btn" @click="showArticleDetail = false">收起</el-button>
      </div>
      <div class="module-content">
        <!-- 使用自定义Tab切换 -->
        <div class="custom-tabs">
          <div class="tab-header">
            <span 
              :class="{ 'active-tab': activeTab === 'xml' }" 
              @click="activeTab = 'xml'"
            >元数据详情</span>
            <span 
              :class="{ 'active-tab': activeTab === 'articles' }" 
              @click="activeTab = 'articles'"
            >挂接篇级数据</span>
          </div>
          <div class="tab-content">
            <!-- 元数据详情 -->
            <div v-show="activeTab === 'xml'" class="xml-content">
              <pre class="xml-code">
&lt;?record xmlns:xlink="http://www.w3.org/1999/xlink"&gt;
  &lt;source-meta&gt;
    &lt;publisher&gt;
      &lt;institution-wrap&gt;
        &lt;institution&gt;{{ articleInfo.publisherName || '内容' }}&lt;/institution&gt;
      &lt;/institution-wrap&gt;
    &lt;/publisher&gt;
    &lt;source-title-group&gt;
      &lt;source-title xml:lang="eng"&gt;{{ articleInfo.sourceTitle || articleInfo.journalTitle || '内容' }}&lt;/source-title&gt;
    &lt;/source-title-group&gt;
    &lt;issn publication-format="print"&gt;{{ articleInfo.issn || '内容' }}&lt;/issn&gt;
    &lt;volume-issue-group&gt;
      &lt;volume&gt;{{ articleInfo.volume || '内容' }}&lt;/volume&gt;
      &lt;issue&gt;{{ articleInfo.issue || '内容' }}&lt;/issue&gt;
    &lt;/volume-issue-group&gt;
    &lt;pub-date&gt;
      &lt;year&gt;{{ articleInfo.year || articleInfo.pubYear || '内容' }}&lt;/year&gt;
      &lt;month&gt;内容&lt;/month&gt;
      &lt;day&gt;内容&lt;/day&gt;
    &lt;/pub-date&gt;
  &lt;/source-meta&gt;
  &lt;article-meta&gt;
    &lt;title-group&gt;
      &lt;article-title xml:lang="eng"&gt;{{ articleInfo.articleTitle || '内容' }}&lt;/article-title&gt;
    &lt;/title-group&gt;
    &lt;fpage&gt;{{ articleInfo.firstPage || '内容' }}&lt;/fpage&gt;
    &lt;lpage&gt;{{ articleInfo.lastPage || '内容' }}&lt;/lpage&gt;
    &lt;article-type&gt;内容&lt;/article-type&gt;
    &lt;article-id pub-id-type="内容"&gt;{{ articleInfo.fusionArticleId || articleInfo.articleId || '内容' }}&lt;/article-id&gt;
    &lt;history&gt;
      &lt;date publication-format="内容"&gt;
        &lt;year&gt;{{ articleInfo.year || articleInfo.pubYear || '内容' }}&lt;/year&gt;
        &lt;month&gt;内容&lt;/month&gt;
        &lt;day&gt;内容&lt;/day&gt;
      &lt;/date&gt;
    &lt;/history&gt;
  &lt;/article-meta&gt;
&lt;/record&gt;
              </pre>
            </div>

            <!-- 挂接篇级数据 -->
            <div v-show="activeTab === 'articles'" class="volume-table-container">
              <el-table
                :data="relatedArticles"
                border
                style="width: 100%"
                :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
                v-loading="relatedLoading"
              >
                <el-table-column label="篇级ID" prop="singleArticleId" align="center" show-overflow-tooltip />
                <el-table-column label="批次号" prop="batchId" align="center" show-overflow-tooltip />
                <el-table-column label="篇级题名" prop="articleTitle" align="center" show-overflow-tooltip />
                <el-table-column label="期刊题名" prop="journalTitle" align="center" show-overflow-tooltip />
                <el-table-column label="ISSN" prop="issn" align="center" show-overflow-tooltip />
                <el-table-column label="年/卷/期" prop="interval" align="center" show-overflow-tooltip />
                <el-table-column label="页码范围" align="center" width="100" show-overflow-tooltip>
                  <template #default="scope">
                    <span>{{ formatPageRange(scope.row) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="DOI" prop="doi" align="center" show-overflow-tooltip />
                <el-table-column label="更新时间" prop="updateTime" align="center" show-overflow-tooltip />
              </el-table>

              <!-- 分页组件 -->
              <pagination
                v-show="relatedParams.relatedTotal > 0"
                :total="relatedParams.relatedTotal"
                v-model:page="relatedParams.relatedQueryParams.pageNum"
                v-model:limit="relatedParams.relatedQueryParams.pageSize"
                @pagination="getRelatedArticles"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 收起状态显示恢复按钮 -->
    <div class="module-collapsed" v-if="!showArticleDetail">
      <div class="module-header">
        <span class="module-title">篇级详情</span>
        <el-button link class="collapse-btn" @click="showArticleDetail = true">展开</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { getMutchArticleInfo, listRelatedMutchArticles } from "@/api/storageService/storageService";

const router = useRouter();
const route = useRoute();

// 模块显示状态控制
const showBasicInfo = ref(true);
const showArticleDetail = ref(true);

// 获取路由参数中的期刊ID
const articleId = ref(route.params.articleId || route.query.articleId);

// 当前激活的标签页 - 设置初始值并避免在渲染过程中变化
const activeTab = ref('xml');

// 品种基本信息
const articleInfo = ref({});

// 分页和查询参数
const queryParams = ref({
  articleId: ''
});

// 挂接篇级数据查询参数
const relatedParams = reactive({
  relatedQueryParams: {
    pageNum: 1,
    pageSize: 10,
    articleId: '',
  },
  relatedTotal: 0
});

// 挂接篇级数据列表
const relatedArticles = ref([]);
const relatedLoading = ref(false);

// 获取篇级详情
async function getArticleDetail() {
  try {
    const res = await getMutchArticleInfo(articleId.value);
    if (res && res.data) {
      articleInfo.value = res.data;
    }
  } catch (error) {
    console.error('获取篇级详情失败:', error);
  }
}

// 获取挂接篇级数据
async function getRelatedArticles() {
  if (!articleId.value) return;
  
  relatedLoading.value = true;
  try {
    // 确保传递正确的参数格式 - 修改为两个参数形式
    const params = {
      pageNum: relatedParams.relatedQueryParams.pageNum,
      pageSize: relatedParams.relatedQueryParams.pageSize
    };
    
    const res = await listRelatedMutchArticles(articleId.value, params);
    
    if (res && res.rows) {
      relatedArticles.value = res.rows;
      relatedParams.relatedTotal = res.total || 0;
    } else {
      relatedArticles.value = [];
      relatedParams.relatedTotal = 0;
    }
  } catch (error) {
    console.error('获取挂接篇级数据失败:', error);
    relatedArticles.value = [];
    relatedParams.relatedTotal = 0;
  } finally {
    relatedLoading.value = false;
  }
}

// 页面加载时初始化数据
onMounted(() => {
  if (articleId.value) {
    // 先设置查询参数
    queryParams.value.articleId = articleId.value;
    relatedParams.relatedQueryParams.articleId = articleId.value;
    
    // 获取基本详情，延迟加载关联数据
    getArticleDetail();
    
    // 使用setTimeout延迟获取关联数据，避免在渲染周期中修改依赖
    setTimeout(() => {
      getRelatedArticles();
    }, 100);
  }
});

// 返回上一页
function goBack() {
  router.go(-1);
}

// 格式化页码范围
function formatPageRange(row) {
  if (!row.firstPage && !row.lastPage) return '-';
  
  const firstPage = row.firstPage || '-';
  const lastPage = row.lastPage || '-';
  
  if (firstPage === lastPage) {
    return firstPage;
  }
  
  return `${firstPage}-${lastPage}`;
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.top-actions {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}

.module-container {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.module-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.collapse-btn {
  color: #409EFF;
  font-size: 14px;
}

.module-content {
  padding: 20px;
}

.module-collapsed {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.info-row {
  display: flex;
  line-height: 20px;
  font-size: 14px;
}

.info-label {
  color: #606266;
  font-weight: 500;
  min-width: 150px;
  text-align: right;
  padding-right: 15px;
}

.info-value {
  color: #303133;
  flex: 1;
}

.abstract-row {
  align-items: flex-start;
}

.abstract-row .info-value {
  line-height: 1.6;
}

/* 自定义Tab样式 */
.custom-tabs {
  width: 100%;
}

.tab-header {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 20px;
}

.tab-header span {
  padding: 8px 16px;
  cursor: pointer;
  color: #606266;
  font-size: 14px;
  margin-right: 20px;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.tab-header span:hover {
  color: #409EFF;
}

.tab-header span.active-tab {
  color: #409EFF;
  border-bottom: 2px solid #409EFF;
}

.tab-content {
  padding-top: 10px;
}

.volume-table-container {
  margin-top: 10px;
}

.xml-content {
  background-color: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 15px;
  height: 100%;
  overflow: auto;
}

.xml-code {
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  margin: 0;
  white-space: pre-wrap;
}

.xml-code .tag {
  color: #0000ff;
}

.xml-code .attr {
  color: #ff0000;
}

.xml-code .string {
  color: #008000;
}
</style>