<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" class="search-form">
        <el-form-item label="品种ID" prop="journalId">
          <el-input
              v-model="queryParams.journalId"
              placeholder="请输入品种ID"
              clearable
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="期刊题名" prop="journalTitle">
          <el-input
              v-model="queryParams.journalTitle"
              placeholder="请输入期刊题名"
              clearable
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="ISSN" prop="issn">
          <el-input
              v-model="queryParams.issn"
              placeholder="请输入ISSN"
              clearable
              @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="数据状态" prop="state">
          <el-select v-model="queryParams.state" placeholder="数据状态" clearable style="width: 200px" @keyup.enter.native="handleQuery">
            <el-option value="0" label="停用">停用</el-option>
            <el-option value="1" label="启用">启用</el-option>
          </el-select>
        </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <div class="flex-container">
        <div class="table-container" :class="{ 'table-collapsed': drawerOpen }">
        <el-row :gutter="10" class="mb8">
          <right-toolbar 
            v-model:showSearch="showSearch" 
            @queryTable="getList"
            :columns="columnList"
            :show-columns-type="'checkbox'"
            @checkbox-change="handleColumnChange"
          />
        </el-row>

      <el-table v-loading="loading" :data="dataSourceList" @row-click="handleRowClick">
        <template v-for="col in columnList" :key="col.key">
          <el-table-column
            v-if="col.visible"
            :label="col.label"
            :prop="col.key"
            align="center"
          />
        </template>
        <el-table-column label="操作" width="180" align="center" class-name="small-padding fixed-width" fixed="right">
          <template #default="scope">
            <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click.stop="handleDetail(scope.row)"
            >详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="handlePagination"
         :disabled="isDeepPaging"
         :hide-on-single-page="false"
      />
      
      <!-- 深度翻页模式提示 -->
      <div v-if="isDeepPaging" class="deep-paging-tip">
        <el-alert
          title="当前处于深度翻页模式"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>1. 只能向后翻页,不能跳页或向前翻页</p>
            <p>2. 如需重新查询,请使用搜索或重置按钮</p>
            <p>3. 当前页码: {{ currentPage }}</p>
          </template>
        </el-alert>
      </div>
        </div>

        <!-- 右侧抽屉式表单 -->
        <div class="drawer-container" v-show="drawer.visible" :class="{ 'open': drawer.visible }">
          <div class="drawer-header">
            <div class="drawer-title">期刊详情</div>
            <el-button class="drawer-close" @click="cancel" icon="Close" circle></el-button>
          </div>
          <div class="drawer-body">
            <!-- Tab 切换 -->
            <el-tabs v-model="activeTab">
              <el-tab-pane label="品种详情" name="journal">
                <div class="journal-info">
                  <div class="info-item">
                    <span class="label">Source ID:</span>
                    <span class="value">{{ journalForm.sourceId }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Source Title:</span>
                    <span class="value">{{ journalForm.sourceTitle }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">ISSN:</span>
                    <span class="value">{{ journalForm.issn }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Publisher Name:</span>
                    <span class="value">{{ journalForm.publisherName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">Pub Year:</span>
                    <span class="value">{{ journalForm.pubYear }}</span>
                  </div>
                </div>
              </el-tab-pane>
              
              <el-tab-pane label="卷期详情" name="volume">
                <div class="volume-info">
                  <el-table :data="volumeList" style="width: 100%" border>
                    <el-table-column label="卷号" prop="volume" align="center" />
                    <el-table-column label="期号" prop="issue" align="center" />
                    <el-table-column label="年份" prop="year" align="center" />
                  </el-table>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
   </div>
</template>

<script setup name="storageService">
import { listSingleJournals, getSingleJournalInfo, getSingleJournalVolumes } from "@/api/storageService/storageService";
import { useRouter } from "vue-router";

const router = useRouter();
const { proxy } = getCurrentInstance();
const { storage_last_state } = proxy.useDict("storage_last_state");

// 列配置
const columnList = ref([
  { key: 'journalId', label: '品种ID', visible: true },
  { key: 'journalTitle', label: '期刊题名', visible: true },
  { key: 'issn', label: 'ISSN', visible: true },
  { key: 'interval', label: '年/卷/期区间', visible: true },
  { key: 'articleNum', label: '篇级数量', visible: true },
  { key: 'language', label: '语种', visible: true },
  { key: 'status', label: '数据状态', visible: true },
  { key: 'updateTime', label: '更新时间', visible: true }
]);

// 其他状态
const dataSourceList = ref([]);
const drawerOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const scrollId = ref('');
const isDeepPaging = ref(false);
const currentPage = ref(1);
const lastAllowedPage = ref(1);

const drawer = ref({
  visible: false
});

const journalForm = ref({
  sourceId: '',
  sourceTitle: '',
  issn: '',
  publisherName: '',
  pubYear: ''
});

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    journalId: null,
    journalTitle: null,
    state: null,
    issn: null
  }
});

const { queryParams } = toRefs(data);

// Tab 激活状态
const activeTab = ref('journal');

// 卷期列表数据 - 移除模拟数据，改为响应式空数组
const volumeList = ref([]);

const MAX_NORMAL_COUNT = 10000; // ES默认最大可查询数据条数

// 处理列显示变化
const handleColumnChange = (checked, label) => {
  const newColumnList = columnList.value.map(col => {
    if (col.label === label) {
      return { ...col, visible: checked };
    }
    return col;
  });
  columnList.value = newColumnList; // 通过整体替换触发响应式更新
};

/** 查询期刊列表 */
function getList() {
  loading.value = true;
  const params = {
    ...queryParams.value,
    scrollId: scrollId.value
  };

  // 根据是否深度翻页选择不同的接口
  const api = isDeepPaging.value ? '/storageService/singleJournal/scroll' : '/storageService/singleJournal/list';

  listSingleJournals(api, params).then(response => {
    if (response.rows && response.rows.length > 0) {
      // 如果是深度翻页,需要保存新的scrollId
      if (isDeepPaging.value && response.rows[0].scrollId) {
        scrollId.value = response.rows[0].scrollId;
      }
    dataSourceList.value = response.rows;
      total.value = 100000;
    } else {
      // 如果没有数据,重置scrollId
      scrollId.value = '';
      isDeepPaging.value = false;
    }
    loading.value = false;
  });
}

/**
 * 点击详情按钮，跳转到详情页面
 * @param {Object} row 行数据
 */
function handleDetail(row) {
  router.push({
    path: '/storageService/single/journal/detail',
    query: {
      journalId: row.journalId
    }
  });
}

function handleRowClick(row) {
  // 获取期刊详情
  getSingleJournalInfo(row.journalId).then(response => {
    journalForm.value = response.data;
  });
  
  // 获取卷期列表
  getSingleJournalVolumes(row.journalId).then(response => {
    volumeList.value = response.data;
  });
  
  // 显示抽屉
  drawer.value.visible = true;
  drawerOpen.value = true;
  
  // 默认显示第一个 tab
  activeTab.value = 'journal';
}

/** 表单重置 */
function reset() {
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  scrollId.value = '';
  isDeepPaging.value = false;
  currentPage.value = 1;
  lastAllowedPage.value = 1;
  getList();
}

/** 取消按钮 */
function cancel() {
  drawer.value.visible = false;
  drawerOpen.value = false;
  resetDrawerForm();
}

/** 重置抽屉表单 */
function resetDrawerForm() {
  journalForm.value = {
    sourceId: '',
    sourceTitle: '',
    issn: '',
    publisherName: '',
    pubYear: ''
  };
  activeTab.value = 'journal';
  volumeList.value = [];
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  scrollId.value = '';
  isDeepPaging.value = false;
  currentPage.value = 1;
  lastAllowedPage.value = 1;
  handleQuery();
}

/** 分页组件事件处理 */
function handlePagination({ page, limit }) {
  // 计算即将查询的数据位置（包含当前页的最后一条数据）
  const targetDataCount = page * limit;
  // 计算最后允许的正常页码 - 使用向上取整，确保可以查看到第10000条数据
  const maxAllowedPage = Math.ceil(MAX_NORMAL_COUNT / limit);

  // 如果已经是深度翻页模式
  if (isDeepPaging.value) {
    // 只允许向后翻页
    if (page < currentPage.value) {
      proxy.$modal.msgWarning('深度翻页模式下不支持向前翻页,请重新执行查询');
      queryParams.value.pageNum = currentPage.value;
      return;
    }
    // 只允许翻到下一页
    if (page > currentPage.value + 1) {
      proxy.$modal.msgWarning('深度翻页模式下只能一页一页向后翻');
      queryParams.value.pageNum = currentPage.value;
      return;
    }
  }
  // 首次跳页时的处理
  else if (!isDeepPaging.value) {
    // 只有当即将查询的数据超过最大限制时才进入深度翻页模式
    if (targetDataCount > MAX_NORMAL_COUNT) {
      proxy.$modal.confirm(
        `查询数据超过${MAX_NORMAL_COUNT}条，系统将自动跳转到第${maxAllowedPage}页并进入深度翻页模式，是否继续？`,
        '提示',
        {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          // 跳转到最大允许页
          page = maxAllowedPage;
          isDeepPaging.value = true;
          scrollId.value = '';
          currentPage.value = page;
          lastAllowedPage.value = page;
          queryParams.value.pageNum = page;
          queryParams.value.pageSize = limit;
          getList();
        })
        .catch(() => {
          queryParams.value.pageNum = lastAllowedPage.value;
        });
      return;
    }
  }

  currentPage.value = page;
  lastAllowedPage.value = page;
  queryParams.value.pageNum = page;
  queryParams.value.pageSize = limit;
  getList();
}

/** 初始加载 */
getList();
</script>
<style scoped>
.flex-container {
  display: flex;
  position: relative;
  width: 100%;
  overflow: visible; /* 防止内容被裁剪 */
}

.table-container {
  flex-grow: 1;
  transition: all 0.3s ease;
  width: 100%;
  margin-right: 0; /* 默认状态无右边距 */
}

.table-container.table-collapsed {
  width: calc(100% - 430px); /* 增加与抽屉之间的距离 */
  margin-right: 430px; /* 添加右边距，确保表格内容不被遮挡 */
}

.drawer-container {
  position: absolute;
  right: 0;
  top: 0;
  width: 430px;
  background-color: #fff;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  border-left: 1px solid #e6e6e6;
  z-index: 100;
  height: calc(100vh - 185px);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.drawer-container.open {
  transform: none;
}

.drawer-header {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.drawer-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.drawer-close {
  padding: 8px;
  font-size: 18px;
  color: #909399;
  transition: color 0.2s;
}

.drawer-close:hover {
  color: #409EFF;
}

.drawer-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.drawer-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

:deep(.el-tabs) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
  flex-shrink: 0;
}

:deep(.el-tabs__content) {
  flex: 1;
  overflow-y: auto;
}

.journal-info {
  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 14px;
    
    .label {
      color: #606266;
      font-weight: 500;
      min-width: 120px;
      margin-right: 10px;
    }
    
    .value {
      color: #303133;
      flex: 1;
      word-break: break-all;
    }
  }
}

.volume-info {
  margin-top: 10px;
}

:deep(.el-drawer__body) {
  padding: 0;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__nav) {
  width: 100%;
}

:deep(.el-tabs__item) {
  flex: 1;
  text-align: center;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table__header-wrapper th) {
  font-weight: 500;
  background-color: #F5F7FA;
}

.tool-selection {
  width: 100%;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 10px;
  background-color: #F8F8F9;
}

.tool-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 5px 0;
}

.tool-row:hover {
  background-color: #F2F6FC;
}

.tool-row.disabled {
  color: #909399;
  background-color: #F5F7FA;
}

.tool-label {
  width: 140px;
  margin-right: 10px;
  font-size: 14px;
}

.tool-value {
  color: #909399;
  font-size: 14px;
}

.custom-switch {
  --el-switch-on-color: #13ce66;
}

.custom-switch :deep(.el-switch__core) {
  background-color: #ff4949;
}

.custom-switch :deep(.el-switch__core)::after {
  content: attr(data-text);
  position: absolute;
  font-size: 12px;
  color: #fff;
  left: 25px;
  top: 0;
  line-height: 20px;
}

.custom-switch.is-checked :deep(.el-switch__core)::after {
  content: "启用";
  left: 5px;
}

.custom-switch :deep(.el-switch__core)::before {
  content: "停用";
  position: absolute;
  font-size: 12px;
  color: #fff;
  right: 5px;
  top: 0;
  line-height: 20px;
}

.form-group-title {
  font-size:14px;
  font-weight: bold;
  margin: 15px 0 10px;
  padding-left: 5px;
  border-left: 3px solid #409EFF;
}

/* 新增样式 */
.no-margin {
  margin-bottom: 5px !important;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  margin-top: 5px;
  padding-left: 2px;
}

.el-form-item.is-required .el-form-item__label::before {
  content: '*';
  color: #F56C6C;
  margin-right: 4px;
}

/* 优化表单元素间距 */
.mt-2 {
  margin-top: 12px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

/* 响应式样式调整 */
@media screen and (max-width: 1400px) {
  .drawer-container {
    width: 380px;
  }
  
  .table-container.table-collapsed {
    width: calc(100% - 400px);
    margin-right: 400px;
  }
}

/* 批次表格相关样式 */
.batch-table-container {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
}

.batch-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.batch-title {
  font-size: 15px;
  font-weight: bold;
  color: #303133;
  border-left: 3px solid #409EFF;
  padding-left: 10px;
}

.batch-table {
  margin-bottom: 15px;
}

/* 添加深度翻页提示样式 */
.deep-paging-tip {
  margin-top: 15px;
  margin-bottom: 15px;
}

.deep-paging-tip :deep(.el-alert__content) {
  display: block;
}

.deep-paging-tip p {
  margin: 5px 0;
  font-size: 13px;
  color: #666;
}

/* 禁用分页组件跳页输入框和部分按钮 */
.pagination-container :deep(.is-disabled) {
  cursor: not-allowed !important;
  background-color: #f5f7fa !important;
  color: #c0c4cc !important;
}
</style>

