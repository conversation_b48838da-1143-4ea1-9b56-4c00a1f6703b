<template>
  <div class="app-container">
    <!-- 基本信息模块 -->
    <div class="module-container" v-show="showBasicInfo">
      <div class="module-header">
        <span class="module-title">基本信息</span>
        <el-button link class="collapse-btn" @click="showBasicInfo = false">收起</el-button>
      </div>
      <div class="module-content">
        <div class="info-grid">
          <div class="info-row">
            <span class="info-label">Source Id:</span>
            <span class="info-value">{{ journalInfo.sourceId }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Source Title:</span>
            <span class="info-value">{{ journalInfo.sourceTitle }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">ISSN:</span>
            <span class="info-value">{{ journalInfo.issn }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Publisher Name:</span>
            <span class="info-value">{{ journalInfo.publisherName }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Pub Year:</span>
            <span class="info-value">{{ journalInfo.pubYear }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Publication cycle:</span>
            <span class="info-value">{{ journalInfo.publicationCycle }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">language:</span>
            <span class="info-value">{{ journalInfo.language }}</span>
          </div>
          <div class="info-row abstract-row">
            <span class="info-label">Abstract:</span>
            <span class="info-value">{{ journalInfo.abstract }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Regions:</span>
            <span class="info-value">{{ journalInfo.regions }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 收起状态显示恢复按钮 -->
    <div class="module-collapsed" v-if="!showBasicInfo">
      <div class="module-header">
        <span class="module-title">基本信息</span>
        <el-button link class="collapse-btn" @click="showBasicInfo = true">展开</el-button>
      </div>
    </div>

    <!-- 品种详情模块 -->
    <div class="module-container" v-show="showJournalDetail">
      <div class="module-header">
        <span class="module-title">品种详情</span>
        <el-button link class="collapse-btn" @click="showJournalDetail = false">收起</el-button>
      </div>
      <div class="module-content">
        <!-- Tab切换 -->
        <el-tabs v-model="activeTab" class="detail-tabs">
          <el-tab-pane label="卷期详情" name="volume">
            <div class="volume-table-container">
              <el-table
                :data="volumeList"
                border
                style="width: 100%"
                :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
              >
                <el-table-column label="卷期ID" prop="volumeId" align="center" />
                <el-table-column label="年份" prop="year" align="center" />
                <el-table-column label="卷号" prop="volume" align="center" />
                <el-table-column label="期号" prop="issue" align="center" />
                <el-table-column label="篇级数量" prop="articleCount" align="center" />
                <el-table-column label="更新时间" prop="updateTime" align="center" />
              </el-table>

              <!-- 分页组件 -->
              <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getVolumeList"
              />
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="篇级详情" name="article">
            <!-- 篇级详情表格 -->
            <div class="volume-table-container">
              <el-table
                :data="articleDetailList"
                border
                style="width: 100%"
                :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
              >
                <el-table-column label="篇级ID" prop="articleId" align="center" width="160" show-overflow-tooltip />
                <el-table-column label="篇级题名" prop="articleTitle" align="center" show-overflow-tooltip />
                <el-table-column label="年/卷/期" prop="interval" align="center" width="100" />
                <el-table-column label="数据状态" prop="status" align="center" width="100" />
                <el-table-column label="更新时间" prop="updateTime" align="center" width="160" />
              </el-table>

              <!-- 分页组件 -->
              <pagination
                v-show="articleTotal > 0"
                :total="articleTotal"
                v-model:page="articleQueryParams.pageNum"
                v-model:limit="articleQueryParams.pageSize"
                @pagination="getArticleList"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    
    <!-- 收起状态显示恢复按钮 -->
    <div class="module-collapsed" v-if="!showJournalDetail">
      <div class="module-header">
        <span class="module-title">品种详情</span>
        <el-button link class="collapse-btn" @click="showJournalDetail = true">展开</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { getSingleJournalDetail, getSingleJournalVolumes, getSingleJournalArticles } from "@/api/storageService/storageService";

const router = useRouter();
const route = useRoute();

// 模块显示状态控制
const showBasicInfo = ref(true);
const showJournalDetail = ref(true);

// 获取路由参数中的期刊ID
const journalId = ref(route.params.journalId || route.query.journalId);

// 当前激活的标签页
const activeTab = ref('volume');

// 品种基本信息
const journalInfo = ref({});

// 分页和查询参数
const data = reactive({
  // 查询参数
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    journalId: '',
  },
  total: 0
});

const { queryParams, total } = toRefs(data);

// 卷期列表数据
const volumeList = ref([]);

// 篇级分页和查询参数
const articleData = reactive({
  // 查询参数
  articleQueryParams: {
    pageNum: 1,
    pageSize: 10,
    journalId: '',
  },
  articleTotal: 0
});

const { articleQueryParams, articleTotal } = toRefs(articleData);

// 篇级详情数据
const articleDetailList = ref([]);

// 获取期刊详情
async function getJournalInfo() {
  try {
    const res = await getSingleJournalDetail(journalId.value);
    journalInfo.value = res.data;
  } catch (error) {
    console.error('获取期刊详情失败:', error);
  }
}

// 获取卷期列表
async function getVolumeList() {
  try {
    const res = await getSingleJournalVolumes(journalId.value);
    volumeList.value = res.data;
    total.value = res.data.length; // 或者使用后端返回的total
  } catch (error) {
    console.error('获取卷期列表失败:', error);
  }
}

// 获取篇级列表
async function getArticleList() {
  try {
    const res = await getSingleJournalArticles(journalId.value);
    articleDetailList.value = res.data;
    articleTotal.value = res.data.length; // 或者使用后端返回的total
  } catch (error) {
    console.error('获取篇级列表失败:', error);
  }
}

// 返回上一页
function goBack() {
  router.go(-1);
}

// 页面加载时初始化数据
onMounted(() => {
  queryParams.value.journalId = journalId.value;
  articleQueryParams.value.journalId = journalId.value;
  getJournalInfo();
  getVolumeList();
  getArticleList();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}


.module-container {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.module-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.collapse-btn {
  color: #409EFF;
  font-size: 14px;
}

.module-content {
  padding: 20px;
}

.module-collapsed {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.info-row {
  display: flex;
  line-height: 20px;
  font-size: 14px;
}

.info-label {
  color: #606266;
  font-weight: 500;
  min-width: 150px;
  text-align: right;
  padding-right: 15px;
}

.info-value {
  color: #303133;
  flex: 1;
}

.abstract-row {
  align-items: flex-start;
}

.abstract-row .info-value {
  line-height: 1.6;
}

.detail-tabs {
  width: 100%;
}

.volume-table-container {
  margin-top: 10px;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__item) {
  height: 40px;
  line-height: 40px;
  padding: 0 20px;
}

.placeholder {
  padding: 50px 0;
  text-align: center;
  color: #909399;
  font-size: 14px;
  background-color: #f9f9f9;
  border-radius: 4px;
}


.xml-code .tag {
  color: #0000ff;
}

.xml-code .attr {
  color: #ff0000;
}

.xml-code .string {
  color: #008000;
}
</style>