<template>
  <div class="app-container">
    <!-- 基本信息模块 -->
    <div class="module-container" v-show="showBasicInfo">
      <div class="module-header">
        <span class="module-title">基本信息</span>
        <el-button link class="collapse-btn" @click="showBasicInfo = false">收起</el-button>
      </div>
      <div class="module-content">
        <div class="info-grid">
          <div class="info-row">
            <span class="info-label">Source Id:</span>
            <span class="info-value">{{ issueInfo.sourceId }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Source Title:</span>
            <span class="info-value">{{ issueInfo.sourceTitle }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">ISSN:</span>
            <span class="info-value">{{ issueInfo.issn }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Publisher Name:</span>
            <span class="info-value">{{ issueInfo.publisherName }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Pub Year:</span>
            <span class="info-value">{{ issueInfo.pubYear }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">language:</span>
            <span class="info-value">{{ issueInfo.language }}</span>
          </div>
          <div class="info-row abstract-row">
            <span class="info-label">Year:</span>
            <span class="info-value">{{ issueInfo.year }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Volume:</span>
            <span class="info-value">{{ issueInfo.volume }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Issue:</span>
            <span class="info-value">{{ issueInfo.issue }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 收起状态显示恢复按钮 -->
    <div class="module-collapsed" v-if="!showBasicInfo">
      <div class="module-header">
        <span class="module-title">基本信息</span>
        <el-button link class="collapse-btn" @click="showBasicInfo = true">展开</el-button>
      </div>
    </div>

    <!-- 品种详情模块 -->
    <div class="module-container" v-show="showJournalDetail">
      <div class="module-header">
        <span class="module-title">关联篇级</span>
        <el-button link class="collapse-btn" @click="showJournalDetail = false">收起</el-button>
      </div>
      <div class="module-content">
            <div class="volume-table-container">
              <el-table
                :data="articleList"
                border
                style="width: 100%"
                :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
              >
                <el-table-column label="篇级ID" prop="articleId" align="center" />
                <el-table-column label="篇级题名" prop="articleTitle" align="center" />
                <el-table-column label="篇级状态" prop="status" align="center" />
                <el-table-column label="更新时间" prop="updateTime" align="center" />
              </el-table>

              <!-- 分页组件 -->
              <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getArticleList"
              />
            </div>
      </div>
    </div>
    
    <!-- 收起状态显示恢复按钮 -->
    <div class="module-collapsed" v-if="!showJournalDetail">
      <div class="module-header">
        <span class="module-title">品种详情</span>
        <el-button link class="collapse-btn" @click="showJournalDetail = true">展开</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { getSingleIssueInfo, listSingleArticles } from "@/api/storageService/storageService";

const route = useRoute();

// 模块显示状态控制
const showBasicInfo = ref(true);
const showJournalDetail = ref(true);

// 获取路由参数中的期刊ID
const issueId = ref(route.params.issueId || route.query.issueId);

// 品种基本信息
const issueInfo = ref({});

// 分页和查询参数
const data = reactive({
  // 查询参数
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    issueId: '',
  },
  total: 0
});

const { queryParams, total } = toRefs(data);

// 篇级列表数据
const articleList = ref([]);

// 获取期刊详情
async function getIssueDetail() {
  try {
    const res = await getSingleIssueInfo(issueId.value);
    issueInfo.value = res.data;
  } catch (error) {
    console.error('获取卷期详情失败:', error);
  }
}

// 获取篇级列表
async function getArticleList() {
  try {
    const res = await listSingleArticles(issueId.value);
    articleList.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error('获取篇级列表失败:', error);
  }
}

// 页面加载时初始化数据
onMounted(() => {
  queryParams.value.issueId = issueId.value;
  getIssueDetail();
  getArticleList();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}


.module-container {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.module-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.collapse-btn {
  color: #409EFF;
  font-size: 14px;
}

.module-content {
  padding: 20px;
}

.module-collapsed {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.info-row {
  display: flex;
  line-height: 20px;
  font-size: 14px;
}

.info-label {
  color: #606266;
  font-weight: 500;
  min-width: 150px;
  text-align: right;
  padding-right: 15px;
}

.info-value {
  color: #303133;
  flex: 1;
}

.abstract-row {
  align-items: flex-start;
}

.abstract-row .info-value {
  line-height: 1.6;
}

.detail-tabs {
  width: 100%;
}

.volume-table-container {
  margin-top: 10px;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__item) {
  height: 40px;
  line-height: 40px;
  padding: 0 20px;
}

.placeholder {
  padding: 50px 0;
  text-align: center;
  color: #909399;
  font-size: 14px;
  background-color: #f9f9f9;
  border-radius: 4px;
}
</style>