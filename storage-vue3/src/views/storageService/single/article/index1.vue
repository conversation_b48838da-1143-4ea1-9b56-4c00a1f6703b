<template>
   <div class="app-container">
      <!-- 背景框框 -->
      <viewBody title="篇级管理">
         <template #content>
            <!-- 搜索栏 -->
            <FormSearch 
               ref="formSearchRef" 
               v-model="queryParams"
               :formItems="formItems"
               @search="handleQuery" 
               @reset="resetQuery"
            />

            <!-- 表格部分 -->
            <TableForm
               ref="tableFormRef"
               :columns="columns"
               :tableData="dataSourceList"
               :total="total"
               v-loading="loading"
               :showIndex="true"
               :showOtherColumn="true"
               tableotherColumnLabel="操作"
               :isShowCount="true"
               :isShowSearchQuery="hasSearchConditions"
               :columnsRightWidth="'180'"
               @row-click="handleRowClick"
            >
             <!-- 操作按钮插槽 -->
             <template #otherOperation="{ row }">
                <span class="operation-btn" @click.stop="handleDetail(row)">
                   详情
                </span>
             </template>
               
             <!-- 分页插槽 -->
             <template #pagination>
                <MyPagination
                   v-if="total > 0"
                   :total="total"
                   :page="queryParams.pageNum"
                   :limit="queryParams.pageSize"
                   @pagination="getList"
                />
             </template>

               <!-- 右侧表单插槽 -->
               <template #drawer>
                  <div class="drawer-body">
                     <el-tabs v-model="activeTab">
                        <el-tab-pane label="列表展示" name="col">
                           <div class="journal-info">
                              <div v-for="(value, key) in articleForm" :key="key" class="info-item">
                                 <span class="label">{{ key }}:</span>
                                 <span class="value">{{ value }}</span>
                              </div>
                           </div>
                        </el-tab-pane>
                        <el-tab-pane label="XML展示" name="xml">
                           <div class="xml-content">
                              <pre class="xml-code">{{ xmlContent }}</pre>
                           </div>
                        </el-tab-pane>
                     </el-tabs>
                  </div>
               </template>
            </TableForm>

            <!-- 挂接篇级数据表格 -->
            <div class="related-data-section" v-if="showRelatedData">
               <div class="section-title">挂接篇级数据</div>
               <TableForm
                  :columns="relatedColumns"
                  :tableData="relatedArticles"
                  :total="relatedTotal"
                  v-loading="loading"
               >
                  <template #pagination>
                     <MyPagination
                        v-if="relatedTotal > 0"
                        :total="relatedTotal"
                        :page="relatedQueryParams.pageNum"
                        :limit="relatedQueryParams.pageSize"
                        @pagination="getRelatedArticles"
                     />
                  </template>
               </TableForm>
            </div>
         </template>
      </viewBody>
   </div>
</template>

<script setup name="storageService">
import { listSingleArticles, getSingleArticleInfo, listRelatedSingleArticles } from "@/api/storageService/storageService";
import { useRouter } from "vue-router";
import viewBody from '@/components/view/viewBody.vue';
import TableForm from '@/views/source/components/TableForm.vue';
import MyPagination from "@/components/Pagination/new.vue";
import FormSearch from '@/views/source/components/FormSearch.vue';
import { ref, computed } from 'vue';

const router = useRouter();
const { proxy } = getCurrentInstance();

const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const activeTab = ref('col');
const showRelatedData = ref(false);
const tableFormRef = ref(null);
const formSearchRef = ref(null);

// 搜索表单配置
const formItems = ref([
  {
    label: '篇级ID',
    prop: 'singleArticleId',
    component: 'el-input',
    props: {
      placeholder: '请输入篇级ID',
      clearable: true,
      style: {width: '200px'},
    }
  },
  {
    label: '篇级题名',
    prop: 'article_title',
    component: 'el-input',
    props: {
      placeholder: '请输入篇级题名',
      clearable: true,
      style: {width: '200px'},
    }
  },
  {
    label: 'ISSN',
    prop: 'issn',
    component: 'el-input',
    props: {
      placeholder: '请输入ISSN',
      clearable: true,
      style: {width: '200px'},
    }
  },
  {
    label: '数据状态',
    prop: 'state',
    component: 'el-select',
    props: {
      placeholder: '数据状态',
      clearable: true,
      style: {width: '200px'},
    },
    children: () => [
      { value: '1', label: '正常' },
      { value: '0', label: '回退' }
    ]
  }
]);

// 主表格列配置
const columns = ref([
  {prop: 'singleArticleId', label: '篇级ID', width: '120'},
  {prop: 'article_title', label: '篇级题名'},
  {prop: 'journal_title', label: '期刊题名'},
  {prop: 'issn', label: 'ISSN', width: '120'},
  {prop: 'interval', label: '年/卷/期', width: '120'},
  {prop: 'originalNum', label: '原始数据条数', width: '120'},
  {prop: 'state', label: '数据状态', width: '100'},
  {prop: 'update_time', label: '更新时间', width: '180'}
]);

// 关联表格列配置
const relatedColumns = ref([
  {prop: 'originalId', label: '原始篇级ID', width: '120'},
  {prop: 'batchId', label: '批次号', width: '100'},
  {prop: 'articleTitle', label: '篇级题名'},
  {prop: 'journalTitle', label: '期刊题名'},
  {prop: 'issn', label: 'ISSN', width: '100'},
  {prop: 'interval', label: '年/卷/期', width: '100'},
  {prop: 'updateTime', label: '更新时间', width: '180'}
]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 20,
  articleId: null,
  articleTitle: null,
  state: null,
  issn: null
});

// 计算是否有搜索条件
const hasSearchConditions = computed(() => {
  return queryParams.value.articleId || 
         queryParams.value.articleTitle || 
         queryParams.value.issn ||
         queryParams.value.state !== null;
});

// 清除单个搜索条件
function clearSearchItem(prop) {
  queryParams.value[prop] = '';
  handleQuery();
}

/** 搜索按钮操作 */
function handleQuery(formData) {
  if (formData) {
    Object.assign(queryParams.value, formData, { pageNum: 1 });
  } else {
    queryParams.value.pageNum = 1;
  }
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 20,
    articleId: null,
    articleTitle: null,
    state: null,
    issn: null
  };
  getList();
}

const dataSourceList = ref([]);
const drawerOpen = ref(false);
const scrollId = ref('');
const isDeepPaging = ref(false);

const drawer = ref({
  visible: false
});

const articleForm = ref({});

const xmlContent = computed(() => {
  return `<?record xmlns:xlink="http://www.w3.org/1999/xlink">
  <source-meta>
    <publisher>
      <institution-wrap>
        <institution>内容</institution>
      </institution-wrap>
    </publisher>
    <source-title-group>
      <source-title xml:lang="eng">${articleForm.value.sourceTitle || ''}</source-title>
    </source-title-group>
    <issn publication-format="print">${articleForm.value.issn || ''}</issn>
    <volume-issue-group>
      <volume>${articleForm.value.volume || ''}</volume>
      <issue>${articleForm.value.issue || ''}</issue>
    </volume-issue-group>
    <pub-date>
      <year>${articleForm.value.pubYear || ''}</year>
      <month>内容</month>
      <day>内容</day>
    </pub-date>
  </source-meta>
  <article-meta>
    <title-group>
      <article-title xml:lang="eng">${articleForm.value.articleTitle || ''}</article-title>
    </title-group>
    <fpage>${articleForm.value.firstPage || ''}</fpage>
    <lpage>${articleForm.value.lastPage || ''}</lpage>
    <article-type>内容</article-type>
    <article-id pub-id-type="内容">${articleForm.value.articleId || ''}</article-id>
    <history>
      <date publication-format="内容">
        <year>${articleForm.value.pubYear || ''}</year>
        <month>内容</month>
        <day>内容</day>
      </date>
    </history>
  </article-meta>
</record>`;
});

const relatedArticles = ref([]);

const relatedQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  articleId: ''
});

const relatedTotal = ref(0);

/** 查询篇级列表 */
function getList() {
  loading.value = true;
  const params = {
    ...queryParams.value,
    scrollId: scrollId.value
  };

  const api = isDeepPaging.value ? '/storageService/singleJournal/scroll' : '/storageService/singleJournal/list';
  
  listSingleArticles(api, params).then(response => {
    if (response.rows && response.rows.length > 0) {
      if (isDeepPaging.value && response.rows[0].scrollId) {
        scrollId.value = response.rows[0].scrollId;
      }
      
      dataSourceList.value = response.rows.map(row => ({
        ...row,
        // 处理时间格式
        interval: row.pub_year+'/'+row.volume+'/'+row.issue,
        update_time: formatDateTime(row.update_time),
        state: row.state==1?'正常':'回退'
      }));

      total.value = response.total;
    } else {
      scrollId.value = '';
      isDeepPaging.value = false;
    }
    loading.value = false;
  });
}
// 格式化日期时间
function formatDateTime(dateTime) {
  if (!dateTime) return '';

  // 处理字符串类型的日期时间
  if (typeof dateTime === 'string') {
    // 替换T为空格，移除毫秒部分和时区信息
    return dateTime.replace('T', ' ').replace(/\.\d+(\+|\-).*$/, '');
  }

  return dateTime;
}
/**
 * 点击详情按钮，跳转到详情页面
 * @param {Object} row 行数据
 */
function handleDetail(row) {
  router.push({
    path: '/storageService/single/article/detail',
    query: {
      articleId: row.singleArticleId
    }
  });
}

function handleRowClick(row) {
  getSingleArticleInfo(row.articleId).then(response => {
    articleForm.value = response.data;
    relatedQueryParams.value.articleId = row.articleId;

    getRelatedArticles();

    drawer.value.visible = true;
    drawerOpen.value = true;

    showRelatedData.value = true;

    activeTab.value = 'col';
  });
  

}

/** 取消按钮 */
function cancel() {
  drawer.value.visible = false;
  drawerOpen.value = false;
  resetDrawerForm();
  showRelatedData.value = false;
}

/** 重置抽屉表单 */
function resetDrawerForm() {
  articleForm.value = {};
  activeTab.value = 'col';
  relatedArticles.value = [];
  relatedTotal.value = 0;
}

/** 监听分页变化 */
function handlePageChange(val) {
  if (val > 100 && !isDeepPaging.value) {
    isDeepPaging.value = true;
    scrollId.value = '';
  }
  queryParams.value.pageNum = val;
  getList();
}

/** 监听每页显示数量变化 */
function handleSizeChange(val) {
  queryParams.value.pageSize = val;
  getList();
}

/** 获取挂接篇级数据 */
function getRelatedArticles() {
  listRelatedSingleArticles(relatedQueryParams.value.articleId).then(response => {
    relatedArticles.value = response.rows;
    relatedTotal.value = response.total;
  });
}

/** 初始加载 */
getList();
</script>

<style lang="scss" scoped>
// 引入 base.scss 样式文件
@import "../../../source/components/base.scss";

.operation-btn {
  display: inline-block;
  margin: 0 5px;
  color: #0076d0 !important;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
}

.journal-info {
  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 14px;
    
    .label {
      color: #606266;
      font-weight: 500;
      min-width: 120px;
      margin-right: 10px;
    }
    
    .value {
      color: #303133;
      flex: 1;
      word-break: break-all;
    }
  }
}

.xml-content {
  background-color: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 15px;
  height: 100%;
  overflow: auto;
}

.xml-code {
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  margin: 0;
  white-space: pre-wrap;
}

.related-data-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  
  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
  }
}
</style>

