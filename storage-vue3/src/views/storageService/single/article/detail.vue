<template>
  <div class="app-container">
    <!-- 基本信息模块 -->
    <div class="module-container" v-show="showBasicInfo">
      <div class="module-header">
        <span class="module-title">基本信息</span>
        <el-button link class="collapse-btn" @click="showBasicInfo = false">收起</el-button>
      </div>
      <div class="module-content">
        <div class="info-grid">
          <div class="info-row">
            <span class="info-label">Article Id:</span>
            <span class="info-value">{{ articleInfo.singleArticleId }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Source Id:</span>
            <span class="info-value">{{ articleInfo.singleJournalId }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Source Title:</span>
            <span class="info-value">{{ articleInfo.journal_title }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">ISSN:</span>
            <span class="info-value">{{ articleInfo.issn }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Publisher Name:</span>
            <span class="info-value">{{ articleInfo.publisher }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Pub Year:</span>
            <span class="info-value">{{ articleInfo.pub_year }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Volume:</span>
            <span class="info-value">{{ articleInfo.volume }}</span>
          </div>
          <div class="info-row abstract-row">
            <span class="info-label">Issue:</span>
            <span class="info-value">{{ articleInfo.issue }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">DOI:</span>
            <span class="info-value">{{ articleInfo.doi }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">ArticleTitle:</span>
            <span class="info-value">{{ articleInfo.article_title }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">FirstPage:</span>
            <span class="info-value">{{ articleInfo.fpage }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">LastPage:</span>
            <span class="info-value">{{ articleInfo.lpage }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Author-FullName:</span>
            <span class="info-value">{{ articleInfo.authors }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Abstract:</span>
            <span class="info-value">{{ articleInfo.abstract }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">Keyword:</span>
            <span class="info-value">{{ articleInfo.keywords }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 收起状态显示恢复按钮 -->
    <div class="module-collapsed" v-if="!showBasicInfo">
      <div class="module-header">
        <span class="module-title">基本信息</span>
        <el-button link class="collapse-btn" @click="showBasicInfo = true">展开</el-button>
      </div>
    </div>

    <!-- 品种详情模块 -->
    <div class="module-container" v-show="showJournalDetail">
      <div class="module-header">
        <span class="module-title">篇级详情</span>
        <el-button link class="collapse-btn" @click="showJournalDetail = false">收起</el-button>
      </div>
      <div class="module-content">
        <!-- Tab切换 -->
        <el-tabs v-model="activeTab" class="detail-tabs">
          <el-tab-pane label="元数据详情" name="xml">
            <div class="xml-content">
              <pre class="xml-code">{{ xmlContent }}
              </pre>
            </div>
          </el-tab-pane>
          <el-tab-pane label="挂接篇级数据" name="articles">
            <div class="volume-table-container">
              <el-table
                :data="relatedArticles"
                border
                style="width: 100%"
                :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
              >
                <el-table-column label="原始篇级ID" prop="originalArticleId" align="center" />
                <el-table-column label="批次号" prop="batchId" align="center" />
                <el-table-column label="篇级题名" prop="article_title" align="center" show-overflow-tooltip />
                <el-table-column label="期刊题名" prop="journal_title" align="center" show-overflow-tooltip />
                <el-table-column label="ISSN" prop="issn" align="center" />
                <el-table-column label="年/卷/期" prop="interval" align="center" />
                <el-table-column label="更新时间" prop="updateTime" align="center" />
              </el-table>

              <!-- 分页组件 -->
              <pagination
                v-show="relatedTotal > 0"
                :total="relatedTotal"
                v-model:page="relatedQueryParams.pageNum"
                v-model:limit="relatedQueryParams.pageSize"
                @pagination="getRelatedArticles"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 收起状态显示恢复按钮 -->
    <div class="module-collapsed" v-if="!showJournalDetail">
      <div class="module-header">
        <span class="module-title">篇级详情</span>
        <el-button link class="collapse-btn" @click="showJournalDetail = true">展开</el-button>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { getSingleArticleInfo, listRelatedSingleArticles, getXmlContentByUrl } from "@/api/storageService/storageService";

const route = useRoute();
const xmlContent = ref('');
// 模块显示状态控制
const showBasicInfo = ref(true);
const showJournalDetail = ref(true);

// 获取路由参数中的期刊ID
const articleId = ref(route.params.articleId || route.query.articleId);

// 当前激活的标签页
const activeTab = ref('xml');

// 品种基本信息
const articleInfo = ref({});

const data = reactive({
  // 查询参数
  queryParams: {
    articleId: ''
  }
});
const { queryParams } = toRefs(data);

// 挂接篇级数据查询参数
const relatedParams = reactive({
  // 查询参数
  relatedQueryParams: {
    pageNum: 1,
    pageSize: 10,
    articleId: '',
  },
  relatedTotal: 0
});

const { relatedQueryParams, relatedTotal } = toRefs(relatedParams);

// 挂接篇级数据列表
const relatedArticles = ref([]);

// 获取期刊详情
async function getArticleDetail() {
  try {
    const res = await getSingleArticleInfo(articleId.value);
    articleInfo.value = res.data;

    getXmlContentByUrl(res.data.xmlUri).then(res => {
      xmlContent.value = res.msg;
    }).catch(() => {
      xmlContent.value = 'XML内容获取失败';
    });
  } catch (error) {
    console.error('获取篇级详情失败:', error);
  }
}

// 获取挂接篇级数据
async function getRelatedArticles() {
  try {
    const res = await listRelatedSingleArticles(articleId.value);
    relatedArticles.value = res.rows.map( row => ({
      ...row,
      interval: row.pub_year+'/'+row.volume+'/'+row.issue
    }));
    relatedTotal.value = res.total;
  } catch (error) {
    console.error('获取挂接篇级数据失败:', error);
  }
}


// 页面加载时初始化数据
onMounted(() => {
  queryParams.value.articleId = articleId.value;
  relatedQueryParams.value.articleId = articleId.value;
  
  getArticleDetail();
  getRelatedArticles();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.module-container {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.module-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.collapse-btn {
  color: #409EFF;
  font-size: 14px;
}

.module-content {
  padding: 20px;
}

.module-collapsed {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.info-row {
  display: flex;
  line-height: 20px;
  font-size: 14px;
}

.info-label {
  color: #606266;
  font-weight: 500;
  min-width: 150px;
  text-align: right;
  padding-right: 15px;
}

.info-value {
  color: #303133;
  flex: 1;
}

.abstract-row {
  align-items: flex-start;
}

.abstract-row .info-value {
  line-height: 1.6;
}

.detail-tabs {
  width: 100%;
}

.volume-table-container {
  margin-top: 10px;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__item) {
  height: 40px;
  line-height: 40px;
  padding: 0 20px;
}

.placeholder {
  padding: 50px 0;
  text-align: center;
  color: #909399;
  font-size: 14px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.tab-header {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.tab-header span {
  padding: 8px 16px;
  cursor: pointer;
  color: #606266;
  font-size: 14px;
}

.tab-header span.active-tab {
  color: #409EFF;
  border-bottom: 2px solid #409EFF;
}

.xml-content {
  background-color: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 15px;
  height: 400px;
  overflow: auto;
}

.xml-code {
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  margin: 0;
  white-space: pre-wrap;
}

.xml-code .tag {
  color: #0000ff;
}

.xml-code .attr {
  color: #ff0000;
}

.xml-code .string {
  color: #008000;
}
</style>