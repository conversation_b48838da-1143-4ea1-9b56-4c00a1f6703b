<template>
  <div class="app-container">
    <!-- 背景框框 -->
    <viewBody title="篇级管理">
      <template #content>
        <!-- 使用FormSearch组件替换搜索菜单 -->
        <FormSearch
            ref="formSearchRef"
            v-model="queryParams"
            :formItems="formItems"
            @search="handleQuery"
            @reset="resetQuery"
        />

        <!-- 使用TableForm组件替换原有表格 -->
        <TableForm
            ref="tableFormRef"
            :columns="columns"
            :tableData="dataSourceList"
            :total="total"
            v-loading="loading"
            :showIndex="true"
            :showOtherColumn="true"
            tableotherColumnLabel="操作"
            :isShowCount="true"
            :isShowSearchQuery="false"
            @cellClick="handleRowClick"
        >
          <!-- 操作按钮插槽 -->
          <template #otherOperation="{ row }">
              <span class="operation-btn" @click.stop="handleDetail(row)">
                   详情
                </span>
          </template>

          <!-- 分页插槽 -->
          <template #pagination>
            <MyPagination
                v-if="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
          </template>

          <!-- 抽屉插槽 -->
          <template #drawer>
            <MyDrawer ref="drawerRef">
              <template #header="{ titleId, titleClass }">
                <div :id="titleId" :class="titleClass" class="my_drawer_title">
                  <span class="active">{{ title }}</span>
                  <div class="my_drawer_title_right">
                    <span class="btn_cancel" @click="cancel">取消</span>
                  </div>
                </div>
              </template>
              <template #form>
              <div class="drawer-body">
                <el-tabs v-model="activeTab">
                  <el-tab-pane label="列表展示" name="col">
                    <div class="journal-info">
                      <div v-for="item in drawerForm" :key="key" class="info-item">
                        <span class="label">{{ item.label }}:</span>
                        <span class="value">{{ articleForm[item.key] }}</span>
                      </div>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="XML展示" name="xml">
                    <div class="xml-content">
                      <pre class="xml-code">{{ xmlContent }}</pre>
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
              </template>
            </MyDrawer>
          </template>
          <!-- 添加底部表格插槽 -->
          <template #footerTable>
            <div v-if="showRelatedData" class="footer-table" style="height: 50%;">
              <TableForm
                  ref="relateTableFormRef"
                  :columns="relatedColumns"
                  :tableData="relatedArticles"
                  :total="relatedTotal"
                  v-loading="loading"
                  :showIndex="true"
                  :showOtherColumn="false"
                  :isShowCount="true"
                  :isShowSearchQuery="false"
                  :columnsRightWidth="'180'"
              >
                <template #pagination1>
                  <MyPagination
                      :total="relatedTotal"
                      :page="relatedQueryParams.pageNum"
                      :limit="relatedQueryParams.pageSize"
                      @pagination="getRelatedArticles"
                  />
                </template>
              </TableForm>
            </div>
          </template>
        </TableForm>
      </template>
    </viewBody>
  </div>
</template>


<script setup name="storageService">
import { listSingleArticles, getSingleArticleInfo, listRelatedSingleArticles, getXmlContentByUrl } from "@/api/storageService/storageService";
import { useRouter } from "vue-router";
import viewBody from '@/components/view/viewBody.vue';
import TableForm from '@/views/source/components/TableForm.vue';
import MyPagination from "@/components/Pagination/new.vue";
import FormSearch from '@/views/source/components/FormSearch.vue';
import {ref, computed, nextTick} from 'vue';
import MyDrawer from "@/views/collection/task/components/MyDrawer.vue";

const router = useRouter();
const { proxy } = getCurrentInstance();

const loading = ref(true);
const drawerRef = ref(null);
const total = ref(0);
const activeTab = ref('col');
const showRelatedData = ref(false);
const tableFormRef = ref(null);
const formSearchRef = ref(null);

// 搜索表单配置
const formItems = ref([
  {
    label: '',
    prop: 'singleArticleId',
    component: 'el-input',
    props: {
      placeholder: '请输入篇级ID',
      clearable: true,
      style: {width: '200px'},
    }
  },
  {
    label: '',
    prop: 'article_title',
    component: 'el-input',
    props: {
      placeholder: '请输入篇级题名',
      clearable: true,
      style: {width: '200px'},
    }
  },
  {
    label: '',
    prop: 'issn',
    component: 'el-input',
    props: {
      placeholder: '请输入ISSN',
      clearable: true,
      style: {width: '200px'},
    }
  },
  {
    label: '',
    prop: 'state',
    component: 'el-select',
    props: {
      placeholder: '数据状态',
      clearable: true,
      style: {width: '200px'},
    },
    children: () => [
      { value: '1', label: '正常' },
      { value: '0', label: '回退' }
    ]
  }
]);

// 主表格列配置
const columns = ref([
  {prop: 'singleArticleId', label: '篇级ID', width: '120'},
  {prop: 'article_title', label: '篇级题名'},
  {prop: 'journal_title', label: '期刊题名'},
  {prop: 'issn', label: 'ISSN', width: '120'},
  {prop: 'interval', label: '年/卷/期', width: '120'},
  {prop: 'originalNum', label: '原始数据条数', width: '120'},
  {prop: 'state', label: '数据状态', width: '100'},
  {prop: 'update_time', label: '更新时间', width: '180'}
]);

// 关联表格列配置
const relatedColumns = ref([
  {prop: 'originalArticleId', label: '原始篇级ID', width: '120'},
  {prop: 'batchId', label: '批次号', width: '100'},
  {prop: 'article_title', label: '篇级题名'},
  {prop: 'journal_title', label: '期刊题名'},
  {prop: 'issn', label: 'ISSN', width: '100'},
  {prop: 'interval', label: '年/卷/期', width: '100'},
  {prop: 'updateTime', label: '更新时间', width: '180'}
]);

const drawerForm = ref([
  {key: 'singleJournalId', label: 'Source Id'},
  {key: 'journal_title', label: 'Source Title'},
  {key: 'issn', label: 'ISSN'},
  {key: 'publisher', label: 'Publisher Name'},
  {key: 'pub_year', label: 'Pub Year'},
  {key: 'volume', label: 'Volume'},
  {key: 'issue', label: 'Issue'},
  {key: 'singleArticleId', label: 'Article Id'},
  {key: 'article_title', label: 'Article Title'},
  {key: 'fpage', label: 'FirstPage'},
  {key: 'lpage', label: 'LastPage'},
  {key: 'authors', label: 'Author-FullName'},
  {key: 'abstract', label: 'Abstract'},
  {key: 'keywords', label: 'Keyword'}
]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 20,
  articleId: null,
  articleTitle: null,
  state: null,
  issn: null
});


/** 搜索按钮操作 */
function handleQuery(formData) {
  if (formData) {
    Object.assign(queryParams.value, formData, { pageNum: 1 });
  } else {
    queryParams.value.pageNum = 1;
  }
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 20,
    articleId: null,
    articleTitle: null,
    state: null,
    issn: null
  };
  getList();
}

const dataSourceList = ref([]);
const scrollId = ref('');
const isDeepPaging = ref(false);


const articleForm = ref({});

const xmlContent = ref('');

const relatedArticles = ref([]);

const relatedQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  articleId: ''
});

const relatedTotal = ref(0);

/** 查询篇级列表 */
function getList() {
  loading.value = true;
  const params = {
    ...queryParams.value,
    scrollId: scrollId.value
  };

  const api = isDeepPaging.value ? '/storageService/singleJournal/scroll' : '/storageService/singleJournal/list';

  listSingleArticles(api, params).then(response => {
    if (response.rows && response.rows.length > 0) {
      if (isDeepPaging.value && response.rows[0].scrollId) {
        scrollId.value = response.rows[0].scrollId;
      }

      dataSourceList.value = response.rows.map(row => ({
        ...row,
        // 处理时间格式
        interval: row.pub_year+'/'+row.volume+'/'+row.issue,
        update_time: formatDateTime(row.update_time),
        state: row.state==1?'正常':'回退'
      }));

      total.value = response.total;
    } else {
      scrollId.value = '';
      isDeepPaging.value = false;
    }
    loading.value = false;
  });
}
// 格式化日期时间
function formatDateTime(dateTime) {
  if (!dateTime) return '';

  // 处理字符串类型的日期时间
  if (typeof dateTime === 'string') {
    // 替换T为空格，移除毫秒部分和时区信息
    return dateTime.replace('T', ' ').replace(/\.\d+(\+|\-).*$/, '');
  }

  return dateTime;
}
/**
 * 点击详情按钮，跳转到详情页面
 * @param {Object} row 行数据
 */
function handleDetail(row) {
  router.push({
    path: '/storageService/single/article/detail',
    query: {
      articleId: row.singleArticleId
    }
  });
}

function handleRowClick(row) {
  nextTick(() => {
    try {
      // 先打开抽屉
      drawerRef.value.openDrawer();
      // 打开后再获取数据
      setTimeout(() => {
        if (row) {
          relatedQueryParams.value.singleArticleId = row.singleArticleId;
          getRelatedArticles();
          showRelatedData.value = true;
          activeTab.value = 'col';

          getSingleArticleInfo(row.singleArticleId).then(response => {
            articleForm.value = response.data;
            // 获取xml内容
            if (response.data && response.data.xmlUri) {
              getXmlContentByUrl(response.data.xmlUri).then(res => {
                xmlContent.value = res.msg;
              }).catch(() => {
                xmlContent.value = 'XML内容获取失败';
              });
            } else {
              xmlContent.value = '无XML地址';
            }
          });
        }
      }, 100);
    } catch (error) {
      console.error('打开抽屉失败:', error);
    }
  });
}

/** 取消按钮 */
function cancel() {
  drawerRef.value.closeDrawer();
  resetDrawerForm();
  showRelatedData.value = false;
}

/** 重置抽屉表单 */
function resetDrawerForm() {
  articleForm.value = {};
  activeTab.value = 'col';
  relatedArticles.value = [];
  relatedTotal.value = 0;
}

/** 监听分页变化 */
function handlePageChange(val) {
  if (val > 100 && !isDeepPaging.value) {
    isDeepPaging.value = true;
    scrollId.value = '';
  }
  queryParams.value.pageNum = val;
  getList();
}

/** 监听每页显示数量变化 */
function handleSizeChange(val) {
  queryParams.value.pageSize = val;
  getList();
}

/** 获取挂接篇级数据 */
function getRelatedArticles() {
  listRelatedSingleArticles(relatedQueryParams.value.singleArticleId).then(response => {
    relatedArticles.value = response.rows.map( row => ({
      ...row,
      interval: row.pub_year+'/'+row.volume+'/'+row.issue
    }));
    relatedTotal.value = response.total;
  });
}

/** 初始加载 */
getList();
</script>
<style lang="scss" scoped>
.footer-table {
  padding: 0;
}
:deep .table-container{
  height:calc(100vh - 64px - 37px)
}
.my_drawer_title {
  display: flex;
  justify-content: space-between;
}
:deep .drawer-content-new{
  height: calc(100vh - 54px - 37px);
  margin-top: 0;
  z-index: 1;
}
.my_drawer_title_right {
  & > span {
    border-radius: 2px;
    background-color: rgb(189, 189, 189);
    font-size: 14px;
    font-family: "MicrosoftYaHei";
    color: rgb(255, 255, 255);
    padding: 5px 12px;
    margin: 0 5px;
  }

  .btn_add {
    background-color: rgb(0, 120, 212);
  }
}
.form-query-box {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin: 6px 10px;
}
.form-query-box1 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin: 6px 10px;
}
.form-query-box .my_primary_btn {
  background-color: #0078d4;
  border-color: #0078d4;
}
:deep(.tableFormContent .table-container) {
  border: none;
  padding: 0;
  height: calc(100% - 1px) !important;
}

.other-bottom-table {
  height: 100%;
}
:deep(.view-body-content){
  height: 100%;
  display: flex;
  flex-direction: column;

  .container {
    flex: 1;
    height: calc(100% - 44px);
  }
}
.view-body-top-right-btns {
  font-size: 14px;
  font-family: "MicrosoftYaHei";
  color: #0078d4;
  margin-left: 30px;
}
.journal-info {
  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 14px;

    .label {
      text-align: right;
      font-weight: bolder;
      color: #606266;
      min-width: 120px;
      margin-right: 10px;
    }

    .value {
      color: #303133;
      flex: 1;
      word-break: break-all;
    }
  }
}

.xml-content {
  background-color: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 15px;
  height: 100%;
  overflow: auto;
}

.xml-code {
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  margin: 0;
  white-space: pre-wrap;
}
</style>

