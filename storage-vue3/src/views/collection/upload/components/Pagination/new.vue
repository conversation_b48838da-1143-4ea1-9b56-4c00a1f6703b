<template>
  <div :class="{ hidden: hidden }" class="pagination-container">
    <span class="font">每页</span>
    <el-select
      v-model="pageSize"
      @change="handleSizeChange"
      size="small"
      class="page-size-selector"
      style="margin: 0 10px"
    >
      <el-option
        v-for="size in pageSizes"
        :key="size"
        :label="size"
        :value="size"
      />
    </el-select>
    <span class="font">条</span>
    <el-pagination
      size="small"
      :background="background"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :layout="layout"
      :page-sizes="pageSizes"
      :pager-count="pagerCount"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <span class="font">跳至</span>
    <el-input-number
      v-model="jumpPage"
      :controls="false"
      :min="1"
      :max="Math.ceil(total / pageSize)"
      size="small"
      class="jump-input"
      @keyup.enter.native="handleJump"
      @change="validateJump"
      style="margin: 0 10px"
    />
    <span class="font">页</span>
  </div>
</template>

<script setup>
import { scrollTo } from "@/utils/scroll-to";

const props = defineProps({
  total: {
    required: true,
    type: Number,
  },
  page: {
    type: Number,
    default: 1,
  },
  limit: {
    type: Number,
    default: 20,
  },
  pageSizes: {
    type: Array,
    default() {
      return [10, 20, 30, 50];
    },
  },
  // 移动端页码按钮的数量端默认值5
  pagerCount: {
    type: Number,
    default: document.body.clientWidth < 992 ? 5 : 7,
  },
  layout: {
    type: String,
    default: "prev, pager, next",
  },
  background: {
    type: Boolean,
    default: true,
  },
  autoScroll: {
    type: Boolean,
    default: true,
  },
  hidden: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits();
const currentPage = computed({
  get() {
    return props.page;
  },
  set(val) {
    emit("update:page", val);
  },
});
const pageSize = computed({
  get() {
    return props.limit;
  },
  set(val) {
    emit("update:limit", val);
  },
});
function handleSizeChange(val) {
  if (currentPage.value * val > props.total) {
    currentPage.value = 1;
  }
  emit("pagination", { page: currentPage.value, limit: val });
  if (props.autoScroll) {
    scrollTo(0, 800);
  }
}
function handleCurrentChange(val) {
  emit("pagination", { page: val, limit: pageSize.value });
  if (props.autoScroll) {
    scrollTo(0, 800);
  }
}

const jumpPage = ref(1);
function handleJump() {
  if (
    jumpPage.value >= 1 &&
    jumpPage.value <= Math.ceil(props.total / pageSize.value)
  ) {
    currentPage.value = jumpPage.value;
    emit("pagination", { page: jumpPage.value, limit: pageSize.value });
    if (props.autoScroll) {
        scrollTo(0, 800);
    }
    console.log(`跳转到页码: ${jumpPage.value}`);
  } else {
    // this.$message.warning("请输入有效的页码");
  }
}
function validateJump() {
  // 校验输入的页码范围
  if (
    jumpPage.value < 1 ||
    jumpPage.value > Math.ceil(props.total / pageSize.value)
  ) {
    // this.$message.warning("请输入有效的页码");
  }
}
</script>

<style scoped lang="scss">
.pagination-container {
  background: #fff;
  padding: 10px !important;
}
.pagination-container.hidden {
  display: none;
}
.page-size-selector {
  width: 50px;
  margin: 0 !important;
}
.font {
  font-size: 12px;
  display: flex;
  align-items: center;
  color: #606266;
  margin: 0 6px;
}
:deep(.el-pagination){
  margin-top: 0px !important;
}
:deep(.el-pagination.is-background .btn-next),
:deep(.el-pagination.is-background .btn-prev),
:deep(.el-pagination.is-background .el-pager li) {
  background-color: #fff;
  border: 1px solid #dadada;
}

:deep(.el-pagination.is-background .btn-next.is-active),
:deep(.el-pagination.is-background .btn-prev.is-active),
:deep(.el-pagination.is-background .el-pager li.is-active) {
  background-color: #0077d3;
  border-color: #0077d3;
  color: #fff;
}
:deep(.page-size-selector) {
    width: 50px !important;  
    margin: 15px 0  !important;
    padding: 0 !important;
}
:deep(.el-select__wrapper) {
    width: 50px;  
}
/* el-select el-select--small page-size-selector */
:deep(.el-select--small) {
    width: 50px !important;  
}
.el-input-number--small {
    width: 35px;
    margin: 0 !important;
}
:deep(.el-input-number.is-without-controls .el-input__wrapper) {
    width: 35px;
    margin: 15px 0;
    padding: 0 !important;
}

</style>
