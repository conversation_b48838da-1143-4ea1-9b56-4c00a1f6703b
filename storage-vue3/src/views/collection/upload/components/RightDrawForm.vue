<template>
  <div class="form-container">
    <el-form
        ref="parsingMappingRef" 
        :rules="rules" 
        :model="form" 
        label-width="120px"
        size="small"
        label-position="left"
    >
      <el-form-item label="规则名称" prop="name">
        <template #label>
         规则名称<em>*</em>：
        </template>
        <el-input v-model="form.name" placeholder="规则名称"/>
        <template #error><em/></template>
      </el-form-item>

      <el-form-item label="数据源" prop="sourceId">
        <template #label>
          数据源<em>*</em>：
        </template>
         <el-select clearable filterable :disabled="modal.isEdit" v-model="form.sourceId" placeholder="">
            <el-option
                v-for="dict in bizDict.dataOriginOptions"
                :key="dict.value"
                :label="dict.text"
                :value="dict.value"
            ></el-option>
          </el-select>
        <template #error><em/></template>
      </el-form-item>

      <!-- 数据类型 -->
      <el-form-item label="数据类型" prop="docType">
        <template #label>
          数据类型<em>*</em>：
        </template>
        <el-select clearable filterable v-model="form.docType" placeholder="">
          <el-option
              v-for="dict in sysDict.storage_data_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          ></el-option>
        </el-select>
        <template #error><em/></template>
      </el-form-item>

      <el-form-item label="应用状态" prop="useStatus">
        <template #label>
          应用状态<em>*</em>：
        </template>
        <el-select filterable :disabled="true" v-model="form.useStatus" placeholder="">
          <el-option
              v-for="dict in sysDict.storage_use_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          ></el-option>
        </el-select>
        <template #error><em/></template>
      </el-form-item>

      <el-form-item label="规则描述" prop="description">
        <template #label>
          规则描述<em>*</em>：
        </template>
        <el-input v-model="form.description" type="textarea" placeholder="规则描述"></el-input>
      </el-form-item>
      
    </el-form>

    <div class="middle-box">
      <div class="middle-box-left"></div>
      <div class="middle-box-none"></div>
    </div>
  </div>
</template>

<script setup>
import {reactive, ref, computed, getCurrentInstance, nextTick} from "vue";
import {testConnectionApi} from "@/api/dataSource/dataSource";

const {proxy} = getCurrentInstance();

const props = defineProps({
  modal: {
    type: Object,
  }
});
/**字典*/
const {sysDict, bizDict} = props.modal.dict
const form = computed({
  get() {
    return props.modal.form;
  },
  set() {
  }
});

// 需要进行数组转换的字段
const arrayField = ref(['processFlow', 'analysisRuleId']);

const ruleFormRef = ref();

const testConnectionFlag = ref(null)

// 动态标签名称
const ftpLabels = computed(() => {
  if (formData.harvestType === '2') {
    // FTP
    return {
      address: 'FTP地址',
      port: 'FTP端口',
      username: 'FTP用户名',
      password: 'FTP密码'
    };
  } else if (formData.harvestType === '3') {
    // SFTP
    return {
      address: 'SFTP地址',
      port: 'SFTP端口',
      username: 'SFTP用户名',
      password: 'SFTP密码'
    };
  } else {
    // 默认
    return {
      address: '地址',
      port: '端口',
      username: '用户名',
      password: '密码'
    };
  }
});

// 映射规则标签显示
const analysisRuleLabels = computed(() => {
  if (!formData.analysisRuleId || !bizDict.ruleBaseSelectOptions?.length) {
    return [];
  }

  // 如果是逗号分隔的字符串，转换为数组
  let ruleIds = [];
  if (typeof formData.analysisRuleId === 'string') {
    if (formData.analysisRuleId.includes(',')) {
      ruleIds = formData.analysisRuleId.split(',').filter(id => id.trim() !== '');
    } else {
      ruleIds = [formData.analysisRuleId];
    }
  } else if (Array.isArray(formData.analysisRuleId)) {
    ruleIds = formData.analysisRuleId;
  } else {
    ruleIds = [formData.analysisRuleId];
  }

  // 根据ID查找对应的标签
  const labels = ruleIds.map(id => {
    const rule = bizDict.ruleBaseSelectOptions.find(option => option.value === id.trim());
    return rule ? (rule.text || rule.label || rule.name) : id.trim();
  }).filter(label => label);

  console.log('映射规则ID:', formData.analysisRuleId, '-> 标签:', labels);
  return labels;
});

// 表单数据
// const formData = reactive({
//   name: '',
//   dataId: '',
//   pubType: '',
//   dataLevel: '',
//   dataType: '',
//   processFlow: [],
//   harvestType: '',
//   ftpUrl: '',
//   ftpPort: '',
//   ftpUser: '',
//   ftpPwd: '',
//   fileType: '',
//   ifaceUrl: '',
//   analysisRuleId: '',
//   sourceType: '',
//   describes: ''
// });
const formData = reactive({
  name: '',
  sourceId: '',
  docType: '',
  useStatus: '',
  description: ''

});
const rules = reactive({
  name: [
    {required: true, message: "规则名称不能为空", trigger: "change"}
  ],
  sourceId: [
    {required: true, message: "数据源不能为空", trigger: "change"}
  ],
  docType: [
    {required: true, message: "数据类型不能为空", trigger: "change"}
  ],
  useStatus: [
    {required: true, message: "应用状态不能为空", trigger: "change"}
  ]
})


// 测试连接
const testConnection = async (type) => {
  console.log('测试连接', formData);
  const message = type == '2' ? 'FTP' : 'SFTP';
  const {data} = await testConnectionApi(formData);
  console.log('测试连接结果', data);
  if (data) {
    proxy.$modal.msgSuccess(message + "测试连接成功");
  } else {
    proxy.$modal.msgError(message + "测试连接失败");
  }
};

// 处理数组字段转换为字符串（保存时使用）
const processArrayToString = (data) => {
  const processedData = {...data};
  if (arrayField.value) {
    arrayField.value.forEach(field => {
      if (processedData[field] && Array.isArray(processedData[field])) {
        console.log(`转换数组字段 ${field}:`, processedData[field], '-> ', processedData[field].join(','));
        // 过滤空值并转换为字符串
        processedData[field] = processedData[field]
            .filter(item => item && item.trim() !== '')
            .join(',');
      }
    });
  }
  return processedData;
};

// 处理字符串转换为数组（编辑时使用）
const processStringToArray = (data, readonly = false) => {
  if (arrayField.value && !readonly) {
    arrayField.value.forEach(field => {
      if (data[field] && typeof data[field] === 'string') {
        console.log(`转换字符串字段 ${field}:`, data[field], '-> ', data[field].split(','));
        // 处理空字符串的情况
        if (data[field].trim() === '') {
          data[field] = [];
        } else {
          data[field] = data[field].split(',').filter(item => item.trim() !== '');
        }
      }
    });
  }
};

// 获取表单数据
const getFormData = () => {
  // 返回处理后的数据（数组转为字符串）
  return processArrayToString(formData);
};

// 重置表单
const resetForm = () => {
  console.log('=== 重置表单开始 ===');
  console.log('重置前 ftpUser:', formData.ftpUser, 'ftpPwd:', formData.ftpPwd);

  // 先清除表单验证状态
  ruleFormRef.value?.clearValidate();

  // 重置表单数据为初始状态
  Object.assign(formData, {
    name: '',
    dataId: '',
    pubType: '',
    dataLevel: '',
    dataType: '',
    processFlow: [],
    harvestType: '',
    ftpUrl: '',
    ftpPort: '',
    ftpUser: '',
    ftpPwd: '',
    fileType: '',
    ifaceUrl: '',
    analysisRuleId: '',
    sourceType: '',
    describes: ''
  });

  console.log('重置后 ftpUser:', formData.ftpUser, 'ftpPwd:', formData.ftpPwd);

  // 强制清空表单字段值，确保输入框显示为空
  setTimeout(() => {
    ruleFormRef.value?.resetFields();

    // 额外确保用户名和密码字段为空
    formData.ftpUser = '';
    formData.ftpPwd = '';

    console.log('最终检查 ftpUser:', formData.ftpUser, 'ftpPwd:', formData.ftpPwd);
    console.log('=== 重置表单完成 ===');
  }, 100);
};

// 设置表单数据
const setFormData = (data) => {
  console.log('=== 设置表单数据 ===');
  console.log('传入数据:', data);

  // 深拷贝数据，避免修改原始对象
  const processedData = JSON.parse(JSON.stringify(data || {}));

  // 处理字符串转数组
  processStringToArray(processedData);

  console.log('处理后数据:', processedData);
  console.log('设置前 ftpUser:', formData.ftpUser, 'ftpPwd:', formData.ftpPwd);

  // 设置到表单数据
  Object.assign(formData, processedData);

  console.log('设置后 ftpUser:', formData.ftpUser, 'ftpPwd:', formData.ftpPwd);
  console.log('=== 设置表单数据完成 ===');
};

// 表单验证
const validateForm = () => {
  return new Promise((resolve, reject) => {
    ruleFormRef.value.validate((valid) => {
      if (valid) {
        resolve(true);
      } else {
        reject(false);
      }
    });
  });
};

// 专门用于新增时的完全重置
const resetForAdd = () => {
  console.log('=== 新增专用重置开始 ===');

  // 先清除表单验证状态
  ruleFormRef.value?.clearValidate();

  // 完全重置表单数据
  Object.keys(formData).forEach(key => {
    if (key === 'processFlow') {
      formData[key] = [];
    } else {
      formData[key] = '';
    }
  });

  // 强制更新响应式数据
  nextTick(() => {
    ruleFormRef.value?.resetFields();

    // 再次确保关键字段为空
    formData.ftpUser = '';
    formData.ftpPwd = '';
    formData.ftpUrl = '';
    formData.ftpPort = '';

    console.log('新增重置完成，所有字段:', JSON.stringify(formData, null, 2));
    console.log('=== 新增专用重置完成 ===');
  });
};

// 暴露方法给父组件
defineExpose({
  getFormData,
  resetForm,
  resetForAdd,  // 新增专用重置方法
  setFormData,
  validateForm
});
</script>

<style lang="scss" scoped>
.form-container {
  border-radius: 2px;
  border: 1px solid rgb(221, 229, 235);
  background-color: rgb(255, 255, 255);
  overflow: hidden;

  :deep(.el-form-item--small) {
    margin-bottom: 0;
  }

  :deep(.el-input) {
    width: 335px;
    height: 34px;
  }
  :deep(.el-select) {
    width: 335px;
    height: 34px;
  }
  :deep(.el-input__wrapper) {
    height: 32px;
  }

  :deep(.el-select--small .el-select__wrapper) {
    height: 32px;
  }

  :deep(.el-form-item__content) {
    padding-top: 6px !important;
    padding-left: 9px !important;
    padding-right: 6px !important;
    background-color: transparent;
  }

  // 确保标签区域背景色连续
  :deep(.el-form-item) {
    display: flex;

    .el-form-item__label {
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 0, 0);
      font-weight: bold;
      padding-left: 15px !important;
      padding-top: 6px !important;
      padding-right: 0 !important;
      height: 40px;
      line-height: 40px;
      background-color: #f7f7f7;
      width: 110px;
      flex-shrink: 0;

      em {
        font-style: normal;
        color: #eb3037;
      }
    }
  }

  // 确保middle-box不受表单样式影响
  .middle-box {
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
  }

  :deep(.el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label-wrap>.el-form-item__label:before),
  :deep(.el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label:before) {
    display: none;
  }

  // textarea项的特殊样式
  :deep(.textarea-item) {
    .el-form-item__label {
      height: 196px !important;
      align-items: flex-start;
      padding-top: 6px !important;
    }
  }
}

.middle-box {
  display: flex;
  flex-direction: row;

  .middle-box-left {
    width: 110px;
    background-color: #f7f7f7 !important;
    flex-shrink: 0;
  }

  .middle-box-none {
    height: 20px;
    flex: 1;
  }

  .middle-box-right {
    height: 80px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    span {
      border: 1px solid rgb(219, 219, 219);
      border-radius: 2px;
      background-color: rgb(255, 255, 255);
      font-size: 14px;
      font-family: "MicrosoftYaHei";
      color: rgb(0, 120, 212);
      padding: 3px 11px;
      cursor: pointer;
    }
  }
}

// 映射规则标签样式
.rule-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
  min-height: 32px;
  padding: 2px 0;

  .rule-tag {
    margin: 0;
    font-size: 12px;
    border-radius: 3px;
    background-color: #e8f4fd;
    border: 1px solid #b3d9f7;
    color: #1890ff;
    padding: 2px 8px;
    height: auto;
    line-height: 1.4;

    :deep(.el-tag__content) {
      line-height: 1.4;
    }

    &:hover {
      background-color: #d6f0ff;
    }
  }

  .no-data-text {
    color: #999;
    font-size: 13px;
    font-style: italic;
    padding: 6px 0;
  }
}
</style>
